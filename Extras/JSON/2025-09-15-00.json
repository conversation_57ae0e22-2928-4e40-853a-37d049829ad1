{"receive_time":"2025-09-15T00:00:00.369Z","event_id":"1904034406888837121","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*********","sw.remote.ip":"*********","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T00:00:00.001208+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757894400001,"timestamp":"2025-09-15T00:00:00.001208+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":64555014,"host.name":"dev-careportal","time":1757894400001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:00:00.372Z","event_id":"1904034406901063686","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T00:00:00.001573+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757894400001,"timestamp":"2025-09-15T00:00:00.001573+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":839979168,"host.name":"dev-careportal","time":1757894400001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:00:00.695Z","event_id":"1904034408256090114","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T00:00:00.317218+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757894400317,"timestamp":"2025-09-15T00:00:00.317218+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":598550846,"host.name":"dev-careportal","time":1757894400317,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:00:00.776Z","event_id":"1904034408594231296","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T00:00:00.394703+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757894400394,"timestamp":"2025-09-15T00:00:00.394703+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":919368764,"host.name":"dev-careportal","time":1757894400394,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:00:49.184Z","event_id":"1904034611633401856","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:00:49.1218+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=bd4124c1-4e04-a5cc-83c8-f26085743c66 fwd=\"************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757894449121,"timestamp":"2025-09-15T00:00:49.1218+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"bd4124c1-4e04-a5cc-83c8-f26085743c66","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":873925035,"host.name":"dev-careportal","time":1757894449121,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:01:49.252Z","event_id":"1904034863575183363","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:01:49.188264+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=feb4faa0-648f-3aa1-4801-0c33b60a77c5 fwd=\"**************\" dyno=web.1 connect=0ms service=5ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757894509188,"timestamp":"2025-09-15T00:01:49.188264+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"feb4faa0-648f-3aa1-4801-0c33b60a77c5","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"5ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":5},"syslog_priority":134,"sender_ip":915624821,"host.name":"dev-careportal","time":1757894509188,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:02:49.169Z","event_id":"1904035114886995968","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:02:49.109234+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=4378b6e2-1bc6-abec-cec3-2adb742b25e9 fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757894569109,"timestamp":"2025-09-15T00:02:49.109234+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"4378b6e2-1bc6-abec-cec3-2adb742b25e9","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":915560822,"host.name":"dev-careportal","time":1757894569109,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:03:49.191Z","event_id":"1904035366637682701","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:03:49.132336+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=fd46cbb8-0e3f-bf8c-37c1-88aeeffc4b16 fwd=\"************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757894629132,"timestamp":"2025-09-15T00:03:49.132336+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"fd46cbb8-0e3f-bf8c-37c1-88aeeffc4b16","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":916300108,"host.name":"dev-careportal","time":1757894629132,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:04:49.268Z","event_id":"1904035618618699778","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:04:49.20673+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=c1b715cc-3cf3-2485-65da-d9fcd31b91c4 fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757894689206,"timestamp":"2025-09-15T00:04:49.20673+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"c1b715cc-3cf3-2485-65da-d9fcd31b91c4","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":583168653,"host.name":"dev-careportal","time":1757894689206,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:05:00.368Z","event_id":"1904035665173245952","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T00:05:00.000974+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757894700000,"timestamp":"2025-09-15T00:05:00.000974+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":876024762,"host.name":"dev-careportal","time":1757894700000,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:05:00.372Z","event_id":"1904035665191489536","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T00:05:00.001057+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757894700001,"timestamp":"2025-09-15T00:05:00.001057+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":583485309,"host.name":"dev-careportal","time":1757894700001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:05:00.653Z","event_id":"1904035666370744321","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T00:05:00.277599+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757894700277,"timestamp":"2025-09-15T00:05:00.277599+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":916838822,"host.name":"dev-careportal","time":1757894700277,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:05:00.654Z","event_id":"1904035666375028748","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T00:05:00.292904+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757894700292,"timestamp":"2025-09-15T00:05:00.292904+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":839979168,"host.name":"dev-careportal","time":1757894700292,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:05:49.136Z","event_id":"1904035869723873281","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:05:49.074945+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=c7f8db9d-e80a-1917-493b-e992648b9ab0 fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757894749074,"timestamp":"2025-09-15T00:05:49.074945+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"c7f8db9d-e80a-1917-493b-e992648b9ab0","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":840027160,"host.name":"dev-careportal","time":1757894749074,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:06:49.170Z","event_id":"1904036121523748864","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:06:49.107432+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=87b2f9aa-6aec-cf36-8165-75c91cafe317 fwd=\"************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757894809107,"timestamp":"2025-09-15T00:06:49.107432+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"87b2f9aa-6aec-cf36-8165-75c91cafe317","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":64801173,"host.name":"dev-careportal","time":1757894809107,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:07:49.262Z","event_id":"1904036373568532480","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:07:49.199397+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=2db74243-c830-fe32-7357-44b4756260bf fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757894869199,"timestamp":"2025-09-15T00:07:49.199397+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"2db74243-c830-fe32-7357-44b4756260bf","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":915560822,"host.name":"dev-careportal","time":1757894869199,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:08:49.171Z","event_id":"1904036624844312576","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:08:49.108211+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=683f01a1-29db-2309-e373-9e888413d59a fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757894929108,"timestamp":"2025-09-15T00:08:49.108211+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"683f01a1-29db-2309-e373-9e888413d59a","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":583547411,"host.name":"dev-careportal","time":1757894929108,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:09:49.124Z","event_id":"1904036876305133569","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:09:49.063761+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=8c243056-f508-02af-26e2-ef39a22cac3a fwd=\"************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757894989063,"timestamp":"2025-09-15T00:09:49.063761+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"8c243056-f508-02af-26e2-ef39a22cac3a","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":387316936,"host.name":"dev-careportal","time":1757894989063,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:10:00.366Z","event_id":"1904036923457179648","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T00:10:00.001336+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757895000001,"timestamp":"2025-09-15T00:10:00.001336+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":1796681299,"host.name":"dev-careportal","time":1757895000001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:10:00.375Z","event_id":"1904036923494965255","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T00:10:00.001142+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757895000001,"timestamp":"2025-09-15T00:10:00.001142+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":912044996,"host.name":"dev-careportal","time":1757895000001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:10:00.375Z","event_id":"1904036923494965256","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T00:10:00.227659+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757895000227,"timestamp":"2025-09-15T00:10:00.227659+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":912044996,"host.name":"dev-careportal","time":1757895000227,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:10:00.643Z","event_id":"1904036924619345920","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T00:10:00.278805+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757895000278,"timestamp":"2025-09-15T00:10:00.278805+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":751251959,"host.name":"dev-careportal","time":1757895000278,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:10:49.260Z","event_id":"1904037128532897792","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:10:49.19994+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=270d930d-87cd-8a1a-4249-d1e09f975c1e fwd=\"**************\" dyno=web.2 connect=0ms service=3ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757895049199,"timestamp":"2025-09-15T00:10:49.19994+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"270d930d-87cd-8a1a-4249-d1e09f975c1e","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"3ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":3},"syslog_priority":134,"sender_ip":916296964,"host.name":"dev-careportal","time":1757895049199,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:11:49.119Z","event_id":"1904037379601620993","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:11:49.055933+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=0e829ec9-eb7b-096b-38f8-c993d45df24c fwd=\"**************\" dyno=web.1 connect=0ms service=3ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757895109055,"timestamp":"2025-09-15T00:11:49.055933+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"0e829ec9-eb7b-096b-38f8-c993d45df24c","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"3ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":3},"syslog_priority":134,"sender_ip":752413774,"host.name":"dev-careportal","time":1757895109055,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:12:49.164Z","event_id":"1904037631447306240","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:12:49.106919+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=c790fc6a-24e6-f938-b83f-d908156b8246 fwd=\"************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757895169106,"timestamp":"2025-09-15T00:12:49.106919+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"c790fc6a-24e6-f938-b83f-d908156b8246","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":583547411,"host.name":"dev-careportal","time":1757895169106,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:13:49.238Z","event_id":"1904037883414802436","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:13:49.178734+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=400a55cf-5d29-ffb0-01c5-3ca718c48f10 fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757895229178,"timestamp":"2025-09-15T00:13:49.178734+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"400a55cf-5d29-ffb0-01c5-3ca718c48f10","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":387259567,"host.name":"dev-careportal","time":1757895229178,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:14:49.164Z","event_id":"1904038134764785664","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:14:49.103359+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=48c08d3b-1f92-28c2-b945-916823fce9d6 fwd=\"**************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757895289103,"timestamp":"2025-09-15T00:14:49.103359+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"48c08d3b-1f92-28c2-b945-916823fce9d6","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":64548717,"host.name":"dev-careportal","time":1757895289103,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:15:00.365Z","event_id":"1904038181743063040","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T00:15:00.000416+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757895300000,"timestamp":"2025-09-15T00:15:00.000416+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":585653277,"host.name":"dev-careportal","time":1757895300000,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:15:00.389Z","event_id":"1904038181846233090","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T00:15:00.002836+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757895300002,"timestamp":"2025-09-15T00:15:00.002836+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":911595259,"host.name":"dev-careportal","time":1757895300002,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:15:00.639Z","event_id":"1904038182892744705","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T00:15:00.255703+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757895300255,"timestamp":"2025-09-15T00:15:00.255703+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":387352237,"host.name":"dev-careportal","time":1757895300255,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:15:00.685Z","event_id":"1904038183086399503","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T00:15:00.305455+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757895300305,"timestamp":"2025-09-15T00:15:00.305455+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":1679314106,"host.name":"dev-careportal","time":1757895300305,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:15:49.177Z","event_id":"1904038386476019715","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:15:49.115543+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=204aa6c3-087c-3d43-b5b6-ee8bcff4a83b fwd=\"************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757895349115,"timestamp":"2025-09-15T00:15:49.115543+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"204aa6c3-087c-3d43-b5b6-ee8bcff4a83b","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":915507155,"host.name":"dev-careportal","time":1757895349115,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:16:49.320Z","event_id":"1904038638733996032","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:16:49.256377+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=cbd642b0-786a-3725-d2e8-b0ba0fee2c02 fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757895409256,"timestamp":"2025-09-15T00:16:49.256377+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"cbd642b0-786a-3725-d2e8-b0ba0fee2c02","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":56552554,"host.name":"dev-careportal","time":1757895409256,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:17:43.580Z","event_id":"1904038866317549569","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:17:43.522675+00:00 host heroku router - ","logmsg":"at=error code=H15 desc=\"Idle connection\" method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=6e4b50fe-dddb-ae77-cda3-f8b42c0137f8 fwd=\"*************\" dyno=web.2 connect=0ms service=55001ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757895463522,"timestamp":"2025-09-15T00:17:43.522675+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"error","code":"H15","desc":"Idle connection","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"6e4b50fe-dddb-ae77-cda3-f8b42c0137f8","fwd":"*************","destinationDyno":"web.2","connect":"0ms","service":"55001ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":55001},"syslog_priority":134,"sender_ip":64563239,"host.name":"dev-careportal","time":1757895463522,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:17:43.893Z","event_id":"1904038867630329856","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T00:17:43.523228+00:00 host app web.2 - ","logmsg":"User Disconnected","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757895463523,"timestamp":"2025-09-15T00:17:43.523228+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":315845624,"host.name":"dev-careportal","time":1757895463523,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:17:49.184Z","event_id":"1904038889823109120","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:17:49.123035+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=e2872a3d-2aaa-421a-11e9-42ad30402ca2 fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757895469123,"timestamp":"2025-09-15T00:17:49.123035+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"e2872a3d-2aaa-421a-11e9-42ad30402ca2","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":872752520,"host.name":"dev-careportal","time":1757895469123,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:18:49.128Z","event_id":"1904039141246750720","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:18:49.070656+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=ba344caa-bd75-62d2-dbf8-9eec78efa28a fwd=\"************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757895529070,"timestamp":"2025-09-15T00:18:49.070656+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"ba344caa-bd75-62d2-dbf8-9eec78efa28a","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":2927750400,"host.name":"dev-careportal","time":1757895529070,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:19:49.244Z","event_id":"1904039393391247360","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:19:49.18584+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=c320d98a-5447-1d8a-0a48-ae89222bf478 fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757895589185,"timestamp":"2025-09-15T00:19:49.18584+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"c320d98a-5447-1d8a-0a48-ae89222bf478","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":583547411,"host.name":"dev-careportal","time":1757895589185,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:20:00.372Z","event_id":"1904039440066220040","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T00:20:00.000899+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757895600000,"timestamp":"2025-09-15T00:20:00.000899+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":840034151,"host.name":"dev-careportal","time":1757895600000,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:20:00.375Z","event_id":"1904039440078327808","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T00:20:00.000661+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757895600000,"timestamp":"2025-09-15T00:20:00.000661+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":885963040,"host.name":"dev-careportal","time":1757895600000,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:20:00.375Z","event_id":"1904039440078327809","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T00:20:00.208024+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757895600208,"timestamp":"2025-09-15T00:20:00.208024+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":885963040,"host.name":"dev-careportal","time":1757895600208,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:20:00.652Z","event_id":"1904039441240268800","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T00:20:00.287596+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757895600287,"timestamp":"2025-09-15T00:20:00.287596+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":315710684,"host.name":"dev-careportal","time":1757895600287,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:20:49.155Z","event_id":"1904039644676583456","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:20:49.089864+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=c739d14d-f0b8-e583-47ea-f41a4156631e fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757895649089,"timestamp":"2025-09-15T00:20:49.089864+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"c739d14d-f0b8-e583-47ea-f41a4156631e","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":839938714,"host.name":"dev-careportal","time":1757895649089,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:21:49.104Z","event_id":"1904039896120492032","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:21:49.04281+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=e165d92d-242d-8117-4e4a-23929a97959f fwd=\"************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757895709042,"timestamp":"2025-09-15T00:21:49.04281+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"e165d92d-242d-8117-4e4a-23929a97959f","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":918836381,"host.name":"dev-careportal","time":1757895709042,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:22:49.253Z","event_id":"1904040148404105216","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:22:49.194749+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=27374150-24b3-1b64-3334-fead42e31d11 fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757895769194,"timestamp":"2025-09-15T00:22:49.194749+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"27374150-24b3-1b64-3334-fead42e31d11","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":598629640,"host.name":"dev-careportal","time":1757895769194,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:23:49.126Z","event_id":"1904040399528030209","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:23:49.065322+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=2dde9864-55e9-a466-a244-d0c4ec700a40 fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757895829065,"timestamp":"2025-09-15T00:23:49.065322+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"2dde9864-55e9-a466-a244-d0c4ec700a40","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":583288920,"host.name":"dev-careportal","time":1757895829065,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:24:49.249Z","event_id":"1904040651701579777","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:24:49.187441+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=690a4805-7a08-a1a6-5f5f-ad5ba2a3f2a3 fwd=\"************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757895889187,"timestamp":"2025-09-15T00:24:49.187441+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"690a4805-7a08-a1a6-5f5f-ad5ba2a3f2a3","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":2927750400,"host.name":"dev-careportal","time":1757895889187,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:25:00.366Z","event_id":"1904040698332254209","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T00:25:00.00089+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757895900000,"timestamp":"2025-09-15T00:25:00.00089+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":872736671,"host.name":"dev-careportal","time":1757895900000,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:25:00.382Z","event_id":"1904040698399186944","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T00:25:00.002655+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757895900002,"timestamp":"2025-09-15T00:25:00.002655+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":65067227,"host.name":"dev-careportal","time":1757895900002,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:25:00.667Z","event_id":"1904040699594354688","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T00:25:00.294387+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757895900294,"timestamp":"2025-09-15T00:25:00.294387+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":840117564,"host.name":"dev-careportal","time":1757895900294,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:25:00.679Z","event_id":"1904040699644596224","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T00:25:00.312345+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757895900312,"timestamp":"2025-09-15T00:25:00.312345+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":583089298,"host.name":"dev-careportal","time":1757895900312,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:25:49.304Z","event_id":"1904040903591415808","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:25:49.24457+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=da138816-f73a-1a7a-b16b-7588a206cef3 fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757895949244,"timestamp":"2025-09-15T00:25:49.24457+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"da138816-f73a-1a7a-b16b-7588a206cef3","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":64563239,"host.name":"dev-careportal","time":1757895949244,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:26:49.141Z","event_id":"1904041154567884800","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:26:49.07805+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=8df185f0-e055-bac1-2ee1-87c9a9cc8fc7 fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757896009078,"timestamp":"2025-09-15T00:26:49.07805+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"8df185f0-e055-bac1-2ee1-87c9a9cc8fc7","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":387316936,"host.name":"dev-careportal","time":1757896009078,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:27:49.172Z","event_id":"1904041406354804737","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:27:49.11257+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=cf85d038-984d-5bb5-32b9-951cbf5ef0ba fwd=\"************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757896069112,"timestamp":"2025-09-15T00:27:49.11257+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"cf85d038-984d-5bb5-32b9-951cbf5ef0ba","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":872446568,"host.name":"dev-careportal","time":1757896069112,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:28:49.259Z","event_id":"1904041658377392128","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:28:49.202076+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=1c32c447-f0d5-e045-2057-55adbe198526 fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757896129202,"timestamp":"2025-09-15T00:28:49.202076+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"1c32c447-f0d5-e045-2057-55adbe198526","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":916485046,"host.name":"dev-careportal","time":1757896129202,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:29:49.139Z","event_id":"1904041909532872704","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:29:49.081324+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=2baa5582-0d02-1694-5035-d38db574e5c8 fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757896189081,"timestamp":"2025-09-15T00:29:49.081324+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"2baa5582-0d02-1694-5035-d38db574e5c8","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":840027160,"host.name":"dev-careportal","time":1757896189081,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:30:00.368Z","event_id":"1904041956631658496","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T00:30:00.00039+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757896200000,"timestamp":"2025-09-15T00:30:00.00039+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":878105080,"host.name":"dev-careportal","time":1757896200000,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:30:00.369Z","event_id":"1904041956635652097","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*********","sw.remote.ip":"*********","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T00:30:00.001265+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757896200001,"timestamp":"2025-09-15T00:30:00.001265+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":64555014,"host.name":"dev-careportal","time":1757896200001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:30:00.369Z","event_id":"1904041956635652098","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*********","sw.remote.ip":"*********","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T00:30:00.244076+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757896200244,"timestamp":"2025-09-15T00:30:00.244076+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":64555014,"host.name":"dev-careportal","time":1757896200244,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:30:00.680Z","event_id":"1904041957938458625","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T00:30:00.302035+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757896200302,"timestamp":"2025-09-15T00:30:00.302035+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":878100814,"host.name":"dev-careportal","time":1757896200302,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:30:49.255Z","event_id":"1904042161677991938","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:30:49.19254+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=a8712486-f439-3c8c-c423-169bac48a024 fwd=\"************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757896249192,"timestamp":"2025-09-15T00:30:49.19254+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"a8712486-f439-3c8c-c423-169bac48a024","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":915459683,"host.name":"dev-careportal","time":1757896249192,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:31:49.226Z","event_id":"1904042413213700097","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:31:49.162103+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=62462da0-a0ff-7205-a71f-b57a6c04f764 fwd=\"**************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757896309162,"timestamp":"2025-09-15T00:31:49.162103+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"62462da0-a0ff-7205-a71f-b57a6c04f764","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":915459683,"host.name":"dev-careportal","time":1757896309162,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:32:49.145Z","event_id":"1904042664532451328","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:32:49.085171+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=4ee978c3-2467-70a5-dc71-fc99a4fa3b10 fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757896369085,"timestamp":"2025-09-15T00:32:49.085171+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"4ee978c3-2467-70a5-dc71-fc99a4fa3b10","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":915459683,"host.name":"dev-careportal","time":1757896369085,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:33:49.146Z","event_id":"1904042916196147231","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:33:49.085543+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=19cbaaa6-056a-c2cf-c670-f98f5f83fbb8 fwd=\"************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757896429085,"timestamp":"2025-09-15T00:33:49.085543+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"19cbaaa6-056a-c2cf-c670-f98f5f83fbb8","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":915420555,"host.name":"dev-careportal","time":1757896429085,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:34:40.223Z","event_id":"1904043130428420096","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:34:40.158875+00:00 host heroku router - ","logmsg":"at=error code=H15 desc=\"Idle connection\" method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=7c0b5466-7958-31f8-6f9c-912d528985d2 fwd=\"*************\" dyno=web.1 connect=0ms service=55001ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757896480158,"timestamp":"2025-09-15T00:34:40.158875+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"error","code":"H15","desc":"Idle connection","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"7c0b5466-7958-31f8-6f9c-912d528985d2","fwd":"*************","destinationDyno":"web.1","connect":"0ms","service":"55001ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":55001},"syslog_priority":134,"sender_ip":65347386,"host.name":"dev-careportal","time":1757896480158,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:34:40.732Z","event_id":"1904043132562214912","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T00:34:40.159553+00:00 host app web.1 - ","logmsg":"User Disconnected","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757896480159,"timestamp":"2025-09-15T00:34:40.159553+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":751259511,"host.name":"dev-careportal","time":1757896480159,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:34:41.220Z","event_id":"1904043134609379328","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:34:41.158236+00:00 host heroku router - ","logmsg":"at=error code=H15 desc=\"Idle connection\" method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=e4123820-31eb-b172-b1eb-f78d5fe3bc2e fwd=\"*************\" dyno=web.1 connect=0ms service=55001ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757896481158,"timestamp":"2025-09-15T00:34:41.158236+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"error","code":"H15","desc":"Idle connection","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"e4123820-31eb-b172-b1eb-f78d5fe3bc2e","fwd":"*************","destinationDyno":"web.1","connect":"0ms","service":"55001ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":55001},"syslog_priority":134,"sender_ip":872446568,"host.name":"dev-careportal","time":1757896481158,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:34:41.527Z","event_id":"1904043135898161217","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**********","sw.remote.ip":"**********","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T00:34:41.158864+00:00 host app web.1 - ","logmsg":"User Disconnected","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757896481158,"timestamp":"2025-09-15T00:34:41.158864+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":872865543,"host.name":"dev-careportal","time":1757896481158,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:34:42.218Z","event_id":"1904043138794786817","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:34:42.154835+00:00 host heroku router - ","logmsg":"at=error code=H15 desc=\"Idle connection\" method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=716218a5-f5f3-15d1-dc83-a2789e2ed4d6 fwd=\"*************\" dyno=web.1 connect=0ms service=55001ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757896482154,"timestamp":"2025-09-15T00:34:42.154835+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"error","code":"H15","desc":"Idle connection","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"716218a5-f5f3-15d1-dc83-a2789e2ed4d6","fwd":"*************","destinationDyno":"web.1","connect":"0ms","service":"55001ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":55001},"syslog_priority":134,"sender_ip":583288920,"host.name":"dev-careportal","time":1757896482154,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:34:42.521Z","event_id":"1904043140067209273","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T00:34:42.155447+00:00 host app web.1 - ","logmsg":"User Disconnected","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757896482155,"timestamp":"2025-09-15T00:34:42.155447+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":598550846,"host.name":"dev-careportal","time":1757896482155,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:34:49.240Z","event_id":"1904043168246772038","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:34:49.180163+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=c8a0fc29-0642-1244-984a-39dc5f1a3428 fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757896489180,"timestamp":"2025-09-15T00:34:49.180163+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"c8a0fc29-0642-1244-984a-39dc5f1a3428","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":872752520,"host.name":"dev-careportal","time":1757896489180,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:35:00.364Z","event_id":"1904043214904209415","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T00:35:00.001302+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757896500001,"timestamp":"2025-09-15T00:35:00.001302+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":751241915,"host.name":"dev-careportal","time":1757896500001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:35:00.366Z","event_id":"1904043214912647180","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T00:35:00.003925+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757896500003,"timestamp":"2025-09-15T00:35:00.003925+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":64943925,"host.name":"dev-careportal","time":1757896500003,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:35:00.636Z","event_id":"1904043216044519458","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T00:35:00.270198+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757896500270,"timestamp":"2025-09-15T00:35:00.270198+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":1210858270,"host.name":"dev-careportal","time":1757896500270,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:35:00.648Z","event_id":"1904043216097660928","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T00:35:00.284071+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757896500284,"timestamp":"2025-09-15T00:35:00.284071+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":751259511,"host.name":"dev-careportal","time":1757896500284,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:35:49.187Z","event_id":"1904043419684679681","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:35:49.128702+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=3f758da1-46e4-8695-cc81-b53ca96ad53e fwd=\"**************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757896549128,"timestamp":"2025-09-15T00:35:49.128702+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"3f758da1-46e4-8695-cc81-b53ca96ad53e","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":917457379,"host.name":"dev-careportal","time":1757896549128,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:36:49.260Z","event_id":"1904043671648063490","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:36:49.203375+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=017aece5-9d3b-b8da-db76-56d0e329ba24 fwd=\"************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757896609203,"timestamp":"2025-09-15T00:36:49.203375+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"017aece5-9d3b-b8da-db76-56d0e329ba24","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":839938714,"host.name":"dev-careportal","time":1757896609203,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:37:49.256Z","event_id":"1904043923290677248","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:37:49.195113+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=db10565b-64dc-06d2-47b2-e31928d851d0 fwd=\"**************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757896669195,"timestamp":"2025-09-15T00:37:49.195113+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"db10565b-64dc-06d2-47b2-e31928d851d0","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":65347386,"host.name":"dev-careportal","time":1757896669195,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:38:49.170Z","event_id":"1904044174586179584","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:38:49.108346+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=0235f7ba-5552-bdcc-44d9-094e66328115 fwd=\"**************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757896729108,"timestamp":"2025-09-15T00:38:49.108346+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"0235f7ba-5552-bdcc-44d9-094e66328115","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":751948336,"host.name":"dev-careportal","time":1757896729108,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:39:49.266Z","event_id":"1904044426647072768","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:39:49.208886+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=651d9bdc-4b28-5726-c444-6e474bcd0deb fwd=\"************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757896789208,"timestamp":"2025-09-15T00:39:49.208886+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"651d9bdc-4b28-5726-c444-6e474bcd0deb","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":917457379,"host.name":"dev-careportal","time":1757896789208,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:40:00.370Z","event_id":"1904044473222262784","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**********","sw.remote.ip":"**********","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T00:40:00.002649+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757896800002,"timestamp":"2025-09-15T00:40:00.002649+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":877072644,"host.name":"dev-careportal","time":1757896800002,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:40:00.383Z","event_id":"1904044473276399619","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T00:40:00.007191+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757896800007,"timestamp":"2025-09-15T00:40:00.007191+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":598550846,"host.name":"dev-careportal","time":1757896800007,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:40:00.639Z","event_id":"1904044474349461991","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T00:40:00.265964+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757896800265,"timestamp":"2025-09-15T00:40:00.265964+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":911355445,"host.name":"dev-careportal","time":1757896800265,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:40:00.646Z","event_id":"1904044474378784768","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T00:40:00.277317+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757896800277,"timestamp":"2025-09-15T00:40:00.277317+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":65412522,"host.name":"dev-careportal","time":1757896800277,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:40:49.266Z","event_id":"1904044678305263640","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:40:49.203734+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=27d6fdda-1382-d83e-f909-0dfddf46235d fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757896849203,"timestamp":"2025-09-15T00:40:49.203734+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"27d6fdda-1382-d83e-f909-0dfddf46235d","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":885544519,"host.name":"dev-careportal","time":1757896849203,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:41:49.184Z","event_id":"1904044929621647361","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:41:49.1274+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=984d43c4-68fd-2aec-3ccf-cb66d35f90b6 fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757896909127,"timestamp":"2025-09-15T00:41:49.1274+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"984d43c4-68fd-2aec-3ccf-cb66d35f90b6","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":65347386,"host.name":"dev-careportal","time":1757896909127,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:42:49.256Z","event_id":"1904045181581471745","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:42:49.191657+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=c80565b8-dae9-e3a7-3ed7-6aefbd84eb1f fwd=\"************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757896969191,"timestamp":"2025-09-15T00:42:49.191657+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"c80565b8-dae9-e3a7-3ed7-6aefbd84eb1f","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":885544519,"host.name":"dev-careportal","time":1757896969191,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:43:49.374Z","event_id":"1904045433734934528","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:43:49.312003+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=5bd46ec6-0835-86fa-85c6-277331db6523 fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757897029312,"timestamp":"2025-09-15T00:43:49.312003+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"5bd46ec6-0835-86fa-85c6-277331db6523","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":917457379,"host.name":"dev-careportal","time":1757897029312,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:44:49.141Z","event_id":"1904045684415901697","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:44:49.083151+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=410c9abf-df04-61ba-3aab-126e7f316d4e fwd=\"**************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757897089083,"timestamp":"2025-09-15T00:44:49.083151+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"410c9abf-df04-61ba-3aab-126e7f316d4e","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":885619922,"host.name":"dev-careportal","time":1757897089083,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:45:00.366Z","event_id":"1904045731495579649","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T00:45:00.001327+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757897100001,"timestamp":"2025-09-15T00:45:00.001327+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":65067227,"host.name":"dev-careportal","time":1757897100001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:45:00.382Z","event_id":"1904045731562156032","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T00:45:00.002837+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757897100002,"timestamp":"2025-09-15T00:45:00.002837+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":840034151,"host.name":"dev-careportal","time":1757897100002,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:45:00.382Z","event_id":"1904045731562156033","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T00:45:00.242963+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757897100242,"timestamp":"2025-09-15T00:45:00.242963+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":840034151,"host.name":"dev-careportal","time":1757897100242,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:45:00.672Z","event_id":"1904045732780036096","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T00:45:00.301277+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757897100301,"timestamp":"2025-09-15T00:45:00.301277+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":65005012,"host.name":"dev-careportal","time":1757897100301,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:45:49.171Z","event_id":"1904045936198373376","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:45:49.111226+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=88a64f4e-1ba1-f8a5-0678-1e2f2cca3982 fwd=\"************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757897149111,"timestamp":"2025-09-15T00:45:49.111226+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"88a64f4e-1ba1-f8a5-0678-1e2f2cca3982","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":752148558,"host.name":"dev-careportal","time":1757897149111,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:46:49.315Z","event_id":"1904046188462104580","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:46:49.250547+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=092caeb0-c49d-5405-6257-47477259d54e fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757897209250,"timestamp":"2025-09-15T00:46:49.250547+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"092caeb0-c49d-5405-6257-47477259d54e","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":598626444,"host.name":"dev-careportal","time":1757897209250,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:47:43.676Z","event_id":"1904046416468840453","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/web.1","loghdr":"<134>1 2025-09-15T00:47:43.616482+00:00 host heroku web.1 - ","logmsg":"Cycling","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757897263616,"timestamp":"2025-09-15T00:47:43.616482+00:00","host":"host","appName":"heroku","procId":"web.1","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"source":"heroku","dyno":"web.1"},"syslog_priority":134,"sender_ip":1796696349,"host.name":"dev-careportal","time":1757897263616,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:47:43.676Z","event_id":"1904046416468840454","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/web.1","loghdr":"<134>1 2025-09-15T00:47:43.619016+00:00 host heroku web.1 - ","logmsg":"State changed from up to starting","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757897263619,"timestamp":"2025-09-15T00:47:43.619016+00:00","host":"host","appName":"heroku","procId":"web.1","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"source":"heroku","dyno":"web.1"},"syslog_priority":134,"sender_ip":1796696349,"host.name":"dev-careportal","time":1757897263619,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:47:44.467Z","event_id":"1904046419784527873","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:47:44.410197+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=a654bbf7-3491-ad4b-b3dc-c67325c92cad fwd=\"************\" dyno=web.1 connect=0ms service=56606698ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757897264410,"timestamp":"2025-09-15T00:47:44.410197+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"a654bbf7-3491-ad4b-b3dc-c67325c92cad","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"56606698ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":56606698},"syslog_priority":134,"sender_ip":64909548,"host.name":"dev-careportal","time":1757897264410,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:47:44.467Z","event_id":"1904046419786059778","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:47:44.410083+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=a933f341-858c-fd29-5135-cc6463f8d876 fwd=\"************\" dyno=web.1 connect=0ms service=84604401ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757897264410,"timestamp":"2025-09-15T00:47:44.410083+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"a933f341-858c-fd29-5135-cc6463f8d876","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"84604401ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":84604401},"syslog_priority":134,"sender_ip":875402401,"host.name":"dev-careportal","time":1757897264410,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:47:44.467Z","event_id":"1904046419786059779","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:47:44.410439+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=7692f2ed-eab5-52ef-d419-45055606a64d fwd=\"************\" dyno=web.1 connect=0ms service=14906678ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757897264410,"timestamp":"2025-09-15T00:47:44.410439+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"7692f2ed-eab5-52ef-d419-45055606a64d","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"14906678ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":14906678},"syslog_priority":134,"sender_ip":875402401,"host.name":"dev-careportal","time":1757897264410,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:47:44.467Z","event_id":"1904046419786358784","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:47:44.410088+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=6bb3b149-2887-b8e8-7a3c-aa5e60cf7f19 fwd=\"************\" dyno=web.1 connect=0ms service=84604140ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757897264410,"timestamp":"2025-09-15T00:47:44.410088+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"6bb3b149-2887-b8e8-7a3c-aa5e60cf7f19","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"84604140ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":84604140},"syslog_priority":134,"sender_ip":65347386,"host.name":"dev-careportal","time":1757897264410,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:47:44.467Z","event_id":"1904046419786358786","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:47:44.410485+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=7ccdcbcc-b741-f22a-e6a0-1ecfa034d409 fwd=\"************\" dyno=web.1 connect=0ms service=14902689ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757897264410,"timestamp":"2025-09-15T00:47:44.410485+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"7ccdcbcc-b741-f22a-e6a0-1ecfa034d409","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"14902689ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":14902689},"syslog_priority":134,"sender_ip":751902213,"host.name":"dev-careportal","time":1757897264410,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:47:44.468Z","event_id":"1904046419789041757","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:47:44.410328+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=527a3a91-2e1c-1031-5801-af62469dfabc fwd=\"************\" dyno=web.1 connect=0ms service=84607615ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757897264410,"timestamp":"2025-09-15T00:47:44.410328+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"527a3a91-2e1c-1031-5801-af62469dfabc","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"84607615ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":84607615},"syslog_priority":134,"sender_ip":56552554,"host.name":"dev-careportal","time":1757897264410,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:47:44.470Z","event_id":"1904046419797643264","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:47:44.410226+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=80d55b4a-ec5c-58e2-74c4-ab2d3b78b762 fwd=\"************\" dyno=web.1 connect=0ms service=84607342ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757897264410,"timestamp":"2025-09-15T00:47:44.410226+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"80d55b4a-ec5c-58e2-74c4-ab2d3b78b762","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"84607342ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":84607342},"syslog_priority":134,"sender_ip":56511184,"host.name":"dev-careportal","time":1757897264410,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:47:44.470Z","event_id":"1904046419797680128","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:47:44.410263+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=3cfe4100-b4a4-fc3e-6991-da644d37daf4 fwd=\"************\" dyno=web.1 connect=0ms service=15981059ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757897264410,"timestamp":"2025-09-15T00:47:44.410263+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"3cfe4100-b4a4-fc3e-6991-da644d37daf4","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"15981059ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":15981059},"syslog_priority":134,"sender_ip":2927750400,"host.name":"dev-careportal","time":1757897264410,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:47:44.470Z","event_id":"1904046419797680129","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:47:44.410304+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=95aa66ba-d8b9-a2fc-319d-a3278337655d fwd=\"************\" dyno=web.1 connect=0ms service=84609293ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757897264410,"timestamp":"2025-09-15T00:47:44.410304+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"95aa66ba-d8b9-a2fc-319d-a3278337655d","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"84609293ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":84609293},"syslog_priority":134,"sender_ip":916874444,"host.name":"dev-careportal","time":1757897264410,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:47:44.470Z","event_id":"1904046419797680130","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:47:44.410449+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=535554cb-fa76-25f8-aeee-b7a65a08ad42 fwd=\"************\" dyno=web.1 connect=0ms service=84609859ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757897264410,"timestamp":"2025-09-15T00:47:44.410449+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"535554cb-fa76-25f8-aeee-b7a65a08ad42","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"84609859ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":84609859},"syslog_priority":134,"sender_ip":915459683,"host.name":"dev-careportal","time":1757897264410,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:47:44.470Z","event_id":"1904046419797987328","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:47:44.410234+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=c72cd689-6cac-c429-90d9-55df42438338 fwd=\"************\" dyno=web.1 connect=0ms service=14901786ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757897264410,"timestamp":"2025-09-15T00:47:44.410234+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"c72cd689-6cac-c429-90d9-55df42438338","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"14901786ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":14901786},"syslog_priority":134,"sender_ip":872752520,"host.name":"dev-careportal","time":1757897264410,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:47:44.471Z","event_id":"1904046419802955776","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:47:44.410285+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=7c9fe609-5c28-82ac-0431-eeadd9268ed0 fwd=\"************\" dyno=web.1 connect=0ms service=84606507ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757897264410,"timestamp":"2025-09-15T00:47:44.410285+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"7c9fe609-5c28-82ac-0431-eeadd9268ed0","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"84606507ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":84606507},"syslog_priority":134,"sender_ip":585709848,"host.name":"dev-careportal","time":1757897264410,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:47:44.471Z","event_id":"1904046419803525120","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:47:44.410178+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=8c3a4f1a-e9be-b79c-10fe-02d0a876610f fwd=\"************\" dyno=web.1 connect=0ms service=84606165ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757897264410,"timestamp":"2025-09-15T00:47:44.410178+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"8c3a4f1a-e9be-b79c-10fe-02d0a876610f","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"84606165ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":84606165},"syslog_priority":134,"sender_ip":915507155,"host.name":"dev-careportal","time":1757897264410,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:47:44.471Z","event_id":"1904046419803525121","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:47:44.410352+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=6d93a185-23f3-397c-38d3-c0a01cbb9ea2 fwd=\"************\" dyno=web.1 connect=0ms service=84608745ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757897264410,"timestamp":"2025-09-15T00:47:44.410352+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"6d93a185-23f3-397c-38d3-c0a01cbb9ea2","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"84608745ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":84608745},"syslog_priority":134,"sender_ip":915507155,"host.name":"dev-careportal","time":1757897264410,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:47:44.472Z","event_id":"1904046419806748672","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:47:44.410515+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=79054e84-2cb1-b049-1e41-4f8e6641e0e6 fwd=\"************\" dyno=web.1 connect=0ms service=15981614ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757897264410,"timestamp":"2025-09-15T00:47:44.410515+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"79054e84-2cb1-b049-1e41-4f8e6641e0e6","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"15981614ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":15981614},"syslog_priority":134,"sender_ip":916530674,"host.name":"dev-careportal","time":1757897264410,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:47:44.475Z","event_id":"1904046419817492480","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:47:44.410264+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=0366aa44-aa03-a4e2-2177-badf5010034e fwd=\"************\" dyno=web.1 connect=0ms service=84605614ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757897264410,"timestamp":"2025-09-15T00:47:44.410264+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"0366aa44-aa03-a4e2-2177-badf5010034e","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"84605614ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":84605614},"syslog_priority":134,"sender_ip":751887166,"host.name":"dev-careportal","time":1757897264410,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:47:44.475Z","event_id":"1904046419817492481","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:47:44.410418+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=83f215b6-c1f5-fe9c-d519-fd5a6ffc62db fwd=\"************\" dyno=web.1 connect=0ms service=84607893ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757897264410,"timestamp":"2025-09-15T00:47:44.410418+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"83f215b6-c1f5-fe9c-d519-fd5a6ffc62db","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"84607893ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":84607893},"syslog_priority":134,"sender_ip":751887166,"host.name":"dev-careportal","time":1757897264410,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:47:44.475Z","event_id":"1904046419819720704","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:47:44.410149+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=92cc270b-908c-a1d3-36aa-2e77f7df0b8a fwd=\"************\" dyno=web.1 connect=0ms service=15980497ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757897264410,"timestamp":"2025-09-15T00:47:44.410149+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"92cc270b-908c-a1d3-36aa-2e77f7df0b8a","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"15980497ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":15980497},"syslog_priority":134,"sender_ip":65353257,"host.name":"dev-careportal","time":1757897264410,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:47:44.475Z","event_id":"1904046419819720705","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:47:44.410375+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=3e6f5ab7-af2c-782e-8568-9fb49941b80f fwd=\"************\" dyno=web.1 connect=0ms service=84608437ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757897264410,"timestamp":"2025-09-15T00:47:44.410375+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"3e6f5ab7-af2c-782e-8568-9fb49941b80f","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"84608437ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":84608437},"syslog_priority":134,"sender_ip":65353257,"host.name":"dev-careportal","time":1757897264410,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:47:44.490Z","event_id":"1904046419880947713","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/web.1","loghdr":"<134>1 2025-09-15T00:47:44.365021+00:00 host heroku web.1 - ","logmsg":"Stopping all processes with SIGTERM","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757897264365,"timestamp":"2025-09-15T00:47:44.365021+00:00","host":"host","appName":"heroku","procId":"web.1","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"source":"heroku","dyno":"web.1"},"syslog_priority":134,"sender_ip":872799036,"host.name":"dev-careportal","time":1757897264365,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:47:44.583Z","event_id":"1904046420272717825","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/web.1","loghdr":"<134>1 2025-09-15T00:47:44.465474+00:00 host heroku web.1 - ","logmsg":"Process exited with status 143","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757897264465,"timestamp":"2025-09-15T00:47:44.465474+00:00","host":"host","appName":"heroku","procId":"web.1","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"source":"heroku","dyno":"web.1"},"syslog_priority":134,"sender_ip":911778771,"host.name":"dev-careportal","time":1757897264465,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:47:49.211Z","event_id":"1904046439682674689","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:47:49.144813+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=af760e76-e3cb-bd9e-b673-2d2167da4b9c fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757897269144,"timestamp":"2025-09-15T00:47:49.144813+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"af760e76-e3cb-bd9e-b673-2d2167da4b9c","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":387412291,"host.name":"dev-careportal","time":1757897269144,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:47:50.322Z","event_id":"1904046444344205312","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/web.1","loghdr":"<134>1 2025-09-15T00:47:50.203285+00:00 host heroku web.1 - ","logmsg":"Starting process with command `npm start`","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757897270203,"timestamp":"2025-09-15T00:47:50.203285+00:00","host":"host","appName":"heroku","procId":"web.1","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"source":"heroku","dyno":"web.1"},"syslog_priority":134,"sender_ip":840009423,"host.name":"dev-careportal","time":1757897270203,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:47:51.645Z","event_id":"1904046449892880385","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T00:47:51.267898+00:00 host app web.1 - ","logmsg":"> patient-care-backend@1.0.0 start","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757897271267,"timestamp":"2025-09-15T00:47:51.267898+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":387340868,"host.name":"dev-careportal","time":1757897271267,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:47:51.645Z","event_id":"1904046449892880386","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T00:47:51.267898+00:00 host app web.1 - ","logmsg":"> node app.mjs","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757897271267,"timestamp":"2025-09-15T00:47:51.267898+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":387340868,"host.name":"dev-careportal","time":1757897271267,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:47:53.221Z","event_id":"1904046456503382017","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T00:47:52.856376+00:00 host app web.1 - ","logmsg":"(node:21) [DEP0170] DeprecationWarning: The URL mongodb://cardiowell-backend:<EMAIL>:27017,prod-cardiowell-shard-00-02.sfvtp.mongodb.net:27017,prod-cardiowell-shard-00-00.sfvtp.mongodb.net:27017/cardiowell_prod?authSource=admin&replicaSet=atlas-l6w7wg-shard-0&retryWrites=true&w=majority&appName=prod-cardiowell&ssl=true is invalid. Future versions of Node.js will throw an error.","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757897272856,"timestamp":"2025-09-15T00:47:52.856376+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":915657614,"host.name":"dev-careportal","time":1757897272856,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:47:53.221Z","event_id":"1904046456503382018","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T00:47:52.856389+00:00 host app web.1 - ","logmsg":"(Use `node --trace-deprecation ...` to show where the warning was created)","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757897272856,"timestamp":"2025-09-15T00:47:52.856389+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":915657614,"host.name":"dev-careportal","time":1757897272856,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:47:54.040Z","event_id":"1904046459936010241","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T00:47:53.678059+00:00 host app web.1 - ","logmsg":"(node:21) DeprecationWarning: collection.ensureIndex is deprecated. Use createIndexes instead.","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757897273678,"timestamp":"2025-09-15T00:47:53.678059+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":885963040,"host.name":"dev-careportal","time":1757897273678,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:47:55.065Z","event_id":"1904046464237768705","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/web.1","loghdr":"<134>1 2025-09-15T00:47:55.007413+00:00 host heroku web.1 - ","logmsg":"State changed from starting to up","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757897275007,"timestamp":"2025-09-15T00:47:55.007413+00:00","host":"host","appName":"heroku","procId":"web.1","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"source":"heroku","dyno":"web.1"},"syslog_priority":134,"sender_ip":915475079,"host.name":"dev-careportal","time":1757897275007,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:47:55.233Z","event_id":"1904046464942411777","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T00:47:54.869247+00:00 host app web.1 - ","logmsg":"[Sentry] express is not instrumented. Please make sure to initialize Sentry in a separate file that you `--import` when running node, see: https://docs.sentry.io/platforms/javascript/guides/express/install/esm/.","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757897274869,"timestamp":"2025-09-15T00:47:54.869247+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":583663810,"host.name":"dev-careportal","time":1757897274869,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:47:55.233Z","event_id":"1904046464942411778","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T00:47:54.891+00:00 host app web.1 - ","logmsg":"Started on 8486","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757897274891,"timestamp":"2025-09-15T00:47:54.891+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":583663810,"host.name":"dev-careportal","time":1757897274891,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:48:49.246Z","event_id":"1904046691489263616","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:48:49.186764+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=ac8d6190-efe5-780f-a1d5-0abeaa27627f fwd=\"************\" dyno=web.1 connect=0ms service=18ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757897329186,"timestamp":"2025-09-15T00:48:49.186764+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"ac8d6190-efe5-780f-a1d5-0abeaa27627f","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"18ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":18},"syslog_priority":134,"sender_ip":64563239,"host.name":"dev-careportal","time":1757897329186,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:49:49.300Z","event_id":"1904046943373996032","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:49:49.23947+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=40c1c58a-0553-e8b6-5a26-26a1920c2ef8 fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757897389239,"timestamp":"2025-09-15T00:49:49.23947+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"40c1c58a-0553-e8b6-5a26-26a1920c2ef8","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":64909548,"host.name":"dev-careportal","time":1757897389239,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:50:00.365Z","event_id":"1904046989781463040","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T00:50:00.001401+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757897400001,"timestamp":"2025-09-15T00:50:00.001401+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":65412522,"host.name":"dev-careportal","time":1757897400001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:50:00.371Z","event_id":"1904046989807788032","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T00:50:00.00562+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757897400005,"timestamp":"2025-09-15T00:50:00.00562+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":317279016,"host.name":"dev-careportal","time":1757897400005,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:50:00.647Z","event_id":"1904046990966497283","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T00:50:00.285117+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757897400285,"timestamp":"2025-09-15T00:50:00.285117+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":1796681299,"host.name":"dev-careportal","time":1757897400285,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:50:00.653Z","event_id":"1904046990991634434","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T00:50:00.286145+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757897400286,"timestamp":"2025-09-15T00:50:00.286145+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":911694378,"host.name":"dev-careportal","time":1757897400286,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:50:49.127Z","event_id":"1904047194306711552","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:50:49.068305+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=1d66da13-7038-bc94-ff6f-8222f2d4d73b fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757897449068,"timestamp":"2025-09-15T00:50:49.068305+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"1d66da13-7038-bc94-ff6f-8222f2d4d73b","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":872446568,"host.name":"dev-careportal","time":1757897449068,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:51:49.132Z","event_id":"1904047445985165312","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:51:49.075927+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=a637f640-6468-7078-7f78-172ec3308221 fwd=\"************\" dyno=web.1 connect=0ms service=3ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757897509075,"timestamp":"2025-09-15T00:51:49.075927+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"a637f640-6468-7078-7f78-172ec3308221","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"3ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":3},"syslog_priority":134,"sender_ip":65053479,"host.name":"dev-careportal","time":1757897509075,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:52:49.261Z","event_id":"1904047698185052160","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:52:49.201121+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=51813c76-e4c8-557c-59c3-a7473a3d0896 fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757897569201,"timestamp":"2025-09-15T00:52:49.201121+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"51813c76-e4c8-557c-59c3-a7473a3d0896","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":56552554,"host.name":"dev-careportal","time":1757897569201,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:53:49.119Z","event_id":"1904047949247700993","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:53:49.061494+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=03411bb8-18fc-4af9-50c2-670f715d9639 fwd=\"**************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757897629061,"timestamp":"2025-09-15T00:53:49.061494+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"03411bb8-18fc-4af9-50c2-670f715d9639","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":64563239,"host.name":"dev-careportal","time":1757897629061,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:54:49.269Z","event_id":"1904048201533825025","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:54:49.210605+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=341cc475-f84d-1372-0619-b30269070049 fwd=\"************\" dyno=web.1 connect=0ms service=3ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757897689210,"timestamp":"2025-09-15T00:54:49.210605+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"341cc475-f84d-1372-0619-b30269070049","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"3ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":3},"syslog_priority":134,"sender_ip":885915710,"host.name":"dev-careportal","time":1757897689210,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:55:00.368Z","event_id":"1904048248085245955","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T00:55:00.002005+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757897700002,"timestamp":"2025-09-15T00:55:00.002005+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":316010271,"host.name":"dev-careportal","time":1757897700002,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:55:00.368Z","event_id":"1904048248085245956","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T00:55:00.234707+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757897700234,"timestamp":"2025-09-15T00:55:00.234707+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":316010271,"host.name":"dev-careportal","time":1757897700234,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:55:00.377Z","event_id":"1904048248123535367","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T00:55:00.001595+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757897700001,"timestamp":"2025-09-15T00:55:00.001595+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":916283955,"host.name":"dev-careportal","time":1757897700001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:55:00.669Z","event_id":"1904048249349971968","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T00:55:00.303126+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757897700303,"timestamp":"2025-09-15T00:55:00.303126+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":585200453,"host.name":"dev-careportal","time":1757897700303,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:55:49.235Z","event_id":"1904048453049765967","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:55:49.160351+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=5e5dd29f-2b86-9558-274a-1719a3dbff94 fwd=\"**************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757897749160,"timestamp":"2025-09-15T00:55:49.160351+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"5e5dd29f-2b86-9558-274a-1719a3dbff94","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":583222442,"host.name":"dev-careportal","time":1757897749160,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:56:49.229Z","event_id":"1904048704683786255","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:56:49.167031+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=bc143c00-7a28-642a-3d50-74404f904c54 fwd=\"**************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757897809167,"timestamp":"2025-09-15T00:56:49.167031+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"bc143c00-7a28-642a-3d50-74404f904c54","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":916485046,"host.name":"dev-careportal","time":1757897809167,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:57:49.148Z","event_id":"1904048955999875074","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:57:49.090939+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=486b767e-3a6a-8fff-3519-11db6dc02beb fwd=\"************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757897869090,"timestamp":"2025-09-15T00:57:49.090939+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"486b767e-3a6a-8fff-3519-11db6dc02beb","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":916530674,"host.name":"dev-careportal","time":1757897869090,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:58:49.258Z","event_id":"1904049208121294848","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:58:49.194165+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=c8a999bf-d8de-7e7a-ed06-89521f680ea1 fwd=\"**************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757897929194,"timestamp":"2025-09-15T00:58:49.194165+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"c8a999bf-d8de-7e7a-ed06-89521f680ea1","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":872446568,"host.name":"dev-careportal","time":1757897929194,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T00:59:49.157Z","event_id":"1904049459355910144","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T00:59:49.099424+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=2a3834be-c469-9bba-3e37-b5eaefb1493f fwd=\"**************\" dyno=web.1 connect=0ms service=3ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757897989099,"timestamp":"2025-09-15T00:59:49.099424+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"2a3834be-c469-9bba-3e37-b5eaefb1493f","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"3ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":3},"syslog_priority":134,"sender_ip":65380042,"host.name":"dev-careportal","time":1757897989099,"source_name":"dev-careportal"}
