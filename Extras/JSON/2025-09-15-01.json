{"receive_time":"2025-09-15T01:00:00.365Z","event_id":"1904049506366103552","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T01:00:00.001499+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757898000001,"timestamp":"2025-09-15T01:00:00.001499+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":583206931,"host.name":"dev-careportal","time":1757898000001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:00:00.386Z","event_id":"1904049506454355970","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T01:00:00.00204+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757898000002,"timestamp":"2025-09-15T01:00:00.00204+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":873837287,"host.name":"dev-careportal","time":1757898000002,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:00:00.666Z","event_id":"1904049507628761088","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T01:00:00.303907+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757898000303,"timestamp":"2025-09-15T01:00:00.303907+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":751174567,"host.name":"dev-careportal","time":1757898000303,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:00:00.711Z","event_id":"1904049507817902081","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***************","sw.remote.ip":"***************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T01:00:00.344607+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757898000344,"timestamp":"2025-09-15T01:00:00.344607+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":2927746991,"host.name":"dev-careportal","time":1757898000344,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:00:49.158Z","event_id":"1904049711018749952","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:00:49.095201+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=b0a6e95c-6e79-9233-38a5-912faaf2f7c1 fwd=\"************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757898049095,"timestamp":"2025-09-15T01:00:49.095201+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"b0a6e95c-6e79-9233-38a5-912faaf2f7c1","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":877058535,"host.name":"dev-careportal","time":1757898049095,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:01:49.279Z","event_id":"1904049963182878784","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:01:49.216411+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=f720fb61-0de3-b4fa-7612-54b456dfefb6 fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757898109216,"timestamp":"2025-09-15T01:01:49.216411+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"f720fb61-0de3-b4fa-7612-54b456dfefb6","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":916300108,"host.name":"dev-careportal","time":1757898109216,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:02:49.142Z","event_id":"1904050214268506112","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:02:49.082434+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=564ec049-3de5-612c-9ba8-40fe17c4cd72 fwd=\"**************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757898169082,"timestamp":"2025-09-15T01:02:49.082434+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"564ec049-3de5-612c-9ba8-40fe17c4cd72","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":64563239,"host.name":"dev-careportal","time":1757898169082,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:03:49.211Z","event_id":"1904050466215989249","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:03:49.142064+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=66df8066-0e1f-19d6-e3ef-67a03d56da2b fwd=\"************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757898229142,"timestamp":"2025-09-15T01:03:49.142064+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"66df8066-0e1f-19d6-e3ef-67a03d56da2b","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":65053479,"host.name":"dev-careportal","time":1757898229142,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:04:49.258Z","event_id":"1904050718070050816","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:04:49.199218+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=18f1ed28-4b64-4c58-19b9-f76815aa4063 fwd=\"**************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757898289199,"timestamp":"2025-09-15T01:04:49.199218+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"18f1ed28-4b64-4c58-19b9-f76815aa4063","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":583481212,"host.name":"dev-careportal","time":1757898289199,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:05:00.367Z","event_id":"1904050764665884712","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T01:05:00.002313+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757898300002,"timestamp":"2025-09-15T01:05:00.002313+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":920785861,"host.name":"dev-careportal","time":1757898300002,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:05:00.388Z","event_id":"1904050764754341888","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T01:05:00.005567+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757898300005,"timestamp":"2025-09-15T01:05:00.005567+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":55684874,"host.name":"dev-careportal","time":1757898300005,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:05:00.631Z","event_id":"1904050765771870211","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T01:05:00.269228+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757898300269,"timestamp":"2025-09-15T01:05:00.269228+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":877154211,"host.name":"dev-careportal","time":1757898300269,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:05:00.698Z","event_id":"1904050766053605379","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T01:05:00.330581+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757898300330,"timestamp":"2025-09-15T01:05:00.330581+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":915452677,"host.name":"dev-careportal","time":1757898300330,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:05:49.114Z","event_id":"1904050969125400576","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:05:49.057077+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=6b872531-233b-791d-d105-01b449b91bf9 fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757898349057,"timestamp":"2025-09-15T01:05:49.057077+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"6b872531-233b-791d-d105-01b449b91bf9","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":915507155,"host.name":"dev-careportal","time":1757898349057,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:06:49.110Z","event_id":"1904051220767248385","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:06:49.052773+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=5f84441c-3d3e-1c55-cd62-b3b1674fd551 fwd=\"************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757898409052,"timestamp":"2025-09-15T01:06:49.052773+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"5f84441c-3d3e-1c55-cd62-b3b1674fd551","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":585825447,"host.name":"dev-careportal","time":1757898409052,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:07:49.228Z","event_id":"1904051472920436736","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:07:49.167687+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=687f56fd-6ceb-bbfa-0067-b750af7fa097 fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757898469167,"timestamp":"2025-09-15T01:07:49.167687+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"687f56fd-6ceb-bbfa-0067-b750af7fa097","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":56552554,"host.name":"dev-careportal","time":1757898469167,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:08:49.135Z","event_id":"1904051724187795456","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:08:49.074546+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=928e008e-bc9a-843d-14d1-b23409bb5510 fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757898529074,"timestamp":"2025-09-15T01:08:49.074546+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"928e008e-bc9a-843d-14d1-b23409bb5510","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":65380042,"host.name":"dev-careportal","time":1757898529074,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:09:49.114Z","event_id":"1904051975757307904","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:09:49.051788+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=b0aad217-bce7-71b1-c033-474f0385071a fwd=\"************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757898589051,"timestamp":"2025-09-15T01:09:49.051788+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"b0aad217-bce7-71b1-c033-474f0385071a","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":583547411,"host.name":"dev-careportal","time":1757898589051,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:10:00.378Z","event_id":"1904052023003209728","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T01:10:00.002126+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757898600002,"timestamp":"2025-09-15T01:10:00.002126+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":911299866,"host.name":"dev-careportal","time":1757898600002,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:10:00.395Z","event_id":"1904052023073214466","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T01:10:00.005559+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757898600005,"timestamp":"2025-09-15T01:10:00.005559+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":752265568,"host.name":"dev-careportal","time":1757898600005,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:10:00.680Z","event_id":"1904052024269697024","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T01:10:00.301295+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757898600301,"timestamp":"2025-09-15T01:10:00.301295+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":872418425,"host.name":"dev-careportal","time":1757898600301,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:10:00.739Z","event_id":"1904052024517550080","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T01:10:00.374348+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757898600374,"timestamp":"2025-09-15T01:10:00.374348+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":917440609,"host.name":"dev-careportal","time":1757898600374,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:10:49.287Z","event_id":"1904052228142813184","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:10:49.226229+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=264ac8cb-f249-33e3-13df-0351717b653b fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757898649226,"timestamp":"2025-09-15T01:10:49.226229+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"264ac8cb-f249-33e3-13df-0351717b653b","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":65053479,"host.name":"dev-careportal","time":1757898649226,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:11:49.186Z","event_id":"1904052479376740352","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:11:49.123341+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=7c035d76-0138-32b7-e514-a7bd6d0e6563 fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757898709123,"timestamp":"2025-09-15T01:11:49.123341+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"7c035d76-0138-32b7-e514-a7bd6d0e6563","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":916530674,"host.name":"dev-careportal","time":1757898709123,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:12:49.149Z","event_id":"1904052730880266241","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:12:49.089761+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=aef16b55-2d1b-d220-274f-2dbf509b630b fwd=\"************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757898769089,"timestamp":"2025-09-15T01:12:49.089761+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"aef16b55-2d1b-d220-274f-2dbf509b630b","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":751887166,"host.name":"dev-careportal","time":1757898769089,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:13:49.326Z","event_id":"1904052983280918529","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**********","sw.remote.ip":"**********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:13:49.264299+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=0864cf03-9c1f-ab91-9566-1ce3bf9a11fc fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757898829264,"timestamp":"2025-09-15T01:13:49.264299+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"0864cf03-9c1f-ab91-9566-1ce3bf9a11fc","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":885720841,"host.name":"dev-careportal","time":1757898829264,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:14:49.228Z","event_id":"1904053234527768576","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:14:49.166905+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=f8bafae7-0207-edfc-2ade-cef3e2518045 fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757898889166,"timestamp":"2025-09-15T01:14:49.166905+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"f8bafae7-0207-edfc-2ade-cef3e2518045","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":751738059,"host.name":"dev-careportal","time":1757898889166,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:15:00.367Z","event_id":"1904053281248284675","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T01:15:00.001312+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757898900001,"timestamp":"2025-09-15T01:15:00.001312+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":315845624,"host.name":"dev-careportal","time":1757898900001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:15:00.368Z","event_id":"1904053281252855808","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T01:15:00.005477+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757898900005,"timestamp":"2025-09-15T01:15:00.005477+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":585653277,"host.name":"dev-careportal","time":1757898900005,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:15:00.638Z","event_id":"1904053282382508044","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T01:15:00.272138+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757898900272,"timestamp":"2025-09-15T01:15:00.272138+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":64399363,"host.name":"dev-careportal","time":1757898900272,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:15:00.696Z","event_id":"1904053282627584000","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T01:15:00.318921+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757898900318,"timestamp":"2025-09-15T01:15:00.318921+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":316052645,"host.name":"dev-careportal","time":1757898900318,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:15:49.220Z","event_id":"1904053486152425472","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:15:49.158798+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=f64dfdcd-9b88-a870-8e26-8f990d0a5481 fwd=\"************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757898949158,"timestamp":"2025-09-15T01:15:49.158798+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"f64dfdcd-9b88-a870-8e26-8f990d0a5481","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":916485046,"host.name":"dev-careportal","time":1757898949158,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:16:49.244Z","event_id":"1904053737910894598","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:16:49.18841+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=6c1d36c3-bc26-e0d1-835b-e023358da7d0 fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899009188,"timestamp":"2025-09-15T01:16:49.18841+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"6c1d36c3-bc26-e0d1-835b-e023358da7d0","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":65347386,"host.name":"dev-careportal","time":1757899009188,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:17:49.118Z","event_id":"1904053989040652288","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:17:49.061556+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=352fd9cd-c25f-3395-f3bd-6d03d3cc5022 fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899069061,"timestamp":"2025-09-15T01:17:49.061556+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"352fd9cd-c25f-3395-f3bd-6d03d3cc5022","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":885619922,"host.name":"dev-careportal","time":1757899069061,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:18:49.251Z","event_id":"1904054241256050689","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:18:49.190787+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=3b73a962-a605-4472-3eef-d8b6fb8b30af fwd=\"************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899129190,"timestamp":"2025-09-15T01:18:49.190787+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"3b73a962-a605-4472-3eef-d8b6fb8b30af","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":752148558,"host.name":"dev-careportal","time":1757899129190,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:19:49.234Z","event_id":"1904054492843671575","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:19:49.176654+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=0351ebde-8ff6-fb72-d2b3-7aeb51be5484 fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899189176,"timestamp":"2025-09-15T01:19:49.176654+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"0351ebde-8ff6-fb72-d2b3-7aeb51be5484","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":916296964,"host.name":"dev-careportal","time":1757899189176,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:20:00.371Z","event_id":"1904054539556446327","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T01:20:00.001126+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757899200001,"timestamp":"2025-09-15T01:20:00.001126+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":878100814,"host.name":"dev-careportal","time":1757899200001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:20:00.396Z","event_id":"1904054539659845632","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T01:20:00.003223+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757899200003,"timestamp":"2025-09-15T01:20:00.003223+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":751241915,"host.name":"dev-careportal","time":1757899200003,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:20:00.641Z","event_id":"1904054540687249414","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T01:20:00.264566+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757899200264,"timestamp":"2025-09-15T01:20:00.264566+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":1796681299,"host.name":"dev-careportal","time":1757899200264,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:20:00.657Z","event_id":"1904054540756209664","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T01:20:00.290881+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757899200290,"timestamp":"2025-09-15T01:20:00.290881+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":387352237,"host.name":"dev-careportal","time":1757899200290,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:20:49.160Z","event_id":"1904054744190849026","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:20:49.102453+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=0fa7b596-a882-e1df-a8c1-ecfa34393985 fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899249102,"timestamp":"2025-09-15T01:20:49.102453+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"0fa7b596-a882-e1df-a8c1-ecfa34393985","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":872752520,"host.name":"dev-careportal","time":1757899249102,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:21:49.159Z","event_id":"1904054995846582274","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:21:49.102556+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=126e0591-6ce0-d87f-9f06-6cb9f42d001f fwd=\"************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899309102,"timestamp":"2025-09-15T01:21:49.102556+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"126e0591-6ce0-d87f-9f06-6cb9f42d001f","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":917457379,"host.name":"dev-careportal","time":1757899309102,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:22:49.267Z","event_id":"1904055247955906560","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:22:49.204499+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=02ba89af-c882-eb12-59ee-1e75fa3768ae fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899369204,"timestamp":"2025-09-15T01:22:49.204499+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"02ba89af-c882-eb12-59ee-1e75fa3768ae","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":875402401,"host.name":"dev-careportal","time":1757899369204,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:23:49.116Z","event_id":"1904055498980806656","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:23:49.057397+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=5ffb4ff5-106c-c0dd-5682-ebf3912cd948 fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899429057,"timestamp":"2025-09-15T01:23:49.057397+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"5ffb4ff5-106c-c0dd-5682-ebf3912cd948","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":917457379,"host.name":"dev-careportal","time":1757899429057,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:24:49.262Z","event_id":"1904055751252733952","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:24:49.200399+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=80ca599b-13aa-21fd-6a7b-1d3d972b0612 fwd=\"************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899489200,"timestamp":"2025-09-15T01:24:49.200399+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"80ca599b-13aa-21fd-6a7b-1d3d972b0612","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":916207073,"host.name":"dev-careportal","time":1757899489200,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:24:50.663Z","event_id":"1904055757129535488","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:24:50.604163+00:00 host heroku router - ","logmsg":"at=error code=H15 desc=\"Idle connection\" method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=77978124-6ba1-7a4b-a51d-2587c8836b34 fwd=\"*************\" dyno=web.2 connect=0ms service=55001ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899490604,"timestamp":"2025-09-15T01:24:50.604163+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"error","code":"H15","desc":"Idle connection","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"77978124-6ba1-7a4b-a51d-2587c8836b34","fwd":"*************","destinationDyno":"web.2","connect":"0ms","service":"55001ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":55001},"syslog_priority":134,"sender_ip":751887166,"host.name":"dev-careportal","time":1757899490604,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:24:51.591Z","event_id":"1904055761020846081","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T01:24:50.604811+00:00 host app web.2 - ","logmsg":"User Disconnected","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757899490604,"timestamp":"2025-09-15T01:24:50.604811+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":64588467,"host.name":"dev-careportal","time":1757899490604,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:24:51.812Z","event_id":"1904055761948221441","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:24:51.602929+00:00 host heroku router - ","logmsg":"at=error code=H15 desc=\"Idle connection\" method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=65d50b31-a484-b694-6b16-66fe598d1c0a fwd=\"*************\" dyno=web.1 connect=0ms service=55004ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899491602,"timestamp":"2025-09-15T01:24:51.602929+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"error","code":"H15","desc":"Idle connection","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"65d50b31-a484-b694-6b16-66fe598d1c0a","fwd":"*************","destinationDyno":"web.1","connect":"0ms","service":"55004ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":55004},"syslog_priority":134,"sender_ip":872752520,"host.name":"dev-careportal","time":1757899491602,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:24:51.966Z","event_id":"1904055762594144256","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T01:24:51.60475+00:00 host app web.1 - ","logmsg":"User Disconnected","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757899491604,"timestamp":"2025-09-15T01:24:51.60475+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":872510944,"host.name":"dev-careportal","time":1757899491604,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:25:00.373Z","event_id":"1904055797855838209","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**********","sw.remote.ip":"**********","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T01:25:00.002073+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757899500002,"timestamp":"2025-09-15T01:25:00.002073+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":872509193,"host.name":"dev-careportal","time":1757899500002,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:25:00.377Z","event_id":"1904055797872701440","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T01:25:00.002262+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757899500002,"timestamp":"2025-09-15T01:25:00.002262+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":56582585,"host.name":"dev-careportal","time":1757899500002,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:25:00.646Z","event_id":"1904055799000731704","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T01:25:00.278198+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757899500278,"timestamp":"2025-09-15T01:25:00.278198+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":65347363,"host.name":"dev-careportal","time":1757899500278,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:25:00.666Z","event_id":"1904055799083470848","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T01:25:00.290915+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757899500290,"timestamp":"2025-09-15T01:25:00.290915+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":64447757,"host.name":"dev-careportal","time":1757899500290,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:25:49.371Z","event_id":"1904056003366465536","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:25:49.310248+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=8f44f15d-33a4-2daa-f173-b8c72805f9f8 fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899549310,"timestamp":"2025-09-15T01:25:49.310248+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"8f44f15d-33a4-2daa-f173-b8c72805f9f8","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":1796498957,"host.name":"dev-careportal","time":1757899549310,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:26:49.148Z","event_id":"1904056254091075584","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:26:49.091992+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=9fb52303-1bc0-20f3-d0d9-2c3fa152907c fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899609091,"timestamp":"2025-09-15T01:26:49.091992+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"9fb52303-1bc0-20f3-d0d9-2c3fa152907c","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":56552554,"host.name":"dev-careportal","time":1757899609091,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:27:11.925Z","event_id":"1904056349623963648","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/web.2","loghdr":"<134>1 2025-09-15T01:27:11.863822+00:00 host heroku web.2 - ","logmsg":"Cycling","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899631863,"timestamp":"2025-09-15T01:27:11.863822+00:00","host":"host","appName":"heroku","procId":"web.2","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"source":"heroku","dyno":"web.2"},"syslog_priority":134,"sender_ip":916207073,"host.name":"dev-careportal","time":1757899631863,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:27:11.925Z","event_id":"1904056349623963649","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/web.2","loghdr":"<134>1 2025-09-15T01:27:11.866987+00:00 host heroku web.2 - ","logmsg":"State changed from up to starting","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899631866,"timestamp":"2025-09-15T01:27:11.866987+00:00","host":"host","appName":"heroku","procId":"web.2","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"source":"heroku","dyno":"web.2"},"syslog_priority":134,"sender_ip":916207073,"host.name":"dev-careportal","time":1757899631866,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:27:13.574Z","event_id":"1904056356540710912","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:27:13.517637+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=78960d43-a1b3-543c-973e-dbdc61914e24 fwd=\"************\" dyno=web.2 connect=0ms service=2364007ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899633517,"timestamp":"2025-09-15T01:27:13.517637+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"78960d43-a1b3-543c-973e-dbdc61914e24","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2364007ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2364007},"syslog_priority":134,"sender_ip":65053479,"host.name":"dev-careportal","time":1757899633517,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:27:13.574Z","event_id":"1904056356541145088","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:27:13.517729+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=363a0cd8-bec4-bb71-c277-4e45214311d4 fwd=\"************\" dyno=web.2 connect=0ms service=18350443ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899633517,"timestamp":"2025-09-15T01:27:13.517729+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"363a0cd8-bec4-bb71-c277-4e45214311d4","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"18350443ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":18350443},"syslog_priority":134,"sender_ip":751738059,"host.name":"dev-careportal","time":1757899633517,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:27:13.574Z","event_id":"1904056356541145089","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:27:13.517853+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=712af3c0-7825-40a0-1db5-0c65407e69e6 fwd=\"************\" dyno=web.2 connect=0ms service=2367343ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899633517,"timestamp":"2025-09-15T01:27:13.517853+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"712af3c0-7825-40a0-1db5-0c65407e69e6","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2367343ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2367343},"syslog_priority":134,"sender_ip":751738059,"host.name":"dev-careportal","time":1757899633517,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:27:13.574Z","event_id":"1904056356541145090","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:27:13.517553+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=8382c978-8a89-af49-085e-3c84c365287b fwd=\"************\" dyno=web.2 connect=0ms service=2364858ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899633517,"timestamp":"2025-09-15T01:27:13.517553+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"8382c978-8a89-af49-085e-3c84c365287b","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2364858ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2364858},"syslog_priority":134,"sender_ip":751738059,"host.name":"dev-careportal","time":1757899633517,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:27:13.574Z","event_id":"1904056356541145091","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:27:13.517381+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=baf7d31a-b042-ccc2-8e8b-5ad7da682ae5 fwd=\"************\" dyno=web.2 connect=0ms service=2363112ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899633517,"timestamp":"2025-09-15T01:27:13.517381+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"baf7d31a-b042-ccc2-8e8b-5ad7da682ae5","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2363112ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2363112},"syslog_priority":134,"sender_ip":64563239,"host.name":"dev-careportal","time":1757899633517,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:27:13.574Z","event_id":"1904056356541173799","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:27:13.517533+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=2c65f934-a9ee-bdd5-24cc-74c09a016c8b fwd=\"************\" dyno=web.2 connect=0ms service=2365676ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899633517,"timestamp":"2025-09-15T01:27:13.517533+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"2c65f934-a9ee-bdd5-24cc-74c09a016c8b","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2365676ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2365676},"syslog_priority":134,"sender_ip":751887166,"host.name":"dev-careportal","time":1757899633517,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:27:13.574Z","event_id":"1904056356541173811","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:27:13.517437+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=b70a6621-bf98-fe12-a8de-57665fcf84ef fwd=\"************\" dyno=web.2 connect=0ms service=2363382ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899633517,"timestamp":"2025-09-15T01:27:13.517437+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"b70a6621-bf98-fe12-a8de-57665fcf84ef","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2363382ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2363382},"syslog_priority":134,"sender_ip":56552554,"host.name":"dev-careportal","time":1757899633517,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:27:13.575Z","event_id":"1904056356544905216","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:27:13.517739+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=02e0108b-42d2-ada0-7763-998eaf0bdaef fwd=\"************\" dyno=web.2 connect=0ms service=2366801ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899633517,"timestamp":"2025-09-15T01:27:13.517739+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"02e0108b-42d2-ada0-7763-998eaf0bdaef","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2366801ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2366801},"syslog_priority":134,"sender_ip":915507155,"host.name":"dev-careportal","time":1757899633517,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:27:13.575Z","event_id":"1904056356545327104","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:27:13.51752+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=48ef0e72-7965-ad76-1f51-c35c5452b3a3 fwd=\"************\" dyno=web.2 connect=0ms service=2363672ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899633517,"timestamp":"2025-09-15T01:27:13.51752+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"48ef0e72-7965-ad76-1f51-c35c5452b3a3","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2363672ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2363672},"syslog_priority":134,"sender_ip":387412291,"host.name":"dev-careportal","time":1757899633517,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:27:13.575Z","event_id":"1904056356545327105","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:27:13.517533+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=c0e66e2f-dc69-538e-6cb4-0387ff426413 fwd=\"************\" dyno=web.2 connect=0ms service=2365942ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899633517,"timestamp":"2025-09-15T01:27:13.517533+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"c0e66e2f-dc69-538e-6cb4-0387ff426413","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2365942ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2365942},"syslog_priority":134,"sender_ip":387412291,"host.name":"dev-careportal","time":1757899633517,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:27:13.575Z","event_id":"1904056356545327106","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:27:13.517584+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=34b0d52b-f64d-d72a-5cc6-90c542022d4b fwd=\"************\" dyno=web.2 connect=0ms service=2364588ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899633517,"timestamp":"2025-09-15T01:27:13.517584+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"34b0d52b-f64d-d72a-5cc6-90c542022d4b","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2364588ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2364588},"syslog_priority":134,"sender_ip":2927750400,"host.name":"dev-careportal","time":1757899633517,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:27:13.575Z","event_id":"1904056356545519616","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:27:13.517577+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=bd3ef306-c130-f208-fd29-c67a3bd7915d fwd=\"************\" dyno=web.2 connect=0ms service=2365413ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899633517,"timestamp":"2025-09-15T01:27:13.517577+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"bd3ef306-c130-f208-fd29-c67a3bd7915d","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2365413ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2365413},"syslog_priority":134,"sender_ip":875402401,"host.name":"dev-careportal","time":1757899633517,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:27:13.575Z","event_id":"1904056356545519617","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:27:13.517811+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=b41e42cf-be8c-e798-f5b7-3c00eec20c0b fwd=\"************\" dyno=web.2 connect=0ms service=58976649ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899633517,"timestamp":"2025-09-15T01:27:13.517811+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"b41e42cf-be8c-e798-f5b7-3c00eec20c0b","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"58976649ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":58976649},"syslog_priority":134,"sender_ip":875402401,"host.name":"dev-careportal","time":1757899633517,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:27:13.575Z","event_id":"1904056356545519618","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:27:13.517778+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=b8bc3f4d-35e8-39ce-4eb8-49ca9f5ae3ad fwd=\"************\" dyno=web.2 connect=0ms service=17272901ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899633517,"timestamp":"2025-09-15T01:27:13.517778+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"b8bc3f4d-35e8-39ce-4eb8-49ca9f5ae3ad","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"17272901ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":17272901},"syslog_priority":134,"sender_ip":64493748,"host.name":"dev-careportal","time":1757899633517,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:27:13.576Z","event_id":"1904056356549521408","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:27:13.517683+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=c67b9250-eb75-3635-099c-0da04c530433 fwd=\"************\" dyno=web.2 connect=0ms service=2366638ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899633517,"timestamp":"2025-09-15T01:27:13.517683+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"c67b9250-eb75-3635-099c-0da04c530433","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2366638ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2366638},"syslog_priority":134,"sender_ip":751948336,"host.name":"dev-careportal","time":1757899633517,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:27:13.576Z","event_id":"1904056356549713920","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:27:13.517753+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=8169d1d2-faeb-5b34-b24c-fa9a08ebba73 fwd=\"************\" dyno=web.2 connect=0ms service=21874584ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899633517,"timestamp":"2025-09-15T01:27:13.517753+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"8169d1d2-faeb-5b34-b24c-fa9a08ebba73","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"21874584ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":21874584},"syslog_priority":134,"sender_ip":583288920,"host.name":"dev-careportal","time":1757899633517,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:27:13.576Z","event_id":"1904056356549713921","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:27:13.517668+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=3cce298c-87c6-3ba9-81a1-69d80eb9a83f fwd=\"************\" dyno=web.2 connect=0ms service=2366531ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899633517,"timestamp":"2025-09-15T01:27:13.517668+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"3cce298c-87c6-3ba9-81a1-69d80eb9a83f","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2366531ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2366531},"syslog_priority":134,"sender_ip":872752520,"host.name":"dev-careportal","time":1757899633517,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:27:13.577Z","event_id":"1904056356552609792","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:27:13.51768+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=c3a60c62-f9a3-b3dd-cd2b-849f9885dbcb fwd=\"************\" dyno=web.2 connect=0ms service=2366254ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899633517,"timestamp":"2025-09-15T01:27:13.51768+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"c3a60c62-f9a3-b3dd-cd2b-849f9885dbcb","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2366254ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2366254},"syslog_priority":134,"sender_ip":885619922,"host.name":"dev-careportal","time":1757899633517,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:27:13.578Z","event_id":"1904056356558278656","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:27:13.517583+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=6c04dfdb-6828-26b6-b20c-784863a3ca10 fwd=\"************\" dyno=web.2 connect=0ms service=2365127ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899633517,"timestamp":"2025-09-15T01:27:13.517583+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"6c04dfdb-6828-26b6-b20c-784863a3ca10","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2365127ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2365127},"syslog_priority":134,"sender_ip":915459683,"host.name":"dev-careportal","time":1757899633517,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:27:13.578Z","event_id":"1904056356558299136","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:27:13.517796+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=bca8107b-78cd-db93-492d-7f83be5a3edd fwd=\"************\" dyno=web.2 connect=0ms service=2367059ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899633517,"timestamp":"2025-09-15T01:27:13.517796+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"bca8107b-78cd-db93-492d-7f83be5a3edd","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2367059ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2367059},"syslog_priority":134,"sender_ip":64614606,"host.name":"dev-careportal","time":1757899633517,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:27:13.578Z","event_id":"1904056356558299137","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:27:13.517505+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=47f1292e-8139-60d7-e29b-16fee663ec25 fwd=\"************\" dyno=web.2 connect=0ms service=2362563ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899633517,"timestamp":"2025-09-15T01:27:13.517505+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"47f1292e-8139-60d7-e29b-16fee663ec25","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2362563ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2362563},"syslog_priority":134,"sender_ip":387259567,"host.name":"dev-careportal","time":1757899633517,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:27:13.578Z","event_id":"1904056356558491648","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:27:13.517519+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=4a2c9a5b-413e-81cc-c632-9a27fa0757c4 fwd=\"************\" dyno=web.2 connect=0ms service=2362849ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899633517,"timestamp":"2025-09-15T01:27:13.517519+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"4a2c9a5b-413e-81cc-c632-9a27fa0757c4","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2362849ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2362849},"syslog_priority":134,"sender_ip":915560822,"host.name":"dev-careportal","time":1757899633517,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:27:13.579Z","event_id":"1904056356562288640","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:27:13.517674+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=0d72d0c8-9cd6-3d5a-3eb8-67b73010609c fwd=\"************\" dyno=web.2 connect=0ms service=21874847ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899633517,"timestamp":"2025-09-15T01:27:13.517674+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"0d72d0c8-9cd6-3d5a-3eb8-67b73010609c","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"21874847ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":21874847},"syslog_priority":134,"sender_ip":915420555,"host.name":"dev-careportal","time":1757899633517,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:27:13.582Z","event_id":"1904056356573618177","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:27:13.517942+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=616460e6-fa8f-939b-1f8a-f59eb9b6e545 fwd=\"************\" dyno=web.2 connect=0ms service=18346813ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899633517,"timestamp":"2025-09-15T01:27:13.517942+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"616460e6-fa8f-939b-1f8a-f59eb9b6e545","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"18346813ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":18346813},"syslog_priority":134,"sender_ip":916650686,"host.name":"dev-careportal","time":1757899633517,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:27:13.582Z","event_id":"1904056356573618178","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:27:13.51749+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=e7e7011e-55f4-aa51-45c5-c456349723a8 fwd=\"************\" dyno=web.2 connect=0ms service=2364294ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899633517,"timestamp":"2025-09-15T01:27:13.51749+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"e7e7011e-55f4-aa51-45c5-c456349723a8","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2364294ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2364294},"syslog_priority":134,"sender_ip":585794316,"host.name":"dev-careportal","time":1757899633517,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:27:13.603Z","event_id":"1904056356662779908","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/web.2","loghdr":"<134>1 2025-09-15T01:27:13.485581+00:00 host heroku web.2 - ","logmsg":"Stopping all processes with SIGTERM","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899633485,"timestamp":"2025-09-15T01:27:13.485581+00:00","host":"host","appName":"heroku","procId":"web.2","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"source":"heroku","dyno":"web.2"},"syslog_priority":134,"sender_ip":583725831,"host.name":"dev-careportal","time":1757899633485,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:27:13.699Z","event_id":"1904056357065433088","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/web.2","loghdr":"<134>1 2025-09-15T01:27:13.581394+00:00 host heroku web.2 - ","logmsg":"Process exited with status 143","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899633581,"timestamp":"2025-09-15T01:27:13.581394+00:00","host":"host","appName":"heroku","procId":"web.2","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"source":"heroku","dyno":"web.2"},"syslog_priority":134,"sender_ip":585982802,"host.name":"dev-careportal","time":1757899633581,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:27:18.846Z","event_id":"1904056378652434432","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/web.2","loghdr":"<134>1 2025-09-15T01:27:18.729499+00:00 host heroku web.2 - ","logmsg":"Starting process with command `npm start`","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899638729,"timestamp":"2025-09-15T01:27:18.729499+00:00","host":"host","appName":"heroku","procId":"web.2","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"source":"heroku","dyno":"web.2"},"syslog_priority":134,"sender_ip":919656059,"host.name":"dev-careportal","time":1757899638729,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:27:20.104Z","event_id":"1904056383928668161","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T01:27:19.728776+00:00 host app web.2 - ","logmsg":"> patient-care-backend@1.0.0 start","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757899639728,"timestamp":"2025-09-15T01:27:19.728776+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":916283955,"host.name":"dev-careportal","time":1757899639728,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:27:20.104Z","event_id":"1904056383928668162","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T01:27:19.728777+00:00 host app web.2 - ","logmsg":"> node app.mjs","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757899639728,"timestamp":"2025-09-15T01:27:19.728777+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":916283955,"host.name":"dev-careportal","time":1757899639728,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:27:21.595Z","event_id":"1904056390184034305","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T01:27:21.229141+00:00 host app web.2 - ","logmsg":"(node:21) [DEP0170] DeprecationWarning: The URL mongodb://cardiowell-backend:<EMAIL>:27017,prod-cardiowell-shard-00-02.sfvtp.mongodb.net:27017,prod-cardiowell-shard-00-00.sfvtp.mongodb.net:27017/cardiowell_prod?authSource=admin&replicaSet=atlas-l6w7wg-shard-0&retryWrites=true&w=majority&appName=prod-cardiowell&ssl=true is invalid. Future versions of Node.js will throw an error.","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757899641229,"timestamp":"2025-09-15T01:27:21.229141+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":839979168,"host.name":"dev-careportal","time":1757899641229,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:27:21.595Z","event_id":"1904056390184034306","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T01:27:21.229158+00:00 host app web.2 - ","logmsg":"(Use `node --trace-deprecation ...` to show where the warning was created)","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757899641229,"timestamp":"2025-09-15T01:27:21.229158+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":839979168,"host.name":"dev-careportal","time":1757899641229,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:27:22.418Z","event_id":"1904056393636139008","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T01:27:22.056595+00:00 host app web.2 - ","logmsg":"(node:21) DeprecationWarning: collection.ensureIndex is deprecated. Use createIndexes instead.","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757899642056,"timestamp":"2025-09-15T01:27:22.056595+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":64214448,"host.name":"dev-careportal","time":1757899642056,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:27:23.613Z","event_id":"1904056398647763164","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T01:27:23.25077+00:00 host app web.2 - ","logmsg":"[Sentry] express is not instrumented. Please make sure to initialize Sentry in a separate file that you `--import` when running node, see: https://docs.sentry.io/platforms/javascript/guides/express/install/esm/.","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757899643250,"timestamp":"2025-09-15T01:27:23.25077+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":315441490,"host.name":"dev-careportal","time":1757899643250,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:27:23.613Z","event_id":"1904056398647763165","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T01:27:23.27237+00:00 host app web.2 - ","logmsg":"Started on 36930","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757899643272,"timestamp":"2025-09-15T01:27:23.27237+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":315441490,"host.name":"dev-careportal","time":1757899643272,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:27:23.738Z","event_id":"1904056399172407296","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/web.2","loghdr":"<134>1 2025-09-15T01:27:23.678403+00:00 host heroku web.2 - ","logmsg":"State changed from starting to up","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899643678,"timestamp":"2025-09-15T01:27:23.678403+00:00","host":"host","appName":"heroku","procId":"web.2","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"source":"heroku","dyno":"web.2"},"syslog_priority":134,"sender_ip":583975488,"host.name":"dev-careportal","time":1757899643678,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:27:49.168Z","event_id":"1904056505833201665","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**********","sw.remote.ip":"**********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:27:49.110633+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=0919df96-77f5-bdbb-b9d5-8568eb45bb29 fwd=\"************\" dyno=web.2 connect=0ms service=14ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899669110,"timestamp":"2025-09-15T01:27:49.110633+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"0919df96-77f5-bdbb-b9d5-8568eb45bb29","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"14ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":14},"syslog_priority":134,"sender_ip":885720841,"host.name":"dev-careportal","time":1757899669110,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:28:49.257Z","event_id":"1904056757864615937","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:28:49.187042+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=5f3d2dab-f815-2423-98bb-0131ef99bd35 fwd=\"**************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899729187,"timestamp":"2025-09-15T01:28:49.187042+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"5f3d2dab-f815-2423-98bb-0131ef99bd35","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":583168653,"host.name":"dev-careportal","time":1757899729187,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:29:49.113Z","event_id":"1904057008918560768","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:29:49.056261+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=7341db5e-793d-6c63-a49c-da8b06ab5e8b fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899789056,"timestamp":"2025-09-15T01:29:49.056261+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"7341db5e-793d-6c63-a49c-da8b06ab5e8b","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":751738059,"host.name":"dev-careportal","time":1757899789056,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:30:00.366Z","event_id":"1904057056116379648","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T01:30:00.00135+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757899800001,"timestamp":"2025-09-15T01:30:00.00135+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":1264953453,"host.name":"dev-careportal","time":1757899800001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:30:00.369Z","event_id":"1904057056128798720","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T01:30:00.006159+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757899800006,"timestamp":"2025-09-15T01:30:00.006159+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":64300867,"host.name":"dev-careportal","time":1757899800006,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:30:00.704Z","event_id":"1904057057534738443","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**********","sw.remote.ip":"**********","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T01:30:00.337252+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757899800337,"timestamp":"2025-09-15T01:30:00.337252+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":885590023,"host.name":"dev-careportal","time":1757899800337,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:30:00.836Z","event_id":"1904057058087538688","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T01:30:00.472029+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757899800472,"timestamp":"2025-09-15T01:30:00.472029+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":1679372803,"host.name":"dev-careportal","time":1757899800472,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:30:49.132Z","event_id":"1904057260656807936","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:30:49.075793+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=3803400a-d16c-c5a9-e82a-8f87b8518b4f fwd=\"************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899849075,"timestamp":"2025-09-15T01:30:49.075793+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"3803400a-d16c-c5a9-e82a-8f87b8518b4f","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":885544519,"host.name":"dev-careportal","time":1757899849075,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:31:49.205Z","event_id":"1904057512620576768","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:31:49.146412+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=ca68a26e-6710-2e13-2793-3fd3a1848177 fwd=\"**************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899909146,"timestamp":"2025-09-15T01:31:49.146412+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"ca68a26e-6710-2e13-2793-3fd3a1848177","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":2927750400,"host.name":"dev-careportal","time":1757899909146,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:32:49.192Z","event_id":"1904057764223782913","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:32:49.12921+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=22f331d5-f666-77dc-ec4b-8a31318d3b0a fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757899969129,"timestamp":"2025-09-15T01:32:49.12921+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"22f331d5-f666-77dc-ec4b-8a31318d3b0a","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":583740246,"host.name":"dev-careportal","time":1757899969129,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:33:49.143Z","event_id":"1904058015677009920","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:33:49.081633+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=17531b86-c197-c3a4-28da-bf1eddd3adea fwd=\"************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757900029081,"timestamp":"2025-09-15T01:33:49.081633+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"17531b86-c197-c3a4-28da-bf1eddd3adea","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":916530674,"host.name":"dev-careportal","time":1757900029081,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:34:49.271Z","event_id":"1904058267870654464","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:34:49.210854+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=d03ffffb-069a-3fc4-cd50-730d31745fd9 fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757900089210,"timestamp":"2025-09-15T01:34:49.210854+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"d03ffffb-069a-3fc4-cd50-730d31745fd9","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":585794316,"host.name":"dev-careportal","time":1757900089210,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:35:00.369Z","event_id":"1904058314420506624","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T01:35:00.005788+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757900100005,"timestamp":"2025-09-15T01:35:00.005788+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":583366555,"host.name":"dev-careportal","time":1757900100005,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:35:00.392Z","event_id":"1904058314517721088","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T01:35:00.001307+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757900100001,"timestamp":"2025-09-15T01:35:00.001307+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":598432550,"host.name":"dev-careportal","time":1757900100001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:35:00.626Z","event_id":"1904058315496976384","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T01:35:00.26484+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757900100264,"timestamp":"2025-09-15T01:35:00.26484+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":877154211,"host.name":"dev-careportal","time":1757900100264,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:35:00.630Z","event_id":"1904058315514875904","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T01:35:00.263507+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757900100263,"timestamp":"2025-09-15T01:35:00.263507+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":840009423,"host.name":"dev-careportal","time":1757900100263,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:35:49.171Z","event_id":"1904058519110930432","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:35:49.1126+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=36637fcd-9fd6-a060-10c9-12b68054b822 fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757900149112,"timestamp":"2025-09-15T01:35:49.1126+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"36637fcd-9fd6-a060-10c9-12b68054b822","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":872752520,"host.name":"dev-careportal","time":1757900149112,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:36:49.262Z","event_id":"1904058771151192068","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:36:49.199503+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=bc6fb96f-2ce4-d37a-5133-420e4535a9e5 fwd=\"************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757900209199,"timestamp":"2025-09-15T01:36:49.199503+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"bc6fb96f-2ce4-d37a-5133-420e4535a9e5","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":583547411,"host.name":"dev-careportal","time":1757900209199,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:37:49.266Z","event_id":"1904059022826524672","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:37:49.205219+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=cd7e86e2-ef7b-ed6b-1ce3-b4498d155b27 fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757900269205,"timestamp":"2025-09-15T01:37:49.205219+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"cd7e86e2-ef7b-ed6b-1ce3-b4498d155b27","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":915459683,"host.name":"dev-careportal","time":1757900269205,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:38:49.184Z","event_id":"1904059274139668480","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:38:49.122716+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=f39a7281-56b2-092a-b788-cbc746c0aede fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757900329122,"timestamp":"2025-09-15T01:38:49.122716+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"f39a7281-56b2-092a-b788-cbc746c0aede","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":583288920,"host.name":"dev-careportal","time":1757900329122,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:39:49.100Z","event_id":"1904059525445586944","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:39:49.043471+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=6e65fe1d-85aa-f0a0-7cb4-3d9801ab6a86 fwd=\"************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757900389043,"timestamp":"2025-09-15T01:39:49.043471+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"6e65fe1d-85aa-f0a0-7cb4-3d9801ab6a86","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":915459683,"host.name":"dev-careportal","time":1757900389043,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:40:00.373Z","event_id":"1904059572729139201","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T01:40:00.003093+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757900400003,"timestamp":"2025-09-15T01:40:00.003093+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":583957059,"host.name":"dev-careportal","time":1757900400003,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:40:00.377Z","event_id":"1904059572745601024","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T01:40:00.003464+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757900400003,"timestamp":"2025-09-15T01:40:00.003464+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":315784300,"host.name":"dev-careportal","time":1757900400003,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:40:00.689Z","event_id":"1904059574054223872","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T01:40:00.324271+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757900400324,"timestamp":"2025-09-15T01:40:00.324271+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":877154211,"host.name":"dev-careportal","time":1757900400324,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:40:00.775Z","event_id":"1904059574415937536","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T01:40:00.400628+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757900400400,"timestamp":"2025-09-15T01:40:00.400628+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":583163665,"host.name":"dev-careportal","time":1757900400400,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:40:49.275Z","event_id":"1904059777838338049","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:40:49.211036+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=59409622-c7c6-8adb-150f-afd7ea045c46 fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757900449211,"timestamp":"2025-09-15T01:40:49.211036+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"59409622-c7c6-8adb-150f-afd7ea045c46","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":585794316,"host.name":"dev-careportal","time":1757900449211,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:41:49.204Z","event_id":"1904060029199728644","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:41:49.145161+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=9668b2eb-5ab7-cdad-314d-eabd26570eac fwd=\"**************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757900509145,"timestamp":"2025-09-15T01:41:49.145161+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"9668b2eb-5ab7-cdad-314d-eabd26570eac","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":598629640,"host.name":"dev-careportal","time":1757900509145,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:42:49.113Z","event_id":"1904060280475996160","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:42:49.054771+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=a46d3b83-e38e-24d6-6657-80c81eca2658 fwd=\"************\" dyno=web.2 connect=0ms service=3ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757900569054,"timestamp":"2025-09-15T01:42:49.054771+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"a46d3b83-e38e-24d6-6657-80c81eca2658","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"3ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":3},"syslog_priority":134,"sender_ip":885619922,"host.name":"dev-careportal","time":1757900569054,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:43:49.312Z","event_id":"1904060532969287680","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:43:49.25238+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=3bde0f4a-d127-5123-badb-7ce0a152d496 fwd=\"**************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757900629252,"timestamp":"2025-09-15T01:43:49.25238+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"3bde0f4a-d127-5123-badb-7ce0a152d496","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":387412291,"host.name":"dev-careportal","time":1757900629252,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:44:49.166Z","event_id":"1904060784015249409","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:44:49.10566+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=f07fe9ac-4e4b-ac4a-1bb1-2ef5144b94ad fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757900689105,"timestamp":"2025-09-15T01:44:49.10566+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"f07fe9ac-4e4b-ac4a-1bb1-2ef5144b94ad","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":872446568,"host.name":"dev-careportal","time":1757900689105,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:45:00.371Z","event_id":"1904060831010988032","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T01:45:00.001135+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757900700001,"timestamp":"2025-09-15T01:45:00.001135+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":912044996,"host.name":"dev-careportal","time":1757900700001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:45:00.371Z","event_id":"1904060831010988033","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T01:45:00.247678+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757900700247,"timestamp":"2025-09-15T01:45:00.247678+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":912044996,"host.name":"dev-careportal","time":1757900700247,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:45:00.371Z","event_id":"1904060831010988034","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T01:45:00.003757+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757900700003,"timestamp":"2025-09-15T01:45:00.003757+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":598568641,"host.name":"dev-careportal","time":1757900700003,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:45:00.627Z","event_id":"1904060832084480003","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T01:45:00.254518+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757900700254,"timestamp":"2025-09-15T01:45:00.254518+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":64943925,"host.name":"dev-careportal","time":1757900700254,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:45:49.168Z","event_id":"1904061035680747520","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:45:49.110767+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=df32094f-8206-000c-3625-06f09fad5389 fwd=\"************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757900749110,"timestamp":"2025-09-15T01:45:49.110767+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"df32094f-8206-000c-3625-06f09fad5389","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":885619922,"host.name":"dev-careportal","time":1757900749110,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:46:49.254Z","event_id":"1904061287698231296","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:46:49.191528+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=5c6a7aca-9c25-587f-ac85-d6a7b0fc208a fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757900809191,"timestamp":"2025-09-15T01:46:49.191528+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"5c6a7aca-9c25-587f-ac85-d6a7b0fc208a","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":915507155,"host.name":"dev-careportal","time":1757900809191,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:47:49.117Z","event_id":"1904061538783318016","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:47:49.057306+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=42d583d0-5ea7-b139-3580-4ee00a4692b5 fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757900869057,"timestamp":"2025-09-15T01:47:49.057306+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"42d583d0-5ea7-b139-3580-4ee00a4692b5","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":1796498957,"host.name":"dev-careportal","time":1757900869057,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:48:49.198Z","event_id":"1904061790782640128","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:48:49.138296+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=a5cb6042-ce27-750a-d94d-2f60e50bbe80 fwd=\"************\" dyno=web.1 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757900929138,"timestamp":"2025-09-15T01:48:49.138296+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"a5cb6042-ce27-750a-d94d-2f60e50bbe80","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":583547411,"host.name":"dev-careportal","time":1757900929138,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:49:49.269Z","event_id":"1904062042737987585","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:49:49.207599+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=8134df67-8642-2c35-7fae-246d0501562c fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757900989207,"timestamp":"2025-09-15T01:49:49.207599+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"8134df67-8642-2c35-7fae-246d0501562c","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":2927750400,"host.name":"dev-careportal","time":1757900989207,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:50:00.364Z","event_id":"1904062089273135140","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T01:50:00.001221+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757901000001,"timestamp":"2025-09-15T01:50:00.001221+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":583089298,"host.name":"dev-careportal","time":1757901000001,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:50:00.368Z","event_id":"1904062089289355264","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T01:50:00.00338+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757901000003,"timestamp":"2025-09-15T01:50:00.00338+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":585205404,"host.name":"dev-careportal","time":1757901000003,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:50:00.627Z","event_id":"1904062090377191426","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T01:50:00.260747+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757901000260,"timestamp":"2025-09-15T01:50:00.260747+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":598568641,"host.name":"dev-careportal","time":1757901000260,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:50:00.663Z","event_id":"1904062090528362500","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T01:50:00.296793+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757901000296,"timestamp":"2025-09-15T01:50:00.296793+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":751106977,"host.name":"dev-careportal","time":1757901000296,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:50:49.189Z","event_id":"1904062294059470858","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:50:49.127788+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=11e31167-b623-dfd1-3206-6138e594cbe0 fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757901049127,"timestamp":"2025-09-15T01:50:49.127788+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"11e31167-b623-dfd1-3206-6138e594cbe0","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":885915710,"host.name":"dev-careportal","time":1757901049127,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:51:49.196Z","event_id":"1904062545747968000","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:51:49.136232+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=58244509-91eb-a3ae-7659-9493f99d24fd fwd=\"************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757901109136,"timestamp":"2025-09-15T01:51:49.136232+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"58244509-91eb-a3ae-7659-9493f99d24fd","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":1796498957,"host.name":"dev-careportal","time":1757901109136,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:52:49.253Z","event_id":"1904062797645705348","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:52:49.193479+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=e2a639c0-5e83-541e-22ad-b194824c2d32 fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757901169193,"timestamp":"2025-09-15T01:52:49.193479+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"e2a639c0-5e83-541e-22ad-b194824c2d32","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":583427709,"host.name":"dev-careportal","time":1757901169193,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:53:49.162Z","event_id":"1904063048922275840","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:53:49.1015+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=caced03d-75d8-06c3-2574-4f02380deded fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757901229101,"timestamp":"2025-09-15T01:53:49.1015+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"caced03d-75d8-06c3-2574-4f02380deded","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":752148558,"host.name":"dev-careportal","time":1757901229101,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:54:49.173Z","event_id":"1904063300626219009","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:54:49.114878+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=323da051-1519-c0c3-943c-23c42a042eff fwd=\"************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757901289114,"timestamp":"2025-09-15T01:54:49.114878+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"323da051-1519-c0c3-943c-23c42a042eff","fwd":"************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":916207073,"host.name":"dev-careportal","time":1757901289114,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:55:00.368Z","event_id":"1904063347582066688","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T01:55:00.000677+00:00 host app web.1 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757901300000,"timestamp":"2025-09-15T01:55:00.000677+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":55728378,"host.name":"dev-careportal","time":1757901300000,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:55:00.370Z","event_id":"1904063347590262785","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T01:55:00.005946+00:00 host app web.2 - ","logmsg":"You will see this message every second","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757901300005,"timestamp":"2025-09-15T01:55:00.005946+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":1796594422,"host.name":"dev-careportal","time":1757901300005,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:55:00.370Z","event_id":"1904063347590262786","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**************","sw.remote.ip":"**************","syslog_appname":"app/web.2","loghdr":"<190>1 2025-09-15T01:55:00.247759+00:00 host app web.2 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757901300247,"timestamp":"2025-09-15T01:55:00.247759+00:00","host":"host","appName":"app","procId":"web.2","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.2"},"syslog_priority":190,"sender_ip":1796594422,"host.name":"dev-careportal","time":1757901300247,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:55:00.676Z","event_id":"1904063348872957952","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"**********","sw.remote.ip":"**********","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T01:55:00.310046+00:00 host app web.1 - ","logmsg":"data updated in the db------------------**********","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757901300310,"timestamp":"2025-09-15T01:55:00.310046+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":877072644,"host.name":"dev-careportal","time":1757901300310,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:55:49.340Z","event_id":"1904063552984260608","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:55:49.273192+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=665e966a-d3ad-027a-795d-655f8d6e09fa fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757901349273,"timestamp":"2025-09-15T01:55:49.273192+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"665e966a-d3ad-027a-795d-655f8d6e09fa","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":583547411,"host.name":"dev-careportal","time":1757901349273,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:56:49.176Z","event_id":"1904063803955744770","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:56:49.116199+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=166f1f21-19f3-07fb-4189-aba6d94cc0df fwd=\"**************\" dyno=web.2 connect=0ms service=1ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757901409116,"timestamp":"2025-09-15T01:56:49.116199+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"166f1f21-19f3-07fb-4189-aba6d94cc0df","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"1ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":1},"syslog_priority":134,"sender_ip":65380042,"host.name":"dev-careportal","time":1757901409116,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:57:49.140Z","event_id":"1904064055461310464","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"***********","sw.remote.ip":"***********","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:57:49.080005+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=86aa3479-8052-3b63-27fb-f8ed16af28db fwd=\"************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757901469080,"timestamp":"2025-09-15T01:57:49.080005+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"86aa3479-8052-3b63-27fb-f8ed16af28db","fwd":"************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":56511184,"host.name":"dev-careportal","time":1757901469080,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:58:49.245Z","event_id":"1904064307560521728","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:58:49.188356+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=c96f0db4-81b8-a732-fe15-c508a8f02130 fwd=\"**************\" dyno=web.2 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757901529188,"timestamp":"2025-09-15T01:58:49.188356+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"c96f0db4-81b8-a732-fe15-c508a8f02130","fwd":"**************","destinationDyno":"web.2","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":64909548,"host.name":"dev-careportal","time":1757901529188,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:59:35.522Z","event_id":"1904064501660635137","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"*************","sw.remote.ip":"*************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:59:35.46508+00:00 host heroku router - ","logmsg":"at=error code=H15 desc=\"Idle connection\" method=GET path=\"/socket.io/?EIO=3&transport=websocket\" host=careportal.cardiowell.com request_id=b3c5e347-3f08-d91e-310d-64ae3cffd29b fwd=\"*************\" dyno=web.1 connect=0ms service=55001ms status=101 bytes=0 protocol=http1.1 tls=true tls_version=tls1.3","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757901575465,"timestamp":"2025-09-15T01:59:35.46508+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"error","code":"H15","desc":"Idle connection","method":"GET","path":"/socket.io/?EIO=3&transport=websocket","host":"careportal.cardiowell.com","request_id":"b3c5e347-3f08-d91e-310d-64ae3cffd29b","fwd":"*************","destinationDyno":"web.1","connect":"0ms","service":"55001ms","status":"101","bytes":"0","protocol":"http1.1","tls":"true","tls_version":"tls1.3","dyno":"router","source":"heroku","connectMs":0,"serviceMs":55001},"syslog_priority":134,"sender_ip":387316936,"host.name":"dev-careportal","time":1757901575465,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:59:35.832Z","event_id":"1904064502960869378","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"app/web.1","loghdr":"<190>1 2025-09-15T01:59:35.465792+00:00 host app web.1 - ","logmsg":"User Disconnected","sw.log_destination.id":215577,"syslog":{"priority":"190","timestampMillis":1757901575465,"timestamp":"2025-09-15T01:59:35.465792+00:00","host":"host","appName":"app","procId":"web.1","severity":"Informational","facility":"local use 7"},"syslog_message":"-","heroku":{"source":"app","dyno":"web.1"},"syslog_priority":190,"sender_ip":63993100,"host.name":"dev-careportal","time":1757901575465,"source_name":"dev-careportal"}
{"receive_time":"2025-09-15T01:59:49.126Z","event_id":"1904064558719389696","sw.token.signature":"nQySAYwyKPpGNwg6H34TMHhgrQKogHdN5pTgxO6bH24","syslog_host":"dev-careportal","sender_ip_str":"************","sw.remote.ip":"************","syslog_appname":"heroku/router","loghdr":"<134>1 2025-09-15T01:59:49.063005+00:00 host heroku router - ","logmsg":"at=info method=GET path=\"/\" host=careportal.cardiowell.com request_id=3781a162-b21b-87f0-01e9-a9062dfe197e fwd=\"**************\" dyno=web.1 connect=0ms service=2ms status=200 bytes=1971 protocol=http1.1 tls=false","sw.log_destination.id":215577,"syslog":{"priority":"134","timestampMillis":1757901589063,"timestamp":"2025-09-15T01:59:49.063005+00:00","host":"host","appName":"heroku","procId":"router","severity":"Informational","facility":"local use 0"},"syslog_message":"-","heroku":{"at":"info","method":"GET","path":"/","host":"careportal.cardiowell.com","request_id":"3781a162-b21b-87f0-01e9-a9062dfe197e","fwd":"**************","destinationDyno":"web.1","connect":"0ms","service":"2ms","status":"200","bytes":"1971","protocol":"http1.1","tls":"false","dyno":"router","source":"heroku","connectMs":0,"serviceMs":2},"syslog_priority":134,"sender_ip":583288920,"host.name":"dev-careportal","time":1757901589063,"source_name":"dev-careportal"}
