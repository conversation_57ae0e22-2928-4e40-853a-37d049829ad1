# Cardiowell Deployment Documentation

## Table of Contents
1. [Overview](#overview)
2. [Git-Based Deployment Architecture](#git-based-deployment-architecture)
3. [Environment & Dependency Management](#environment--dependency-management)
4. [Configuration Management](#configuration-management)
5. [CI/CD Pipeline Details](#cicd-pipeline-details)
6. [Deployment Process](#deployment-process)
7. [Monitoring & Verification](#monitoring--verification)
8. [Troubleshooting](#troubleshooting)
9. [Heroku Version Management](#heroku-version-management)

## Overview

Cardiowell uses **Heroku's Git-based deployment with buildpacks** rather than containerized deployment. This approach leverages Heroku's automatic runtime management for Node.js applications, eliminating the need for Docker configuration while providing robust scalability and deployment automation.

### Deployment Architecture Summary
- **Frontend**: React SPA built and integrated into backend repository
- **Backend**: Node.js/Express API server that serves both API endpoints and compiled frontend
- **Platform**: Heroku with Node.js buildpack
- **Database**: MongoDB Atlas (production) / Local MongoDB (development)
- **Monitoring**: Sentry for error tracking and performance monitoring

## Git-Based Deployment Architecture

### Repository Structure
The Cardiowell project consists of two main repositories that work together in the deployment process:

```
cardiowell/
├── cardiowell-backend/          # Main deployment repository
│   ├── build/                   # Compiled React frontend
│   ├── app.mjs                  # Main server entry point
│   ├── package.json             # Backend dependencies & scripts
│   └── .github/workflows/       # Backend CI/CD pipeline
└── patientCareReact-master/     # Frontend development repository
    ├── src/                     # React source code
    ├── package.json             # Frontend dependencies & build scripts
    └── .github/workflows/       # Frontend CI/CD pipeline
```

### Complete Deployment Workflow

#### Phase 1: Frontend Development & Build
1. **Development**: Frontend developers work in `patientCareReact-master/`
2. **CI/CD Pipeline**: GitHub Actions run linting and quality checks
3. **Build Process**: When ready for deployment, run:
   ```bash
   cd patientCareReact-master/
   yarn build
   ```
4. **Build Output**: Creates optimized production build in `./build/` directory

#### Phase 2: Frontend Integration
1. **Copy Build**: Transfer the entire `./build/` folder from frontend to backend repository:
   ```bash
   # From patientCareReact-master/ directory
   cp -r ./build/* ../cardiowell-backend/build/
   ```
2. **Commit Changes**: Add the updated build to the backend repository:
   ```bash
   cd ../cardiowell-backend/
   git add build/
   git commit -m "Update frontend build for deployment"
   git push origin master
   ```

#### Phase 3: Backend Deployment
1. **Heroku Detection**: Heroku automatically detects Node.js project from `package.json`
2. **Buildpack Selection**: Uses Node.js buildpack (automatic)
3. **Dependency Installation**: Runs `yarn install` using lockfile
4. **Application Start**: Executes `yarn start` → `node app.mjs`

### Frontend-Backend Integration Details

The backend serves the compiled frontend through Express static file serving:

```javascript
// In app.mjs
app.use(express.static(path.join(__dirname, "/build")))

// SPA fallback route
app.get("/*", function (req, res) {
  res.sendFile(path.join(import.meta.dirname, "/build", "index.html"))
})
```

This configuration allows the single Heroku application to serve:
- **API endpoints**: `/routes/*` paths handled by Express routers
- **Static assets**: CSS, JS, images from `/build/static/`
- **SPA routing**: All other paths serve the React application

## Environment & Dependency Management

### Node.js Version Requirements

**Required Versions:**
- **Node.js**: v20.13.0+ (Development requirement)
- **Yarn**: v1.22.22+ (Package manager)

**Version Enforcement:**
- **GitHub Actions**: Configured to use Node.js v20.9.0
  ```yaml
  # In .github/workflows/all-branches-pipeline.yml
  - name: Set up Node.js
    uses: actions/setup-node@v4
    with:
      node-version: "20.9.0"
      cache: "yarn"
  ```
- **Local Development**: Developers must ensure correct versions
- **Heroku**: Uses latest stable Node.js version unless specified

### Package Management Configuration

**Yarn Configuration:**
- **Lockfile**: `yarn.lock` ensures consistent dependency versions
- **Package Manager**: Specified in `package.json`:
  ```json
  "packageManager": "yarn@1.22.22"
  ```

**Caching Strategy:**
GitHub Actions implements dependency caching for optimal build performance:

```yaml
- name: Cache Yarn Modules
  id: yarn-cache
  uses: actions/cache@v4
  with:
    path: node_modules
    key: ${{ runner.os }}-node-${{ hashFiles('**/yarn.lock') }}

- name: Install Dependencies
  if: steps.yarn-cache.outputs.cache-hit != 'true'
  run: yarn
```

**Cache Benefits:**
- Reduces build time from ~2-3 minutes to ~30 seconds when cache hits
- Consistent dependency versions across builds
- Reduced network bandwidth usage

## Configuration Management

### Required Environment Variables

The application requires the following environment variables in Heroku:

#### Core Application
- `NODE_ENV`: Set to `"production"` for production deployments
- `PORT`: Automatically provided by Heroku (typically 80/443)
- `mongoUri`: MongoDB Atlas connection string

#### Authentication & Security
- `MAGIC_LINK_JWT_SECRET`: JWT signing secret for magic links
- `MAGIC_LINK_JWT_EXPIRATION`: Token expiration time (e.g., "1h")
- `clinicalNoteKey`: Encryption key for clinical notes (must be 32 bytes)

#### Third-Party Integrations
- `sendgridAPI`: SendGrid API key for email services
- `twilioSID`: Twilio Account SID
- `twilioToken`: Twilio Auth Token
- `twilioNumber`: Twilio phone number for SMS

#### Device Integrations
- `AD_API`: A&D Medical API endpoint
- `AD_USERNAME`: A&D API username
- `AD_PASSWORD`: A&D API password
- `AD_RECEIVING_ENDPOINT`: Webhook endpoint for A&D devices
- `BERRY_RECEIVING_API_TOKEN`: BerryMed API authentication token
- `BODY_TRACE_RECEIVING_API_KEY`: BodyTrace API key name
- `BODY_TRACE_RECEIVING_API_KEY_VALUE`: BodyTrace API key value

#### Monitoring & Analytics
- `SENTRY_DSN`: Sentry project DSN for error monitoring
- `SERVICE_ENV`: Service environment identifier
- `OPENAI_API_KEY`: OpenAI API key for patient assistant
- `OPENAI_ASSISTANT_ID`: OpenAI assistant configuration ID

#### Feature Flags
- `FEATURE_BP_BUDDY_ENABLED`: Enable/disable BP buddy feature (`"true"/"false"`)
- `WEB_APP_ORIGIN`: Frontend application origin URL

### Environment Setup Process

#### 1. Development Environment
Create `.env` file in backend repository:
```bash
# Core
NODE_ENV="development"
mongoUri="mongodb://localhost:27017/cardiowell"

# Add other variables as needed for development
```

#### 2. Heroku Configuration Transfer
1. **Access Heroku Dashboard**:
   ```
   https://dashboard.heroku.com/apps/cardiowell-application
   ```

2. **Navigate to Settings**: Click "Settings" tab

3. **Reveal Config Vars**: Click "Reveal Config Vars" button

4. **Copy Variables**: Transfer all production values to Heroku config vars

5. **Programmatic Setup** (Alternative):
   ```bash
   # Install Heroku CLI first
   heroku config:set NODE_ENV=production -a cardiowell-application
   heroku config:set mongoUri="your-mongodb-atlas-connection-string" -a cardiowell-application
   # Continue for all variables...
   ```

#### 3. Environment-Specific Differences

**Development vs Production:**
- **Database**: Local MongoDB vs MongoDB Atlas
- **URLs**: localhost endpoints vs production domains
- **SSL**: Optional in development, required in production
- **Error Handling**: Detailed errors in development, sanitized in production
- **Caching**: Disabled in development, enabled in production

## CI/CD Pipeline Details

### GitHub Workflows Configuration

Both repositories implement identical CI/CD pipelines with the following characteristics:

#### Pipeline Trigger
```yaml
on:
  push:
    branches:
      - '**'  # Runs on all branch pushes
```

#### Pipeline Jobs

**1. Static Checks Job**
- **Runner**: Ubuntu Latest
- **Purpose**: Code quality assurance before deployment

**2. Checkout & Setup**
```yaml
- name: Checkout Repository
  uses: actions/checkout@v4

- name: Set up Node.js
  uses: actions/setup-node@v4
  with:
    node-version: "20.9.0"
    cache: "yarn"
```

**3. Dependency Management**
```yaml
- name: Cache Yarn Modules
  id: yarn-cache
  uses: actions/cache@v4
  with:
    path: node_modules
    key: ${{ runner.os }}-node-${{ hashFiles('**/yarn.lock') }}

- name: Install Dependencies
  if: steps.yarn-cache.outputs.cache-hit != 'true'
  run: yarn
```

**4. Quality Checks**
```yaml
- name: Lint
  run: yarn lint --quiet  # Backend pipeline
  # OR
  run: yarn lint          # Frontend pipeline
```

### Quality Assurance Process

**Linting Configuration:**
- **Backend**: ESLint with Prettier integration
- **Frontend**: ESLint with React-specific rules
- **Standards**: Enforces code style, potential bugs, and React best practices

**Automated Checks:**
- Code formatting consistency
- Import/export validation
- React hooks usage compliance
- Security vulnerability scanning (via dependencies)

**Manual Steps Required:**
After automated pipeline completion, the following manual steps are required:

1. **Frontend Build**: Manual execution of `yarn build`
2. **Build Integration**: Manual copy of build files to backend
3. **Deployment Trigger**: Manual deployment via Heroku dashboard

## Deployment Process

### Step-by-Step Deployment Guide

#### Prerequisites
- [ ] Both repositories have passing CI/CD pipelines
- [ ] All required environment variables are set in Heroku
- [ ] Access to Heroku dashboard

#### Frontend Deployment Process

1. **Prepare Frontend Build**
   ```bash
   cd patientCareReact-master/
   
   # Ensure dependencies are installed
   yarn install
   
   # Run production build
   yarn build
   ```

2. **Verify Build Output**
   ```bash
   # Check build directory contents
   ls -la build/
   
   # Should contain:
   # - index.html
   # - static/ (CSS, JS, media files)
   # - manifest.json
   # - Asset files
   ```

3. **Integrate with Backend**
   ```bash
   # Copy entire build directory
   cp -r ./build/* ../cardiowell-backend/build/
   
   # Navigate to backend
   cd ../cardiowell-backend/
   
   # Verify integration
   ls -la build/
   ```

#### Backend Deployment Process

1. **Commit Updated Build**
   ```bash
   # Add all build files
   git add build/
   
   # Commit with descriptive message
   git commit -m "Deploy: Update frontend build $(date)"
   
   # Push to master branch
   git push origin master
   ```

2. **Deploy via Heroku Dashboard**
   ```
   1. Navigate to: https://dashboard.heroku.com/apps/cardiowell-application
   2. Click "Deploy" tab
   3. Scroll to "Manual Deploy" section
   4. Select "master" branch from dropdown
   5. Click "Deploy Branch" button
   ```

3. **Monitor Deployment**
   ```
   # Watch deployment logs in real-time
   heroku logs --tail -a cardiowell-application
   
   # Or view recent logs
   heroku logs -a cardiowell-application
   ```

#### Alternative CLI Deployment
```bash
# Install Heroku CLI if not already installed
# Then connect to the app
heroku git:remote -a cardiowell-application

# Deploy directly via Git
git push heroku master
```

### Deployment Timeline
- **Build Process**: 2-5 minutes (depending on cache hits)
- **Heroku Deployment**: 1-3 minutes
- **DNS Propagation**: Immediate (since no domain changes)
- **Total Deployment Time**: 3-8 minutes

## Monitoring & Verification

### Deployment Verification Steps

#### 1. Application Health Check
```bash
# Basic connectivity test
curl https://careportal.cardiowell.io/ping
# Expected response: "pong"

# Check application status
heroku ps -a cardiowell-application
```

#### 2. Frontend Verification
1. **Navigate to Application**: https://careportal.cardiowell.io
2. **Verify Loading**: Ensure React application loads without errors
3. **Check Console**: Open browser dev tools, verify no critical errors
4. **Test Authentication**: Attempt login to verify API connectivity

#### 3. API Endpoint Testing
```bash
# Test API endpoints
curl https://careportal.cardiowell.io/routes/ping

# Test authenticated endpoints (requires valid token)
curl -H "Authorization: Bearer <token>" \
     https://careportal.cardiowell.io/routes/patients
```

### Sentry Error Monitoring Configuration

**Sentry Setup:**
The application includes comprehensive error monitoring through Sentry integration:

```javascript
// Sentry configuration in observability/sentry.mjs
const SENTRY_DSN = process.env.SENTRY_DSN
const SERVICE_ENV = process.env.SERVICE_ENV ?? "unknown"
const NODE_ENV = process.env.NODE_ENV

// Only initialize in production with valid DSN
if (SENTRY_DSN && NODE_ENV === "production" && !["unknown", "local"].includes(SERVICE_ENV)) {
  Sentry.init({
    dsn: SENTRY_DSN,
    environment: SERVICE_ENV,
    // Additional configuration...
  })
}
```

**Monitoring Dashboard:**
- **Access**: Sentry dashboard (URL provided by SENTRY_DSN)
- **Alerts**: Configured for error rate thresholds
- **Performance**: Request duration and error tracking

**Key Metrics to Monitor:**
- Application uptime
- Response times
- Error rates
- Database connection status
- Memory usage
- Active user sessions

### Production Health Indicators

**Green Status:**
- ✅ Application responds to `/ping` endpoint
- ✅ Frontend loads within 3 seconds
- ✅ No critical errors in Sentry dashboard
- ✅ Database connections active
- ✅ All environment variables configured

**Yellow Status (Investigate):**
- ⚠️ Elevated response times (>2 seconds)
- ⚠️ Non-critical errors in logs
- ⚠️ High memory usage (>85%)

**Red Status (Immediate Action Required):**
- ❌ Application unreachable
- ❌ Database connection failures
- ❌ Critical errors preventing user access
- ❌ Missing environment variables

## Troubleshooting

### Common Deployment Issues

#### 1. Build Failures

**Symptom**: Heroku build fails during deployment
```
Error: Cannot find module 'some-package'
```

**Solution**:
```bash
# Verify all dependencies are in package.json
yarn install

# Check for missing dependencies
yarn check

# Update lockfile if needed
yarn install --force
```

#### 2. Environment Variable Issues

**Symptom**: Application starts but features don't work
```
Error: Cannot read property 'mongoUri' of undefined
```

**Solution**:
```bash
# Check Heroku config vars
heroku config -a cardiowell-application

# Add missing variables
heroku config:set MISSING_VAR=value -a cardiowell-application

# Restart application
heroku restart -a cardiowell-application
```

#### 3. Frontend Not Loading

**Symptom**: API works but frontend shows blank page

**Solution**:
1. **Verify Build Integration**:
   ```bash
   # Check if build files exist in backend
   ls -la cardiowell-backend/build/
   ```

2. **Check Static File Serving**:
   ```bash
   # Test static file access
   curl https://careportal.cardiowell.io/static/css/main.css
   ```

3. **Rebuild Frontend**:
   ```bash
   cd patientCareReact-master/
   rm -rf build/ node_modules/
   yarn install
   yarn build
   # Re-integrate with backend
   ```

#### 4. Database Connection Issues

**Symptom**: 
```
Error: connection timeout
```

**Solution**:
```bash
# Check MongoDB Atlas connectivity
# Verify IP whitelist includes Heroku IP ranges
# Test connection string locally
node -e "require('mongoose').connect(process.env.mongoUri)"
```

### Emergency Rollback Procedure

If a deployment causes critical issues:

1. **Immediate Rollback**:
   ```bash
   # Rollback to previous release
   heroku rollback -a cardiowell-application
   ```

2. **Verify Rollback**:
   ```bash
   # Check application status
   curl https://careportal.cardiowell.io/ping
   ```

3. **Investigate Issues**:
   - Review Heroku logs
   - Check Sentry error reports
   - Compare environment variables

4. **Fix and Redeploy**:
   - Address identified issues
   - Test in development environment
   - Redeploy using standard process

## Heroku Version Management

### Heroku Platform Stack

**Current Stack**: Heroku-22 (Ubuntu 22.04)
- **Node.js Runtime**: Latest stable version compatible with specified requirements
- **Automatic Updates**: Heroku automatically updates the runtime for security patches
- **Manual Stack Upgrades**: Required for major version changes

### Version Control Strategy

#### 1. Node.js Version Management
```json
// In package.json - specify exact version if needed
{
  "engines": {
    "node": "20.x",
    "yarn": "1.x"
  }
}
```

#### 2. Dependency Version Pinning
```json
// Use exact versions for critical dependencies
{
  "dependencies": {
    "express": "4.16.3",    // Exact version
    "mongoose": "^5.9.10"   // Compatible version
  }
}
```

#### 3. Heroku Release Management

**Release Tracking**:
```bash
# View deployment history
heroku releases -a cardiowell-application

# Get detailed release info
heroku releases:info v123 -a cardiowell-application
```

**Release Rollback**:
```bash
# Rollback to specific version
heroku rollback v122 -a cardiowell-application
```

### Automated Version Updates

**Dependabot Configuration** (Recommended):
Create `.github/dependabot.yml`:
```yaml
version: 2
updates:
  - package-ecosystem: "npm"
    directory: "/"
    schedule:
      interval: "weekly"
    open-pull-requests-limit: 5
```

### Production Maintenance Windows

**Recommended Schedule**:
- **Minor Updates**: Weekly during low-traffic periods
- **Security Updates**: Immediate for critical vulnerabilities
- **Major Updates**: Quarterly with comprehensive testing

**Maintenance Process**:
1. **Staging Deployment**: Test all changes in staging environment
2. **User Notification**: Inform users of planned maintenance
3. **Deployment**: Execute during agreed maintenance window
4. **Verification**: Comprehensive testing post-deployment
5. **Monitoring**: Enhanced monitoring for 24 hours post-deployment

---

This deployment documentation provides comprehensive coverage of the Cardiowell deployment process. For additional support or questions about specific deployment scenarios, refer to the development team or create issues in the respective GitHub repositories.