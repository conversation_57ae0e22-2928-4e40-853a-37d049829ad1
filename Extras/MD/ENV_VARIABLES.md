# Environment Variables Documentation

## Introduction

Environment variables are crucial for configuring the CardioWell backend application across different environments (development, staging, production). They provide a secure way to manage sensitive information like API keys, database connections, and third-party service credentials without hardcoding them in the source code.

This document lists all environment variables used by the CardioWell backend application, their purposes, affected components, and dependencies.

## Core Application Variables

### Database Configuration

| Variable | Purpose | Component/Functionality | Dependencies |
|----------|---------|------------------------|--------------|
| `mongoUri` | MongoDB connection string | Database connectivity for all models and services | Required for application startup |
| `NODE_ENV` | Application environment mode | Controls development features, HTTPS redirection, error handling | Affects logging, debugging, and security features |

### Server Configuration

| Variable | Purpose | Component/Functionality | Dependencies |
|----------|---------|------------------------|--------------|
| `PORT` | Server port number (defaults to 8081) | Express server configuration | None |
| `WEB_APP_ORIGIN` | Web application origin URL (defaults to http://localhost:3010) | CORS configuration, short URLs, patient authentication | Required for proper frontend-backend communication |

## Authentication & Security

### JWT Configuration

| Variable | Purpose | Component/Functionality | Dependencies |
|----------|---------|------------------------|--------------|
| `MAGIC_LINK_JWT_SECRET` | Secret key for signing magic link JWTs | Patient authentication via magic links | Required for auth/token.mjs |
| `MAGIC_LINK_JWT_EXPIRATION` | JWT token expiration time | Magic link token validity period | Works with MAGIC_LINK_JWT_SECRET |

### Clinical Data Security

| Variable | Purpose | Component/Functionality | Dependencies |
|----------|---------|------------------------|--------------|
| `clinicalNoteKey` | Encryption key for clinical notes | Encrypting/decrypting sensitive clinical data | Required for clinical notes encryption service |

## Third-Party Service Integrations

### Email Services

| Variable | Purpose | Component/Functionality | Dependencies |
|----------|---------|------------------------|--------------|
| `sendgridAPI` | SendGrid API key | Email notifications, patient communications | Required for email functionality |
| `emailSubject` | Default email subject line | Email notifications to patients/providers | Works with sendgridAPI |
| `emailMessage` | Default email message content | Email notifications to patients/providers | Works with sendgridAPI |

### SMS/Twilio Configuration

| Variable | Purpose | Component/Functionality | Dependencies |
|----------|---------|------------------------|--------------|
| `twilioSID` | Twilio account SID | SMS notifications, patient communications | Required for SMS functionality |
| `twilioToken` | Twilio authentication token | SMS service authentication | Works with twilioSID |
| `twilioNumber` | Twilio phone number | SMS sender identification | Required for SMS sending |
| `smsMessage` | Default SMS message content | SMS notifications to patients | Works with Twilio configuration |

### OpenAI Integration

| Variable | Purpose | Component/Functionality | Dependencies |
|----------|---------|------------------------|--------------|
| `OPENAI_API_KEY` | OpenAI API authentication | Patient assistant AI functionality | Required for patient-assistant module |
| `OPENAI_ASSISTANT_ID` | OpenAI assistant identifier | Specific AI assistant configuration | Works with OPENAI_API_KEY |

### Monitoring & Observability

| Variable | Purpose | Component/Functionality | Dependencies |
|----------|---------|------------------------|--------------|
| `SENTRY_DSN` | Sentry error tracking URL | Application error monitoring and reporting | Optional but recommended for production |
| `SERVICE_ENV` | Service environment identifier | Error tracking context, defaults to "unknown" | Works with Sentry configuration |

## Device Manufacturer Integrations

### A&D Medical Devices

| Variable | Purpose | Component/Functionality | Dependencies |
|----------|---------|------------------------|--------------|
| `AD_API` | A&D API base URL | Blood pressure monitor integration | Required for A&D device communication |
| `AD_USERNAME` | A&D API username | Authentication with A&D services | Required with AD_API |
| `AD_PASSWORD` | A&D API password | Authentication with A&D services | Required with AD_API |
| `AD_RECEIVING_ENDPOINT` | Webhook endpoint for A&D data | Receiving measurement data from A&D devices | Required for A&D webhook setup |
| `AD_PROCESSING_MODE` | Data processing mode ("active" or passive) | Controls how A&D messages are processed | Affects device/service/ad/handleMessage.mjs |

### BodyTrace Devices

| Variable | Purpose | Component/Functionality | Dependencies |
|----------|---------|------------------------|--------------|
| `BODY_TRACE_RECEIVING_API_KEY` | API key header name for BodyTrace | Authentication header identification | Required for BodyTrace integration |
| `BODY_TRACE_RECEIVING_API_KEY_VALUE` | API key value for BodyTrace | Authentication with BodyTrace services | Works with BODY_TRACE_RECEIVING_API_KEY |

### Berry Medical Devices

| Variable | Purpose | Component/Functionality | Dependencies |
|----------|---------|------------------------|--------------|
| `BERRY_RECEIVING_API_TOKEN` | Authentication token for Berry devices | Pulse oximeter and other Berry device integration | Required for Berry device communication |

## Testing & Development

### Test Data Management

| Variable | Purpose | Component/Functionality | Dependencies |
|----------|---------|------------------------|--------------|
| `LOAD_TEST_DATA_API_KEY` | API key for loading test data | Bulk device creation and test data loading | Required for test data endpoints |

### Test Forwarding

| Variable | Purpose | Component/Functionality | Dependencies |
|----------|---------|------------------------|--------------|
| `TEST_FORWARD_ENDPOINT` | URL for forwarding test data | Test data forwarding to external systems | Optional, for testing integrations |
| `TEST_FORWARD_API_TOKEN` | Authentication token for test forwarding | Secure test data forwarding | Works with TEST_FORWARD_ENDPOINT |
| `TEST_FORWARD_HEADER_KEY` | Header name for test forwarding auth (defaults to "authorization") | Custom authentication header specification | Works with TEST_FORWARD_API_TOKEN |

### Device Testing Endpoints

| Variable | Purpose | Component/Functionality | Dependencies |
|----------|---------|------------------------|--------------|
| `TEST_RECEIVING_ENDPOINT` | Endpoint for test device messages | Simulating device data reception | Used in device testing services |
| `TEST_RECEIVING_API_TOKEN` | Token for test receiving endpoint | Authentication for test message simulation | Works with TEST_RECEIVING_ENDPOINT |
| `TEST_BERRY_RECEIVING_API_TOKEN` | Token for Berry test endpoint | Berry device message simulation | Used in test simulation |
| `TEST_BERRY_RECEIVING_ENDPOINT` | Endpoint for Berry test messages | Berry device data simulation | Works with TEST_BERRY_RECEIVING_API_TOKEN |
| `TEST_BODY_TRACE_RECEIVING_ENDPOINT` | Endpoint for BodyTrace test messages | BodyTrace device data simulation | Used in test simulation |
| `TEST_BODY_TRACE_RECEIVING_API_TOKEN` | Token for BodyTrace test endpoint | BodyTrace device message simulation | Works with TEST_BODY_TRACE_RECEIVING_ENDPOINT |

## Feature Flags

| Variable | Purpose | Component/Functionality | Dependencies |
|----------|---------|------------------------|--------------|
| `FEATURE_BP_BUDDY_ENABLED` | Enable/disable BP Buddy feature (set to "true" to enable) | Blood pressure monitoring assistant functionality | Controls patient-assistant BP features |

## Frontend-Specific Variables

### React Application (patientCareReact-master)

| Variable | Purpose | Component/Functionality | Dependencies |
|----------|---------|------------------------|--------------|
| `REACT_APP_GOOGLE_MAPS_KEY` | Google Maps API key | Location services in patient profiles | Required for map functionality |
| `API` | Backend API URL for proxy | Development proxy configuration | Used in local-proxy.js |
| `WEB_UI` | Web UI URL for proxy | Development proxy configuration | Used in local-proxy.js |
| `PROXY_PORT` | Proxy server port | Development proxy server | Used in local-proxy.js |
| `PUBLIC_URL` | Public URL for React app | Service worker and asset loading | Used by React build process |

## Configuration Dependencies

### Critical Dependencies
- `mongoUri` is required for application startup
- `twilioSID`, `twilioToken`, and `twilioNumber` must be configured together for SMS functionality
- `OPENAI_API_KEY` and `OPENAI_ASSISTANT_ID` must be configured together for AI assistant features
- Device-specific API credentials must be complete sets (username/password, API keys, endpoints)

### Optional but Recommended
- `SENTRY_DSN` for production error monitoring
- `SERVICE_ENV` for better error tracking context
- Feature flags for gradual feature rollouts

### Environment-Specific Considerations
- Development: `NODE_ENV=development` enables additional debugging features and test routes
- Production: Ensure all security-related variables are properly set and HTTPS is enforced
- Testing: Test-specific endpoints and tokens should only be configured in non-production environments

## Security Notes

1. **Never commit environment variables to version control**
2. **Rotate API keys and tokens regularly**
3. **Use different credentials for different environments**
4. **Ensure database connections use authentication and encryption**
5. **Keep encryption keys secure and backed up safely**
6. **Monitor usage of API keys through respective service dashboards**

## Setup Instructions

1. Copy the `.env.example` file to `.env`
2. Fill in all required variables for your environment
3. Verify database connectivity with your `mongoUri`
4. Test third-party service integrations with their respective API keys
5. Ensure proper firewall and network configurations for webhook endpoints

## Development Server Configuration

### API Values That Need to be Changed for Dev Server

When setting up a development server, you'll need to obtain and configure the following API credentials and endpoints. These values must be changed from any placeholder or production values:

#### **Critical APIs Requiring New Values:**

1. **MongoDB Database**
   - `mongoUri` - Set up a development MongoDB instance (local or cloud)
   - Use a separate database from production

2. **Twilio SMS Service**
   - `twilioSID` - Your Twilio Account SID
   - `twilioToken` - Your Twilio Auth Token  
   - `twilioNumber` - Your Twilio phone number
   - Get these from: https://console.twilio.com/

3. **SendGrid Email Service**
   - `sendgridAPI` - Your SendGrid API key
   - Get this from: https://app.sendgrid.com/settings/api_keys

4. **OpenAI Integration**
   - `OPENAI_API_KEY` - Your OpenAI API key
   - `OPENAI_ASSISTANT_ID` - Your specific assistant ID
   - Get these from: https://platform.openai.com/api-keys

5. **Security Keys**
   - `MAGIC_LINK_JWT_SECRET` - Generate a secure random string
   - `clinicalNoteKey` - Generate a 32-byte encryption key
   - Use strong, unique values for development

#### **Device Manufacturer APIs (if testing device integrations):**

6. **A&D Medical Devices**
   - `AD_API` - A&D API base URL
   - `AD_USERNAME` - Your A&D API username
   - `AD_PASSWORD` - Your A&D API password
   - `AD_RECEIVING_ENDPOINT` - Your webhook endpoint URL

7. **BodyTrace Devices**
   - `BODY_TRACE_RECEIVING_API_KEY` - Header name for authentication
   - `BODY_TRACE_RECEIVING_API_KEY_VALUE` - API key value

8. **Berry Medical Devices**
   - `BERRY_RECEIVING_API_TOKEN` - Authentication token

#### **Optional but Recommended:**

9. **Error Monitoring**
   - `SENTRY_DSN` - Your Sentry project DSN
   - Get this from: https://sentry.io/

10. **Google Maps (for frontend)**
    - `REACT_APP_GOOGLE_MAPS_KEY` - Google Maps API key
    - Get this from: https://console.cloud.google.com/

#### **Development-Specific Endpoints:**
- `WEB_APP_ORIGIN` - Set to your local frontend URL (e.g., http://localhost:3000)
- `PORT` - Set to your preferred backend port (defaults to 8081)
- `NODE_ENV` - Set to "development"

#### **Test Data and Forwarding (optional for dev):**
- `LOAD_TEST_DATA_API_KEY` - For loading test data
- `TEST_FORWARD_ENDPOINT` - For test data forwarding
- `TEST_FORWARD_API_TOKEN` - Authentication for test forwarding

### Quick Start Checklist:
- [ ] Set up MongoDB development database
- [ ] Create Twilio account and get credentials
- [ ] Create SendGrid account and get API key
- [ ] Create OpenAI account and get API key + assistant ID
- [ ] Generate secure JWT secret and encryption key
- [ ] Configure CORS origin for your frontend URL
- [ ] Set NODE_ENV to "development"
- [ ] (Optional) Set up Sentry for error tracking
- [ ] (Optional) Get Google Maps API key for location features

**Note:** For initial development and testing, you can start with just the core APIs (MongoDB, Twilio, SendGrid, OpenAI) and add device manufacturer integrations as needed.

## Verification Notes

This documentation has been comprehensively verified against the entire codebase as of the latest analysis. All environment variables listed are actively used in the application code, and their descriptions accurately reflect their implementation and dependencies.

### Hardcoded Configurations (Not Environment Variables)

The following are **NOT** environment variables but are worth noting for developers:

- **Withings Client Credentials**: Hardcoded in `withings/utils/client.mjs` (both production and development)
- **Test Data API Key**: Hardcoded in `scripts/testDataLoader.mjs` 
- **Session Secret**: Hardcoded as "fraggle-rock" in `app.mjs` (consider making this an environment variable for production)

### Missing Environment Variables

If you encounter references to environment variables not listed here, they may be:
1. Legacy variables no longer in use
2. Optional variables with sensible defaults
3. Variables specific to your deployment environment

### Validation Checklist

Before deployment, ensure:
- [ ] All **Critical APIs** are configured with valid credentials
- [ ] Database connection string is properly formatted and accessible
- [ ] Webhook endpoints are publicly accessible (for device integrations)
- [ ] JWT secrets are cryptographically secure (minimum 32 characters)
- [ ] Encryption keys are exactly 32 bytes for clinical note encryption
- [ ] Feature flags are set according to your deployment requirements