# Cardiowell Comprehensive Deployment Documentation

## Table of Contents
1. [Overview](#overview)
2. [Prerequisites & Environment Setup](#prerequisites--environment-setup)
3. [Git-Based Deployment Architecture](#git-based-deployment-architecture)
4. [Environment & Dependency Management](#environment--dependency-management)
5. [Configuration Management](#configuration-management)
6. [CI/CD Pipeline Details](#cicd-pipeline-details)
7. [Step-by-Step Deployment Process](#step-by-step-deployment-process)
8. [Development Environment Deployment](#development-environment-deployment)
9. [Verification and Testing](#verification-and-testing)
10. [Monitoring & Error Tracking](#monitoring--error-tracking)
11. [Troubleshooting](#troubleshooting)
12. [Rollback Procedures](#rollback-procedures)
13. [Development vs Production Differences](#development-vs-production-differences)
14. [Heroku Version Management](#heroku-version-management)

## Overview

Cardiowell uses **Heroku's Git-based deployment with buildpacks** rather than containerized deployment. This approach leverages Heroku's automatic runtime management for Node.js applications, eliminating the need for Docker configuration while providing robust scalability and deployment automation.

### Deployment Architecture Summary
- **Frontend**: React SPA built and integrated into backend repository
- **Backend**: Node.js/Express API server that serves both API endpoints and compiled frontend
- **Platform**: Heroku with Node.js buildpack
- **Database**: MongoDB Atlas (production) / Local MongoDB (development)
- **Monitoring**: Sentry for error tracking and performance monitoring

### Dual-Repository Architecture
The Cardiowell project uses a dual-repository architecture where the React frontend is built separately and then integrated with the Node.js backend for deployment:
- **Frontend Repository**: `patientCareReact-master/` (React application)
- **Backend Repository**: `cardiowell-backend/` (Node.js/Express API + serves frontend)
- **Deployment Method**: Git-based with Node.js buildpack

## Prerequisites & Environment Setup

### Required Tools
Before starting any deployment process, ensure you have the following tools installed:

```bash
# Check Node.js version (required: v20.13.0+)
node --version

# Check Yarn version (required: v1.22.22+)
yarn --version

# Check Git
git --version

# Install Heroku CLI if not already installed
# macOS
brew tap heroku/brew && brew install heroku

# Ubuntu/Debian
curl https://cli-assets.heroku.com/install-ubuntu.sh | sh

# Windows
# Download from https://devcenter.heroku.com/articles/heroku-cli

# Verify Heroku CLI installation
heroku --version
```

### Required Access and Permissions

1. **Git Repository Access**
   - Read/write access to both `cardiowell-backend` and `patientCareReact-master` repositories
   - SSH key configured for Git operations

2. **Heroku Access**
   - Heroku account with access to the target application
   - Collaborator permissions on the Heroku application

3. **Environment Variables**
   - Access to environment configuration values
   - Database connection strings for target environment

### Verify Access
```bash
# Test Git access
<NAME_EMAIL>:Cardiowell/cardiowell-backend.git
<NAME_EMAIL>:Cardiowell/patientCareReact-master.git

# Test Heroku access
heroku auth:login
heroku apps --team your-team-name
```

### Initial Repository Setup

```bash
# Create workspace directory
mkdir cardiowell-workspace
cd cardiowell-workspace

# Clone both repositories
<NAME_EMAIL>:Cardiowell/cardiowell-backend.git
<NAME_EMAIL>:Cardiowell/patientCareReact-master.git

# Setup backend
cd cardiowell-backend
yarn install
cd ..

# Setup frontend
cd patientCareReact-master
yarn install
cd ..
```

## Git-Based Deployment Architecture

### Repository Structure
The Cardiowell project consists of two main repositories that work together in the deployment process:

```
cardiowell/
├── cardiowell-backend/          # Main deployment repository
│   ├── build/                   # Compiled React frontend
│   ├── app.mjs                  # Main server entry point
│   ├── package.json             # Backend dependencies & scripts
│   └── .github/workflows/       # Backend CI/CD pipeline
└── patientCareReact-master/     # Frontend development repository
    ├── src/                     # React source code
    ├── package.json             # Frontend dependencies & build scripts
    └── .github/workflows/       # Frontend CI/CD pipeline
```

### Complete Deployment Workflow

#### Phase 1: Frontend Development & Build
1. **Development**: Frontend developers work in `patientCareReact-master/`
2. **CI/CD Pipeline**: GitHub Actions run linting and quality checks
3. **Build Process**: When ready for deployment, run:
   ```bash
   cd patientCareReact-master/
   yarn build
   ```
4. **Build Output**: Creates optimized production build in `./build/` directory

#### Phase 2: Frontend Integration
1. **Copy Build**: Transfer the entire `./build/` folder from frontend to backend repository:
   ```bash
   # From patientCareReact-master/ directory
   cp -r ./build/* ../cardiowell-backend/build/
   ```
2. **Commit Changes**: Add the updated build to the backend repository:
   ```bash
   cd ../cardiowell-backend/
   git add build/
   git commit -m "Update frontend build for deployment"
   git push origin master
   ```

#### Phase 3: Backend Deployment
1. **Heroku Detection**: Heroku automatically detects Node.js project from `package.json`
2. **Buildpack Selection**: Uses Node.js buildpack (automatic)
3. **Dependency Installation**: Runs `yarn install` using lockfile
4. **Application Start**: Executes `yarn start` → `node app.mjs`

### Frontend-Backend Integration Details

The backend serves the compiled frontend through Express static file serving:

```javascript
// In app.mjs
app.use(express.static(path.join(__dirname, "/build")))

// SPA fallback route
app.get("/*", function (req, res) {
  res.sendFile(path.join(import.meta.dirname, "/build", "index.html"))
})
```

This configuration allows the single Heroku application to serve:
- **API endpoints**: `/routes/*` paths handled by Express routers
- **Static assets**: CSS, JS, images from `/build/static/`
- **SPA routing**: All other paths serve the React application

## Environment & Dependency Management

### Node.js Version Requirements

**Required Versions:**
- **Node.js**: v20.13.0+ (Development requirement)
- **Yarn**: v1.22.22+ (Package manager)

**Version Enforcement:**
- **GitHub Actions**: Configured to use Node.js v20.9.0
  ```yaml
  # In .github/workflows/all-branches-pipeline.yml
  - name: Set up Node.js
    uses: actions/setup-node@v4
    with:
      node-version: "20.9.0"
      cache: "yarn"
  ```
- **Local Development**: Developers must ensure correct versions
- **Heroku**: Uses latest stable Node.js version unless specified

### Package Management Configuration

**Yarn Configuration:**
- **Lockfile**: `yarn.lock` ensures consistent dependency versions
- **Package Manager**: Specified in `package.json`:
  ```json
  "packageManager": "yarn@1.22.22"
  ```

**Caching Strategy:**
GitHub Actions implements dependency caching for optimal build performance:

```yaml
- name: Cache Yarn Modules
  id: yarn-cache
  uses: actions/cache@v4
  with:
    path: node_modules
    key: ${{ runner.os }}-node-${{ hashFiles('**/yarn.lock') }}

- name: Install Dependencies
  if: steps.yarn-cache.outputs.cache-hit != 'true'
  run: yarn
```

**Cache Benefits:**
- Reduces build time from ~2-3 minutes to ~30 seconds when cache hits
- Consistent dependency versions across builds
- Reduced network bandwidth usage

## Configuration Management

### Required Environment Variables

The application requires the following environment variables in Heroku:

#### Core Application
- `NODE_ENV`: Set to `"production"` for production deployments, `"development"` for development
- `PORT`: Automatically provided by Heroku (typically 80/443)
- `mongoUri`: MongoDB Atlas connection string

#### Authentication & Security
- `MAGIC_LINK_JWT_SECRET`: JWT signing secret for magic links
- `MAGIC_LINK_JWT_EXPIRATION`: Token expiration time (e.g., "1h")
- `clinicalNoteKey`: Encryption key for clinical notes (must be 32 bytes)

#### Third-Party Integrations
- `sendgridAPI`: SendGrid API key for email services
- `twilioSID`: Twilio Account SID
- `twilioToken`: Twilio Auth Token
- `twilioNumber`: Twilio phone number for SMS

#### Device Integrations
- `AD_API`: A&D Medical API endpoint
- `AD_USERNAME`: A&D API username
- `AD_PASSWORD`: A&D API password
- `AD_RECEIVING_ENDPOINT`: Webhook endpoint for A&D devices
- `AD_PROCESSING_MODE`: Processing mode for A&D devices
- `BERRY_RECEIVING_API_TOKEN`: BerryMed API authentication token
- `BODY_TRACE_RECEIVING_API_KEY`: BodyTrace API key name
- `BODY_TRACE_RECEIVING_API_KEY_VALUE`: BodyTrace API key value

#### Monitoring & Analytics
- `SENTRY_DSN`: Sentry project DSN for error monitoring
- `SERVICE_ENV`: Service environment identifier
- `OPENAI_API_KEY`: OpenAI API key for patient assistant
- `OPENAI_ASSISTANT_ID`: OpenAI assistant configuration ID

#### Feature Flags
- `FEATURE_BP_BUDDY_ENABLED`: Enable/disable BP buddy feature (`"true"/"false"`)
- `WEB_APP_ORIGIN`: Frontend application origin URL

#### Testing and Development
- `LOAD_TEST_DATA_API_KEY`: API key for loading test data
- `TEST_FORWARD_ENDPOINT`: Test forwarding endpoint
- `TEST_FORWARD_API_TOKEN`: Test forwarding API token
- `TEST_FORWARD_HEADER_KEY`: Test forwarding header key

### Environment Setup Process

#### 1. Development Environment
Create `.env` file in backend repository:
```bash
cd cardiowell-backend
touch .env
```

Add development environment variables to `.env`:
```bash
# Core Application Settings
NODE_ENV=development
PORT=8081

# Database Configuration (Development)
mongoUri=mongodb://localhost:27017/cardiowell-dev
# OR use a development MongoDB Atlas instance
# mongoUri=mongodb+srv://dev-user:<EMAIL>/cardiowell-dev

# Authentication & Security (Development Keys)
MAGIC_LINK_JWT_SECRET=your-dev-jwt-secret-key
MAGIC_LINK_JWT_EXPIRATION=1h
clinicalNoteKey=your-32-byte-development-encryption-key

# Email Service (Development/Testing)
sendgridAPI=your-sendgrid-api-key-for-dev

# SMS Service (Development/Testing)
twilioSID=your-twilio-dev-sid
twilioToken=your-twilio-dev-token
twilioNumber=your-twilio-dev-number

# Device Integration APIs (Development Endpoints)
AD_API=https://dev-api.ad-medical.com
AD_USERNAME=dev-username
AD_PASSWORD=dev-password
AD_RECEIVING_ENDPOINT=https://your-dev-app.herokuapp.com/

# BerryMed Integration
BERRY_RECEIVING_API_TOKEN=dev-berry-token

# BodyTrace Integration
BODY_TRACE_RECEIVING_API_KEY=dev-bodytrace-key
BODY_TRACE_RECEIVING_API_KEY_VALUE=dev-bodytrace-value

# Monitoring (Development)
SENTRY_DSN=your-sentry-dev-dsn
SERVICE_ENV=development

# AI Assistant (Development)
OPENAI_API_KEY=your-openai-dev-key
OPENAI_ASSISTANT_ID=your-dev-assistant-id

# Feature Flags
FEATURE_BP_BUDDY_ENABLED=true

# Application Origin
WEB_APP_ORIGIN=https://your-dev-app.herokuapp.com
```

#### 2. Heroku Configuration Transfer

**Production Environment:**
1. **Access Heroku Dashboard**:
   ```
   https://dashboard.heroku.com/apps/cardiowell-application
   ```

2. **Navigate to Settings**: Click "Settings" tab

3. **Reveal Config Vars**: Click "Reveal Config Vars" button

4. **Copy Variables**: Transfer all production values to Heroku config vars

**Development Environment:**
```bash
# Set development environment variables
heroku config:set NODE_ENV=development -a your-dev-app
heroku config:set mongoUri="your-development-mongodb-connection-string" -a your-dev-app
heroku config:set MAGIC_LINK_JWT_SECRET="your-dev-jwt-secret" -a your-dev-app
heroku config:set clinicalNoteKey="your-32-byte-dev-encryption-key" -a your-dev-app
# Continue setting all other variables...

# Verify all variables are set
heroku config -a your-dev-app
```

**Programmatic Setup** (Alternative):
```bash
# Install Heroku CLI first
heroku config:set NODE_ENV=production -a cardiowell-application
heroku config:set mongoUri="your-mongodb-atlas-connection-string" -a cardiowell-application
# Continue for all variables...
```

#### 3. Environment-Specific Differences

**Development vs Production:**
- **Database**: Local MongoDB vs MongoDB Atlas
- **URLs**: localhost endpoints vs production domains
- **SSL**: Optional in development, required in production
- **Error Handling**: Detailed errors in development, sanitized in production
- **Caching**: Disabled in development, enabled in production

## CI/CD Pipeline Details

### GitHub Workflows Configuration

Both repositories implement identical CI/CD pipelines with the following characteristics:

#### Pipeline Trigger
```yaml
on:
  push:
    branches:
      - '**'  # Runs on all branch pushes
```

#### Pipeline Jobs

**1. Static Checks Job**
- **Runner**: Ubuntu Latest
- **Purpose**: Code quality assurance before deployment

**2. Checkout & Setup**
```yaml
- name: Checkout Repository
  uses: actions/checkout@v4

- name: Set up Node.js
  uses: actions/setup-node@v4
  with:
    node-version: "20.9.0"
    cache: "yarn"
```

**3. Dependency Management**
```yaml
- name: Cache Yarn Modules
  id: yarn-cache
  uses: actions/cache@v4
  with:
    path: node_modules
    key: ${{ runner.os }}-node-${{ hashFiles('**/yarn.lock') }}

- name: Install Dependencies
  if: steps.yarn-cache.outputs.cache-hit != 'true'
  run: yarn
```

**4. Quality Checks**
```yaml
- name: Lint
  run: yarn lint --quiet  # Backend pipeline
  # OR
  run: yarn lint          # Frontend pipeline
```

### Quality Assurance Process

**Linting Configuration:**
- **Backend**: ESLint with Prettier integration
- **Frontend**: ESLint with React-specific rules
- **Standards**: Enforces code style, potential bugs, and React best practices

**Automated Checks:**
- Code formatting consistency
- Import/export validation
- React hooks usage compliance
- Security vulnerability scanning (via dependencies)

**Manual Steps Required:**
After automated pipeline completion, the following manual steps are required:

1. **Frontend Build**: Manual execution of `yarn build`
2. **Build Integration**: Manual copy of build files to backend
3. **Deployment Trigger**: Manual deployment via Heroku dashboard

## Step-by-Step Deployment Process

### Prerequisites Checklist
- [ ] Both repositories have passing CI/CD pipelines
- [ ] All required environment variables are set in Heroku
- [ ] Access to Heroku dashboard or CLI
- [ ] Clean working directories in both repositories

### General Deployment Process

#### Frontend Deployment Process

1. **Prepare Frontend Build**
   ```bash
   cd patientCareReact-master/
   
   # Ensure dependencies are installed
   yarn install
   
   # Run production build
   yarn build
   ```

2. **Verify Build Output**
   ```bash
   # Check build directory contents
   ls -la build/
   
   # Should contain:
   # - index.html
   # - static/ (CSS, JS, media files)
   # - manifest.json
   # - Asset files
   ```

3. **Integrate with Backend**
   ```bash
   # Copy entire build directory
   cp -r ./build/* ../cardiowell-backend/build/
   
   # Navigate to backend
   cd ../cardiowell-backend/
   
   # Verify integration
   ls -la build/
   ```

#### Backend Deployment Process

1. **Commit Updated Build**
   ```bash
   # Add all build files
   git add build/
   
   # Commit with descriptive message
   git commit -m "Deploy: Update frontend build $(date)"
   
   # Push to master branch
   git push origin master
   ```

2. **Deploy via Heroku Dashboard**
   ```
   1. Navigate to: https://dashboard.heroku.com/apps/cardiowell-application
   2. Click "Deploy" tab
   3. Scroll to "Manual Deploy" section
   4. Select "master" branch from dropdown
   5. Click "Deploy Branch" button
   ```

3. **Monitor Deployment**
   ```
   # Watch deployment logs in real-time
   heroku logs --tail -a cardiowell-application
   
   # Or view recent logs
   heroku logs -a cardiowell-application
   ```

#### Alternative CLI Deployment
```bash
# Install Heroku CLI if not already installed
# Then connect to the app
heroku git:remote -a cardiowell-application

# Deploy directly via Git
git push heroku master
```

### Deployment Timeline
- **Build Process**: 2-5 minutes (depending on cache hits)
- **Heroku Deployment**: 1-3 minutes
- **DNS Propagation**: Immediate (since no domain changes)
- **Total Deployment Time**: 3-8 minutes

## Development Environment Deployment

### Setup Development Heroku App

If a development Heroku app doesn't exist, create one:

```bash
# Login to Heroku
heroku auth:login

# Create new Heroku app for development
heroku create cardiowell-dev-[your-name] --region us

# Or connect to existing development app
heroku git:remote -a cardiowell-dev-existing-app-name

# Verify connection
heroku apps:info
```

### Development-Specific Deployment Process

#### Phase 1: Prepare Frontend Build for Development

1. **Navigate to Frontend Repository**
   ```bash
   cd patientCareReact-master
   ```

2. **Ensure Clean Working Directory**
   ```bash
   # Check for uncommitted changes
   git status
   
   # Commit any pending changes
   git add .
   git commit -m "Development changes for deployment"
   ```

3. **Install Dependencies (if needed)**
   ```bash
   # Clear node_modules if having issues
   rm -rf node_modules package-lock.json
   
   # Install fresh dependencies
   yarn install
   ```

4. **Build Production Frontend**
   ```bash
   # Run production build
   yarn build
   
   # Verify build completed successfully
   echo "Build exit code: $?"
   
   # Check build output
   ls -la build/
   ```

5. **Verify Build Contents**
   ```bash
   # Ensure required files exist
   ls build/index.html
   ls build/static/css/
   ls build/static/js/
   ls build/manifest.json
   
   # Check build size (should be reasonable)
   du -sh build/
   ```

#### Phase 2: Integrate Frontend with Backend

1. **Navigate to Backend Repository**
   ```bash
   cd ../cardiowell-backend
   ```

2. **Backup Current Build (Optional)**
   ```bash
   # Create backup of current build
   cp -r build/ build-backup-$(date +%Y%m%d-%H%M%S)/
   ```

3. **Clear Existing Build**
   ```bash
   # Remove old build files
   rm -rf build/*
   ```

4. **Copy New Frontend Build**
   ```bash
   # Copy all build files from frontend to backend
   cp -r ../patientCareReact-master/build/* ./build/
   
   # Verify copy was successful
   ls -la build/
   ```

5. **Verify Integration**
   ```bash
   # Check that index.html exists
   cat build/index.html | head -n 5
   
   # Verify static assets
   ls build/static/
   ```

#### Phase 3: Deploy to Development Heroku

1. **Commit Frontend Build Changes**
   ```bash
   # Check what files changed
   git status
   
   # Add all build files
   git add build/
   
   # Commit with descriptive message
   git commit -m "Deploy: Update frontend build for development - $(date)"
   ```

2. **Push to Development Branch**
   ```bash
   # Push to development branch (or master for dev environment)
   git push origin master
   
   # Or if using a development branch:
   # git push origin development
   ```

3. **Deploy to Heroku**

   **Option A: Deploy via Heroku CLI (Recommended)**
   ```bash
   # Deploy directly to Heroku
   git push heroku master
   
   # Monitor deployment in real-time
   heroku logs --tail
   ```

   **Option B: Deploy via Heroku Dashboard**
   ```bash
   # If using dashboard deployment:
   echo "Navigate to: https://dashboard.heroku.com/apps/your-dev-app-name"
   echo "1. Click 'Deploy' tab"
   echo "2. Scroll to 'Manual Deploy'"
   echo "3. Select 'master' branch"
   echo "4. Click 'Deploy Branch'"
   ```

4. **Monitor Deployment Progress**
   ```bash
   # Watch deployment logs
   heroku logs --tail
   
   # Check build progress
   heroku builds:info
   
   # View recent activity
   heroku activity
   ```

#### Phase 4: Post-Deployment Tasks

1. **Wait for Deployment Completion**
   ```bash
   # Check application status
   heroku ps
   
   # Ensure web dyno is running
   heroku ps:scale web=1
   ```

2. **Restart Application (if needed)**
   ```bash
   # Restart all dynos
   heroku restart
   
   # Wait for restart to complete
   sleep 10
   ```

## Verification and Testing

### 1. Basic Connectivity Tests

#### Production Environment
```bash
# Basic connectivity test
curl https://careportal.cardiowell.io/ping
# Expected response: "pong"

# Check application status
heroku ps -a cardiowell-application
```

#### Development Environment
```bash
# Get your Heroku app URL
HEROKU_URL=$(heroku apps:info -s | grep web_url | cut -d= -f2)
echo "App URL: $HEROKU_URL"

# Test basic connectivity
curl -f "${HEROKU_URL}ping" || echo "Ping test failed"

# Test with expected response
PING_RESPONSE=$(curl -s "${HEROKU_URL}ping")
if [ "$PING_RESPONSE" = "pong" ]; then
    echo "✅ Ping test successful"
else
    echo "❌ Ping test failed. Response: $PING_RESPONSE"
fi
```

### 2. Frontend Application Tests

#### Production
1. **Navigate to Application**: https://careportal.cardiowell.io
2. **Verify Loading**: Ensure React application loads without errors
3. **Check Console**: Open browser dev tools, verify no critical errors
4. **Test Authentication**: Attempt login to verify API connectivity

#### Development
```bash
# Test that frontend loads
curl -s "$HEROKU_URL" | grep -q "CardioWell" && echo "✅ Frontend loads" || echo "❌ Frontend load failed"

# Test static assets
curl -s -o /dev/null -w "%{http_code}" "${HEROKU_URL}static/css/" | grep -q "200" && echo "✅ CSS assets accessible" || echo "❌ CSS assets failed"

# Test manifest file
curl -s -o /dev/null -w "%{http_code}" "${HEROKU_URL}manifest.json" | grep -q "200" && echo "✅ Manifest accessible" || echo "❌ Manifest failed"
```

### 3. API Endpoint Testing

#### Production
```bash
# Test API endpoints
curl https://careportal.cardiowell.io/routes/ping

# Test authenticated endpoints (requires valid token)
curl -H "Authorization: Bearer <token>" \
     https://careportal.cardiowell.io/routes/patients
```

#### Development
```bash
# Test API endpoints (these might require authentication)
echo "Testing API endpoints..."

# Test public endpoints if any exist
curl -s -o /dev/null -w "Status: %{http_code}" "${HEROKU_URL}routes/ping"
echo ""

# List available endpoints (if there's a discovery endpoint)
# curl -s "${HEROKU_URL}routes/" 2>/dev/null | head -n 20
```

### 4. Browser Testing Checklist

Open your browser and navigate to your application URL:

1. **Frontend Loading**
   - [ ] React application loads without errors
   - [ ] No console errors in browser developer tools
   - [ ] All CSS styles are applied correctly
   - [ ] Images and icons load properly

2. **Navigation Testing**
   - [ ] Client-side routing works (refresh any page)
   - [ ] Navigation between different sections works
   - [ ] Back/forward browser buttons work correctly

3. **Authentication Testing**
   - [ ] Login page loads
   - [ ] Can attempt login (even if credentials are test)
   - [ ] Error handling works for invalid credentials

4. **API Connectivity**
   - [ ] Frontend can communicate with backend API
   - [ ] No CORS errors in browser console
   - [ ] API responses are received and processed

### 5. Database Connectivity Test

```bash
# Check database connection (requires app to be running)
heroku logs --tail | grep -i "mongodb\|database\|connection" &
GREP_PID=$!

# Trigger a database operation by accessing the app
curl -s "$HEROKU_URL" > /dev/null

# Wait a moment for logs
sleep 5

# Stop grep and check if we saw database connection logs
kill $GREP_PID 2>/dev/null
```

## Monitoring & Error Tracking

### Sentry Error Monitoring Configuration

**Sentry Setup:**
The application includes comprehensive error monitoring through Sentry integration:

```javascript
// Sentry configuration in observability/sentry.mjs
const SENTRY_DSN = process.env.SENTRY_DSN
const SERVICE_ENV = process.env.SERVICE_ENV ?? "unknown"
const NODE_ENV = process.env.NODE_ENV

// Only initialize in production with valid DSN
if (SENTRY_DSN && NODE_ENV === "production" && !["unknown", "local"].includes(SERVICE_ENV)) {
  Sentry.init({
    dsn: SENTRY_DSN,
    environment: SERVICE_ENV,
    integrations: [nodeProfilingIntegration(), Sentry.expressIntegration()],
    tracesSampleRate: 1.0, //  Capture 100% of the transactions
    // Additional configuration...
  })
}
```

**Monitoring Dashboard:**
- **Access**: Sentry dashboard (URL provided by SENTRY_DSN)
- **Alerts**: Configured for error rate thresholds
- **Performance**: Request duration and error tracking

**Key Metrics to Monitor:**
- Application uptime
- Response times
- Error rates
- Database connection status
- Memory usage
- Active user sessions

### Production Health Indicators

**Green Status:**
- ✅ Application responds to `/ping` endpoint
- ✅ Frontend loads within 3 seconds
- ✅ No critical errors in Sentry dashboard
- ✅ Database connections active
- ✅ All environment variables configured

**Yellow Status (Investigate):**
- ⚠️ Elevated response times (>2 seconds)
- ⚠️ Non-critical errors in logs
- ⚠️ High memory usage (>85%)

**Red Status (Immediate Action Required):**
- ❌ Application unreachable
- ❌ Database connection failures
- ❌ Critical errors preventing user access
- ❌ Missing environment variables

## Troubleshooting

### Common Deployment Issues

#### 1. Build Failures

**Symptom**: Heroku build fails during deployment
```
Error: Cannot find module 'some-package'
remote: ERROR: Cannot find module 'some-package'
```

**Solution**:
```bash
# Check package.json for missing dependencies
cd patientCareReact-master
yarn check

# Rebuild lock file
rm yarn.lock
yarn install

# In backend
cd ../cardiowell-backend
yarn check
rm yarn.lock
yarn install

# Verify all dependencies are in package.json
yarn install

# Check for missing dependencies
yarn check

# Update lockfile if needed
yarn install --force

# Redeploy
git add yarn.lock
git commit -m "Fix: Update yarn lockfile"
git push heroku master
```

#### 2. Environment Variable Issues

**Symptom**: Application starts but features don't work
```
Error: Cannot read property 'mongoUri' of undefined
```

**Solution**:
```bash
# Check Heroku config vars
heroku config -a cardiowell-application

# Compare with required variables
heroku config | grep -E "(NODE_ENV|mongoUri|JWT_SECRET)" || echo "Missing critical variables"

# Add missing variables
heroku config:set MISSING_VAR=value -a cardiowell-application

# Restart application
heroku restart -a cardiowell-application
```

#### 3. Frontend Not Loading

**Symptom**: API works but frontend shows blank page

**Solution**:
1. **Verify Build Integration**:
   ```bash
   # Check if build files exist in backend
   ls -la cardiowell-backend/build/
   
   # Verify build files were copied correctly
   cd ../cardiowell-backend
   ls -la build/
   cat build/index.html | head

   # Check if index.html contains expected content
   grep -q "CardioWell" build/index.html && echo "✅ Valid build" || echo "❌ Invalid build"
   ```

2. **Check Static File Serving**:
   ```bash
   # Test static file access
   curl https://careportal.cardiowell.io/static/css/main.css
   ```

3. **Rebuild Frontend**:
   ```bash
   cd patientCareReact-master/
   rm -rf build/ node_modules/
   yarn install
   yarn build
   # Re-integrate with backend
   ```

#### 4. Frontend Build Issues

**Issue**: Frontend build fails or is incomplete
```bash
# Clean build and retry
cd patientCareReact-master
rm -rf build/ node_modules/
yarn install
yarn build

# Check for build errors
echo "Build exit code: $?"
```

#### 5. Database Connection Issues

**Symptom**: 
```
Error: connection timeout
```

**Solution**:
```bash
# Check MongoDB Atlas connectivity
# Verify IP whitelist includes Heroku IP ranges
# Test connection string locally
node -e "require('mongoose').connect(process.env.mongoUri)"

# Check database connectivity from Heroku
heroku run node -e "
const mongoose = require('mongoose');
mongoose.connect(process.env.mongoUri)
  .then(() => console.log('✅ Database connected'))
  .catch(err => console.log('❌ Database error:', err.message));
"

# For MongoDB Atlas, check IP whitelist
echo "Ensure Heroku IP ranges are whitelisted in MongoDB Atlas"
echo "Add 0.0.0.0/0 for development (not recommended for production)"
```

#### 6. Memory or Performance Issues

```bash
# Check dyno status and memory usage
heroku ps
heroku logs --tail | grep -i "memory\|error"

# Scale up dyno if needed
heroku ps:scale web=1:standard-1x
```

### Debugging Commands

```bash
# View detailed logs
heroku logs --tail --dyno=web

# Access Heroku bash shell
heroku run bash

# Check environment variables inside dyno
heroku run env

# Test database connection inside dyno
heroku run node -e "console.log('Testing connection...'); require('mongoose').connect(process.env.mongoUri)"

# Check file system state
heroku run "ls -la build/ && cat build/index.html | head"
```

## Rollback Procedures

### 1. Quick Rollback (Emergency)

If the deployment causes critical issues:

```bash
# View recent releases
heroku releases

# Rollback to previous release
heroku rollback v[previous-version-number]

# Example: heroku rollback v42

# Verify rollback
curl "$HEROKU_URL/ping"
# OR for production
curl https://careportal.cardiowell.io/ping
```

### 2. Git-Based Rollback

If you need to rollback to a specific commit:

```bash
# View commit history
git log --oneline -10

# Reset to previous commit (replace COMMIT_HASH)
git reset --hard COMMIT_HASH

# Force push to Heroku
git push heroku master --force

# Or create a revert commit (safer)
git revert HEAD
git push heroku master
```

### 3. Frontend-Only Rollback

If only the frontend build needs to be rolled back:

```bash
# Restore from backup
cd cardiowell-backend
rm -rf build/*
cp -r build-backup-TIMESTAMP/* build/

# Commit and deploy
git add build/
git commit -m "Rollback: Restore previous frontend build"
git push heroku master
```

### 4. Rollback Verification

After any rollback:

```bash
# Test basic functionality
curl "$HEROKU_URL/ping"

# Check application logs
heroku logs --tail

# Test frontend loading
curl -s "$HEROKU_URL" | grep -q "CardioWell" && echo "✅ Frontend OK" || echo "❌ Frontend issue"

# Monitor for a few minutes
heroku logs --tail | head -n 50
```

### Emergency Rollback Procedure

If a deployment causes critical issues:

1. **Immediate Rollback**:
   ```bash
   # Rollback to previous release
   heroku rollback -a cardiowell-application
   ```

2. **Verify Rollback**:
   ```bash
   # Check application status
   curl https://careportal.cardiowell.io/ping
   ```

3. **Investigate Issues**:
   - Review Heroku logs
   - Check Sentry error reports
   - Compare environment variables

4. **Fix and Redeploy**:
   - Address identified issues
   - Test in development environment
   - Redeploy using standard process

## Development vs Production Differences

### Environment Variables

| Variable | Development | Production |
|----------|-------------|------------|
| `NODE_ENV` | `development` | `production` |
| `mongoUri` | Local/Dev MongoDB | MongoDB Atlas Production |
| Database Name | `cardiowell-dev` | `cardiowell-prod` |
| API Endpoints | Development APIs | Production APIs |
| `SENTRY_DSN` | Development project | Production project |
| `SERVICE_ENV` | `development` | `production` |

### Application Behavior Differences

#### Development Environment
- **Error Handling**: Detailed error messages shown
- **Logging**: Verbose logging enabled
- **Caching**: Disabled for easier debugging
- **SSL**: Not strictly required
- **Rate Limiting**: Relaxed or disabled
- **Email/SMS**: Test credentials or sandbox mode

#### Production Environment
- **Error Handling**: Sanitized error messages
- **Logging**: Essential logs only
- **Caching**: Enabled for performance
- **SSL**: Required (HTTPS enforcement)
- **Rate Limiting**: Strict limits enforced
- **Email/SMS**: Production credentials

### Testing Approach

#### Development Testing
```bash
# Use test data
heroku run node scripts/testDataLoader.mjs --url $HEROKU_URL --test-mode

# Test with dummy credentials
curl -X POST "$HEROKU_URL/routes/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username":"<EMAIL>","password":"testpass"}'
```

#### Performance Considerations

```bash
# Development: Single dyno is usually sufficient
heroku ps:scale web=1:hobby

# Check dyno usage
heroku ps
heroku logs --tail | grep -i "memory"
```

### Security Considerations for Development

1. **Never use production secrets in development**
2. **Use separate database instances**
3. **Enable additional logging for debugging**
4. **Use test API keys where possible**
5. **Document all development-specific configurations**

### Environment-Specific Differences

**Development vs Production:**
- **Database**: Local MongoDB vs MongoDB Atlas
- **URLs**: localhost endpoints vs production domains
- **SSL**: Optional in development, required in production
- **Error Handling**: Detailed errors in development, sanitized in production
- **Caching**: Disabled in development, enabled in production

## Heroku Version Management

### Heroku Platform Stack

**Current Stack**: Heroku-22 (Ubuntu 22.04)
- **Node.js Runtime**: Latest stable version compatible with specified requirements
- **Automatic Updates**: Heroku automatically updates the runtime for security patches
- **Manual Stack Upgrades**: Required for major version changes

### Version Control Strategy

#### 1. Node.js Version Management
```json
// In package.json - specify exact version if needed
{
  "engines": {
    "node": "20.x",
    "yarn": "1.x"
  }
}
```

#### 2. Dependency Version Pinning
```json
// Use exact versions for critical dependencies
{
  "dependencies": {
    "express": "4.16.3",    // Exact version
    "mongoose": "^5.9.10"   // Compatible version
  }
}
```

#### 3. Heroku Release Management

**Release Tracking**:
```bash
# View deployment history
heroku releases -a cardiowell-application

# Get detailed release info
heroku releases:info v123 -a cardiowell-application
```

**Release Rollback**:
```bash
# Rollback to specific version
heroku rollback v122 -a cardiowell-application
```

### Automated Version Updates

**Dependabot Configuration** (Recommended):
Create `.github/dependabot.yml`:
```yaml
version: 2
updates:
  - package-ecosystem: "npm"
    directory: "/"
    schedule:
      interval: "weekly"
    open-pull-requests-limit: 5
```

### Production Maintenance Windows

**Recommended Schedule**:
- **Minor Updates**: Weekly during low-traffic periods
- **Security Updates**: Immediate for critical vulnerabilities
- **Major Updates**: Quarterly with comprehensive testing

**Maintenance Process**:
1. **Staging Deployment**: Test all changes in staging environment
2. **User Notification**: Inform users of planned maintenance
3. **Deployment**: Execute during agreed maintenance window
4. **Verification**: Comprehensive testing post-deployment
5. **Monitoring**: Enhanced monitoring for 24 hours post-deployment

---

## Quick Reference Commands

### Complete Deployment Flow
```bash
# Navigate to frontend and build
cd patientCareReact-master && yarn build

# Integrate with backend
cd ../cardiowell-backend
cp -r ../patientCareReact-master/build/* ./build/

# Commit and deploy
git add build/ && git commit -m "Deploy: $(date)"
git push heroku master

# Monitor deployment
heroku logs --tail
```

### Health Checks
```bash
# Production health check
curl https://careportal.cardiowell.io/ping

# Development health check
curl $(heroku apps:info -s | grep web_url | cut -d= -f2)ping
```

### Emergency Commands
```bash
# Emergency rollback
heroku rollback v$(expr $(heroku releases --json | jq '.[0].version' | tr -d 'v"') - 1)

# Quick status check
heroku ps && heroku config | wc -l

# View recent logs
heroku logs --tail --dyno=web
```

### Environment Management
```bash
# Check environment variables
heroku config

# Set environment variable
heroku config:set VARIABLE_NAME=value

# Restart application
heroku restart
```

---

This comprehensive deployment documentation covers all aspects of deploying Cardiowell to both development and production environments on Heroku. For additional support or questions about specific deployment scenarios, refer to the development team or create issues in the respective GitHub repositories.