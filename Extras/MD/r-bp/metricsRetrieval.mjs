import { getRBPDB, getAllDurationMetrics } from './mongoConnection.mjs';

/**
 * Metrics Retrieval System - Phase 4 Implementation
 * 
 * Provides API endpoints and utility functions for retrieving
 * pre-computed duration metrics from the database
 */

/**
 * Validate IMEI format
 * @param {string} imei - IMEI to validate
 * @returns {boolean} True if valid
 */
const validateIMEI = (imei) => {
  if (!imei || typeof imei !== 'string') {
    return false;
  }
  
  // Basic IMEI validation (15 digits)
  const imeiRegex = /^\d{15}$/;
  return imeiRegex.test(imei);
};

/**
 * Validate duration parameter
 * @param {string} duration - Duration to validate
 * @returns {boolean} True if valid
 */
const validateDuration = (duration) => {
  const validDurations = ['1Month', '3Months', '6Months', '1Year', '2Years', 'All'];
  return validDurations.includes(duration);
};

/**
 * Check if metrics are fresh (less than 24 hours old)
 * @param {Object} metrics - Metrics object
 * @returns {boolean} True if fresh
 */
const isMetricsFresh = (metrics) => {
  if (!metrics || !metrics.lastUpdated) {
    return false;
  }
  
  const lastUpdated = new Date(metrics.lastUpdated);
  const now = new Date();
  const hoursDiff = (now - lastUpdated) / (1000 * 60 * 60);
  
  return hoursDiff < 24; // Consider fresh if less than 24 hours old
};

/**
 * Format metrics for API response
 * @param {Object} metrics - Raw metrics from database
 * @param {string} duration - Specific duration (optional)
 * @returns {Object} Formatted response
 */
const formatMetricsResponse = (metrics, duration = null) => {
  if (!metrics) {
    return {
      success: false,
      error: 'No metrics found',
      data: null
    };
  }

  const response = {
    success: true,
    patientId: metrics.patientId,
    lastUpdated: metrics.lastUpdated,
    isFresh: isMetricsFresh(metrics),
    durations: {}
  };

  if (duration) {
    // Return specific duration
    const durationKey = `metrics${duration}`;
    if (metrics[durationKey]) {
      response.durations[duration] = metrics[durationKey];
    } else {
      return {
        success: false,
        error: `Duration ${duration} not found`,
        data: null
      };
    }
  } else {
    // Return all durations
    response.durations = {
      '1Month': metrics.metrics1Month,
      '3Months': metrics.metrics3Months,
      '6Months': metrics.metrics6Months,
      '1Year': metrics.metrics1Year,
      '2Years': metrics.metrics2Years,
      'All': metrics.metricsAll
    };
  }

  return response;
};

/**
 * Get duration metrics for a patient
 * @param {string} imei - Device IMEI
 * @param {string} duration - Specific duration (optional)
 * @returns {Object} Formatted metrics response
 */
export const getDurationMetrics = async (imei, duration = null) => {
  try {
    // Validate inputs
    if (!validateIMEI(imei)) {
      return {
        success: false,
        error: 'Invalid IMEI format',
        data: null
      };
    }

    if (duration && !validateDuration(duration)) {
      return {
        success: false,
        error: 'Invalid duration. Valid options: 1Month, 3Months, 6Months, 1Year, 2Years, All',
        data: null
      };
    }

    console.log(`📊 Retrieving duration metrics for IMEI: ${imei}${duration ? `, Duration: ${duration}` : ''}`);

    const patientId = `patient_${imei}`;
    const metrics = await getAllDurationMetrics(patientId);

    if (!metrics) {
      console.log(`⚠️ No metrics found for IMEI: ${imei}`);
      return {
        success: false,
        error: 'No metrics found for this patient',
        data: null
      };
    }

    // metrics from getAllDurationMetrics is already in formatted shape
    let durationsOut = {}
    if (duration) {
      const block = metrics.durations?.[duration]
      if (!block) {
        return { success: false, error: `Duration ${duration} not found`, data: null }
      }
      durationsOut[duration] = block
    } else {
      durationsOut = metrics.durations || {}
    }

    const response = {
      success: true,
      patientId: metrics.patientId,
      lastUpdated: metrics.lastUpdated,
      isFresh: isMetricsFresh(metrics),
      durations: durationsOut,
    }

    console.log(`✅ Retrieved metrics for IMEI: ${imei}`)
    console.log(`   Fresh: ${response.isFresh}`)
    console.log(`   Durations: ${Object.keys(response.durations).join(', ')}`)

    return response;

  } catch (error) {
    console.error(`❌ Error retrieving metrics for IMEI ${imei}:`, error);
    return {
      success: false,
      error: 'Internal server error',
      data: null
    };
  }
};

/**
 * Get metrics summary for a patient
 * @param {string} imei - Device IMEI
 * @returns {Object} Summary response
 */
export const getMetricsSummary = async (imei) => {
  try {
    if (!validateIMEI(imei)) {
      return {
        success: false,
        error: 'Invalid IMEI format',
        data: null
      };
    }

    console.log(`📈 Retrieving metrics summary for IMEI: ${imei}`);

    // Use the formatted retrieval to ensure consistent shape
    const formatted = await getDurationMetrics(imei);

    if (!formatted?.success) {
      return {
        success: false,
        error: formatted?.error || 'No metrics found for this patient',
        data: null
      };
    }

    const durationsObj = formatted.durations || {};
    const summary = {
      patientId: formatted.patientId,
      lastUpdated: formatted.lastUpdated,
      isFresh: formatted.isFresh,
      totalDurations: 6,
      availableDurations: [],
      readingsCount: {},
      latestReadings: {}
    };

    Object.entries(durationsObj).forEach(([duration, block]) => {
      if (block && block.totalReadings > 0) {
        summary.availableDurations.push(duration);
        summary.readingsCount[duration] = block.totalReadings;
        summary.latestReadings[duration] = block.lastReading;
      }
    });

    console.log(`✅ Retrieved summary for IMEI: ${imei}`);
    console.log(`   Available durations: ${summary.availableDurations.join(', ')}`);
    console.log(`   Total readings: ${Object.values(summary.readingsCount).reduce((a, b) => a + b, 0)}`);

    return {
      success: true,
      data: summary
    };

  } catch (error) {
    console.error(`❌ Error retrieving summary for IMEI ${imei}:`, error);
    return {
      success: false,
      error: 'Internal server error',
      data: null
    };
  }
};

/**
 * Get specific duration metrics with detailed stats
 * @param {string} imei - Device IMEI
 * @param {string} duration - Duration to retrieve
 * @returns {Object} Detailed metrics response
 */
export const getDetailedMetrics = async (imei, duration) => {
  try {
    if (!validateIMEI(imei)) {
      return {
        success: false,
        error: 'Invalid IMEI format',
        data: null
      };
    }

    if (!validateDuration(duration)) {
      return {
        success: false,
        error: 'Invalid duration. Valid options: 1Month, 3Months, 6Months, 1Year, 2Years, All',
        data: null
      };
    }

    console.log(`🔍 Retrieving detailed metrics for IMEI: ${imei}, Duration: ${duration}`);

    const patientId = `patient_${imei}`;
    const metrics = await getAllDurationMetrics(patientId);

    if (!metrics) {
      return {
        success: false,
        error: 'No metrics found for this patient',
        data: null
      };
    }

    const durationKey = `metrics${duration}`;
    const durationData = metrics[durationKey];

    if (!durationData) {
      return {
        success: false,
        error: `Duration ${duration} not found`,
        data: null
      };
    }

    if (durationData.totalReadings === 0) {
      return {
        success: true,
        data: {
          duration,
          totalReadings: 0,
          message: 'No readings available for this duration',
          dateRange: durationData.dateRange,
          computedAt: durationData.computedAt
        }
      };
    }

    // Create detailed response
    const detailedResponse = {
      duration,
      totalReadings: durationData.totalReadings,
      dateRange: durationData.dateRange,
      computedAt: durationData.computedAt,
      isFresh: isMetricsFresh(metrics),
      
      // Basic Statistics
      basicStats: {
        meanSBP: durationData.basicStats?.mean_sbp || null,
        meanDBP: durationData.basicStats?.mean_dbp || null,
        map: durationData.basicStats?.map || null,
        pulse: durationData.basicStats?.mean_pulse || null
      },
      
      // Variability Statistics
      variabilityStats: {
        sbpSD: durationData.variabilityStats?.sbp_sd || null,
        dbpSD: durationData.variabilityStats?.dbp_sd || null,
        sbpCV: durationData.variabilityStats?.sbp_cv || null,
        dbpCV: durationData.variabilityStats?.dbp_cv || null
      },
      
      // Classification Counts
      classifications: {
        normal: durationData.classificationCounts?.normal || 0,
        stage1: durationData.classificationCounts?.stage1_hypertension || 0,
        stage2: durationData.classificationCounts?.stage2_hypertension || 0,
        crisis: durationData.classificationCounts?.hypertensive_crisis || 0
      },
      
      // Alerts
      alerts: durationData.alerts || [],
      
      // Latest Reading
      latestReading: durationData.lastReading || null
    };

    console.log(`✅ Retrieved detailed metrics for IMEI: ${imei}, Duration: ${duration}`);
    console.log(`   Readings: ${detailedResponse.totalReadings}`);
    console.log(`   SBP: ${detailedResponse.basicStats.meanSBP}, DBP: ${detailedResponse.basicStats.meanDBP}`);

    return {
      success: true,
      data: detailedResponse
    };

  } catch (error) {
    console.error(`❌ Error retrieving detailed metrics for IMEI ${imei}, Duration ${duration}:`, error);
    return {
      success: false,
      error: 'Internal server error',
      data: null
    };
  }
};

/**
 * Get all patients with available metrics
 * @returns {Object} List of patients with metrics
 */
export const getAllPatientsWithMetrics = async () => {
  try {
    console.log('📋 Retrieving all patients with metrics...');

    const db = await getRBPDB();
    const collection = db.collection('bp_duration_metrics');
    
    const patients = await collection.find({}, {
      projection: {
        patientId: 1,
        lastUpdated: 1,
        'metrics1Month.totalReadings': 1,
        'metrics3Months.totalReadings': 1,
        'metrics6Months.totalReadings': 1,
        'metrics1Year.totalReadings': 1,
        'metrics2Years.totalReadings': 1,
        'metricsAll.totalReadings': 1
      }
    }).toArray();

    const formattedPatients = patients.map(patient => {
      const imei = patient.patientId.replace('patient_', '');
      const totalReadings = Object.values(patient).reduce((sum, value) => {
        if (typeof value === 'object' && value?.totalReadings) {
          return sum + value.totalReadings;
        }
        return sum;
      }, 0);

      return {
        imei,
        patientId: patient.patientId,
        lastUpdated: patient.lastUpdated,
        totalReadings,
        isFresh: isMetricsFresh(patient)
      };
    });

    console.log(`✅ Retrieved ${formattedPatients.length} patients with metrics`);

    return {
      success: true,
      data: {
        patients: formattedPatients,
        totalPatients: formattedPatients.length,
        totalReadings: formattedPatients.reduce((sum, p) => sum + p.totalReadings, 0)
      }
    };

  } catch (error) {
    console.error('❌ Error retrieving all patients:', error);
    return {
      success: false,
      error: 'Internal server error',
      data: null
    };
  }
};

/**
 * Test metrics retrieval system
 * @param {string} testImei - Test IMEI
 */
export const testMetricsRetrieval = async (testImei = '352847091234567') => {
  console.log(`\n🧪 Testing Metrics Retrieval System with IMEI: ${testImei}`);
  
  try {
    // Test 1: Get all durations
    console.log('\n📊 Testing getDurationMetrics...');
    const allMetrics = await getDurationMetrics(testImei);
    console.log(`Result: ${allMetrics.success ? '✅' : '❌'}`);
    
    // Test 2: Get specific duration
    console.log('\n📊 Testing getDetailedMetrics (1Month)...');
    const detailedMetrics = await getDetailedMetrics(testImei, '1Month');
    console.log(`Result: ${detailedMetrics.success ? '✅' : '❌'}`);
    
    // Test 3: Get summary
    console.log('\n📈 Testing getMetricsSummary...');
    const summary = await getMetricsSummary(testImei);
    console.log(`Result: ${summary.success ? '✅' : '❌'}`);
    
    // Test 4: Get all patients
    console.log('\n📋 Testing getAllPatientsWithMetrics...');
    const allPatients = await getAllPatientsWithMetrics();
    console.log(`Result: ${allPatients.success ? '✅' : '❌'}`);
    
    console.log('\n✅ Metrics Retrieval System test completed');
    
    return {
      allMetrics,
      detailedMetrics,
      summary,
      allPatients
    };
    
  } catch (error) {
    console.error('❌ Metrics Retrieval System test failed:', error);
    throw error;
  }
};






