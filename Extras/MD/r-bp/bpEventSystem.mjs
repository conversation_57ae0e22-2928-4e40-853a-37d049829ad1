import { EventEmitter } from 'events';
import { processBPDay } from './bpAnalysisPipeline.mjs';
import { processAllDurations } from './durationCalculator.mjs';
import { getRBPDB } from './mongoConnection.mjs';
import { updateDurationMetrics } from './mongoConnection.mjs';
import dotenv from 'dotenv';

dotenv.config();

/**
 * BP Event System - Phase 2 Implementation
 * 
 * Centralized event management for real-time BP data processing
 * Triggers R analysis pipeline when new BP readings arrive from any device
 */

class BPEventSystem extends EventEmitter {
  constructor() {
    super();
    this.setupEventListeners();
    console.log('🔧 BP Event System initialized');
  }

  /**
   * Setup event listeners for BP data processing
   */
  setupEventListeners() {
    // Listen for new BP reading events
    this.on('newBPReading', this.handleNewBPReading.bind(this));
    
    // Listen for error events
    this.on('error', this.handleError.bind(this));
    
    console.log('📡 BP Event listeners configured');
  }

  /**
   * Handle new BP reading event
   * @param {Object} eventData - Event data containing IMEI and device info
   */
  async handleNewBPReading(eventData) {
    const { imei, deviceType, source, timestamp } = eventData;
    
    console.log(`\n🚀 BP Event Triggered:`);
    console.log(`   IMEI: ${imei}`);
    console.log(`   Device Type: ${deviceType}`);
    console.log(`   Source: ${source}`);
    console.log(`   Timestamp: ${timestamp}`);
    
    // Force local Node processing (external analytics disabled)
    const analyticsEnabled = false;

    try {
      // Local processing
      console.log(`📊 Processing all 6 durations locally for IMEI: ${imei}`);
      await processAllDurations(imei);
      console.log(`✅ Successfully processed all durations locally for ${imei}`);
      
    } catch (error) {
      console.error(`❌ Error processing durations for ${imei}:`, error);
      
      // Emit error event for monitoring
      this.emit('error', {
        imei,
        deviceType,
        source,
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Handle error events
   * @param {Object} errorData - Error data
   */
  handleError(errorData) {
    console.error('🚨 BP Event System Error:', errorData);
    
    // Log error to database for monitoring (optional)
    this.logErrorToDatabase(errorData);
  }

  /**
   * Log error to database for monitoring
   * @param {Object} errorData - Error data to log
   */
  async logErrorToDatabase(errorData) {
    try {
      const db = await getRBPDB();
      const collection = db.collection('bp_event_errors');
      
      await collection.insertOne({
        ...errorData,
        loggedAt: new Date(),
        resolved: false
      });
      
      console.log('📝 Error logged to database');
    } catch (error) {
      console.error('Failed to log error to database:', error);
    }
  }

  /**
   * Emit new BP reading event
   * @param {string} imei - Device IMEI
   * @param {string} deviceType - Type of device (ad, transtek, withings, generic)
   * @param {string} source - Source collection or endpoint
   * @param {string} timestamp - Reading timestamp
   */
  emitNewBPReading(imei, deviceType, source, timestamp = new Date().toISOString()) {
    console.log(`📤 Emitting newBPReading event for IMEI: ${imei}`);
    
    this.emit('newBPReading', {
      imei,
      deviceType,
      source,
      timestamp,
      eventId: `bp_${imei}_${Date.now()}`
    });
  }

  /**
   * Get event system status
   * @returns {Object} Status information
   */
  getStatus() {
    return {
      status: 'active',
      listeners: this.listenerCount('newBPReading'),
      errorListeners: this.listenerCount('error'),
      uptime: process.uptime(),
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Test event system
   * @param {string} testImei - Test IMEI to use
   */
  testEventSystem(testImei = '352847091234567') {
    console.log('🧪 Testing BP Event System...');
    
    this.emitNewBPReading(
      testImei,
      'test',
      'test_event',
      new Date().toISOString()
    );
    
    console.log('✅ Test event emitted');
  }
}

// Create singleton instance
const bpEventSystem = new BPEventSystem();

// Export the singleton instance
export default bpEventSystem;

// Export individual functions for convenience
export const emitNewBPReading = (imei, deviceType, source, timestamp) => {
  bpEventSystem.emitNewBPReading(imei, deviceType, source, timestamp);
};

export const getEventSystemStatus = () => {
  return bpEventSystem.getStatus();
};

export const testEventSystem = (testImei) => {
  bpEventSystem.testEventSystem(testImei);
};

// Graceful shutdown handling
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down BP Event System...');
  bpEventSystem.removeAllListeners();
  console.log('✅ BP Event System shutdown complete');
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Shutting down BP Event System...');
  bpEventSystem.removeAllListeners();
  console.log('✅ BP Event System shutdown complete');
});
