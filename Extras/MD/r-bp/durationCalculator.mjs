import { getRBPDB, updateDurationMetrics, getDurationMetrics } from './mongoConnection.mjs';
import { fetchBPReadings } from './dataFetcher.mjs';
import analyzeReadings from './nodeBpAnalysis.mjs';
import { 
  validateRAnalysisResult, 
  createFallbackMetrics, 
  validateStoredMetrics,
  getDataQualityScore,
  validateAndRepairMetrics,
  getDataHealthReport
} from './dataValidator.mjs';
import dotenv from 'dotenv';

dotenv.config();

/**
 * Duration Calculator - Phase 3 Implementation
 * 
 * Calculates metrics for all 6 fixed durations (1M, 3M, 6M, 1Y, 2Y, ALL)
 * whenever new BP data arrives from any device
 */

// Duration configurations
const DURATIONS = {
  '1Month': { days: 30, label: '1 Month' },
  '3Months': { days: 90, label: '3 Months' },
  '6Months': { days: 180, label: '6 Months' },
  '1Year': { days: 365, label: '1 Year' },
  '2Years': { days: 730, label: '2 Years' },
  'All': { days: null, label: 'All Time' }
};

/**
 * Calculate date range for a specific duration
 * @param {string} duration - Duration key (e.g., '1Month', '3Months')
 * @param {Date} endDate - End date (usually today)
 * @returns {Object} Object with start and end dates
 */
export const calculateDateRange = (duration, endDate = new Date()) => {
  const config = DURATIONS[duration];
  if (!config) {
    throw new Error(`Invalid duration: ${duration}`);
  }

  const end = new Date(endDate);
  end.setHours(23, 59, 59, 999); // End of day

  let start;
  if (duration === 'All') {
    // For 'All', use a very old date to get all data
    start = new Date('2020-01-01T00:00:00.000Z');
  } else {
    start = new Date(end);
    start.setDate(start.getDate() - config.days);
    start.setHours(0, 0, 0, 0); // Start of day
  }

  return {
    start: start.toISOString(),
    end: end.toISOString(),
    duration: config.label,
    days: config.days
  };
};

/**
 * Fetch BP readings for a specific duration
 * @param {string} imei - Device IMEI
 * @param {string} duration - Duration key
 * @param {Date} endDate - End date
 * @returns {Array} Array of normalized readings
 */
export const fetchReadingsForDuration = async (imei, duration, endDate = new Date()) => {
  try {
    const dateRange = calculateDateRange(duration, endDate);
    
    console.log(`📅 Fetching ${duration} data for IMEI: ${imei}`);
    console.log(`   Date range: ${dateRange.start} to ${dateRange.end}`);
    
    const readings = await fetchBPReadings(imei, dateRange.start.split('T')[0], dateRange.end.split('T')[0]);
    
    console.log(`   Found ${readings.length} readings for ${duration}`);
    return readings;
    
  } catch (error) {
    console.error(`❌ Error fetching ${duration} data for ${imei}:`, error);
    return [];
  }
};

// Node-only analysis wrapper (replaces R call)
const callNodeAnalysisForDuration = async (inputData) => {
  return analyzeReadings(inputData);
};

/**
 * Process a single duration with enhanced validation and fallback logic
 * @param {string} imei - Device IMEI
 * @param {string} duration - Duration key
 * @param {Date} endDate - End date
 * @returns {Object} Processed metrics for the duration
 */
export const processDuration = async (imei, duration, endDate = new Date()) => {
  try {
    console.log(`\n🔄 Processing ${duration} for IMEI: ${imei}`);
    
    // Fetch readings for this duration
    const readings = await fetchReadingsForDuration(imei, duration, endDate);
    const dateRange = calculateDateRange(duration, endDate);
    
    if (readings.length === 0) {
      console.log(`⚠️ No readings found for ${duration}`);
      const fallbackMetrics = createFallbackMetrics(duration, 'No readings found', dateRange);
      console.log(`📋 Created fallback metrics for ${duration}`);
      return fallbackMetrics;
    }
    
    // Prepare data for Node analysis
    const patientId = `patient_${imei}`;
    const inputData = {
      patient_id: patientId,
      duration: duration,
      analysis_version: process.env.CURRENT_ANALYSIS_VERSION || 'v1.0.0-node',
      readings: readings.map(reading => ({
        sbp: reading.sbp,
        dbp: reading.dbp,
        pulse: reading.pulse,
        reading_time: reading.readingAtUTC
      }))
    };
    
    // Call Node analysis
    const analysisResult = await callNodeAnalysisForDuration(inputData);
    
    // Validate R analysis result
    const validation = validateRAnalysisResult(analysisResult, duration);
    
    if (!validation.isValid) {
      console.error(`❌ R analysis validation failed for ${duration}:`, validation.errors);
      console.log(`📋 Creating fallback metrics due to validation failure`);
      return createFallbackMetrics(duration, validation.errors.join('; '), dateRange);
    }
    
    // Log warnings if any
    if (validation.warnings.length > 0) {
      console.log(`⚠️ Validation warnings for ${duration}:`, validation.warnings);
    }
    
    // Prepare metrics for storage
    const metrics = {
      duration: duration,
      totalReadings: analysisResult.total_readings,
      dateRange: dateRange,
      basicStats: analysisResult.basic_stats,
      variabilityStats: analysisResult.variability_stats,
      rangeStats: analysisResult.range_stats,
      pressureStats: analysisResult.pressure_stats,
      circadianStats: analysisResult.circadian_stats,
      timeOfDay: analysisResult.time_of_day,
      classificationCounts: analysisResult.classification_counts,
      alerts: analysisResult.alerts,
      combinedStats: analysisResult.combined_stats,
      lastReading: analysisResult.latest_reading,
      computedAt: new Date(),
      isFallback: false,
      validationWarnings: validation.warnings
    };
    
    // Get data quality score
    const quality = getDataQualityScore(metrics);
    metrics.qualityScore = quality.score;
    metrics.qualityLevel = quality.qualityLevel;
    
    console.log(`✅ ${duration} processing completed: ${metrics.totalReadings} readings`);
    console.log(`   SBP: ${metrics.basicStats.mean_sbp || 'N/A'}, DBP: ${metrics.basicStats.mean_dbp || 'N/A'}`);
    console.log(`   Quality: ${quality.qualityLevel} (${quality.score}/100)`);
    
    return metrics;
    
  } catch (error) {
    console.error(`❌ Error processing ${duration} for ${imei}:`, error);
    const dateRange = calculateDateRange(duration, endDate);
    const fallbackMetrics = createFallbackMetrics(duration, error.message, dateRange);
    console.log(`📋 Created fallback metrics due to processing error`);
    return fallbackMetrics;
  }
};

/**
 * Process all 6 durations for a patient with enhanced validation and error handling
 * @param {string} imei - Device IMEI
 * @param {Date} endDate - End date (optional, defaults to today)
 * @returns {Object} Results for all durations
 */
export const processAllDurations = async (imei, endDate = new Date()) => {
  try {
    console.log(`\n🚀 Processing all durations for IMEI: ${imei}`);
    console.log(`📅 End date: ${endDate.toISOString().split('T')[0]}`);
    
    const results = {};
    const patientId = `patient_${imei}`;
    const processingStats = {
      successful: 0,
      failed: 0,
      fallback: 0,
      totalReadings: 0,
      startTime: new Date()
    };
    
    // Process each duration
    for (const duration of Object.keys(DURATIONS)) {
      try {
        console.log(`\n⏱️ Processing ${duration}...`);
        const metrics = await processDuration(imei, duration, endDate);
        results[duration] = metrics;
        
        // Update processing stats
        processingStats.totalReadings += metrics.totalReadings || 0;
        if (metrics.isFallback) {
          processingStats.fallback++;
        } else if (metrics.error) {
          processingStats.failed++;
        } else {
          processingStats.successful++;
        }
        
        // Update database with this duration's metrics
        await updateDurationMetrics(patientId, duration, metrics);
        
      } catch (error) {
        console.error(`❌ Failed to process ${duration}:`, error);
        const fallbackMetrics = createFallbackMetrics(
          duration, 
          `Processing failed: ${error.message}`, 
          calculateDateRange(duration, endDate)
        );
        results[duration] = fallbackMetrics;
        processingStats.failed++;
        
        // Still update database with fallback metrics
        try {
          await updateDurationMetrics(patientId, duration, fallbackMetrics);
        } catch (dbError) {
          console.error(`❌ Failed to save fallback metrics for ${duration}:`, dbError);
        }
      }
    }
    
    // Update lastUpdated timestamp
    const db = await getRBPDB();
    const collection = db.collection('bp_duration_metrics');
    await collection.updateOne(
      { patientId },
      { 
        $set: { 
          lastUpdated: new Date(),
          updatedAt: new Date(),
          processingStats: {
            ...processingStats,
            endTime: new Date(),
            duration: new Date() - processingStats.startTime
          }
        }
      },
      { upsert: true }
    );
    
    console.log(`\n✅ All durations processed for IMEI: ${imei}`);
    
    // Enhanced summary with quality information
    console.log(`\n📊 Duration Summary:`);
    Object.entries(results).forEach(([duration, metrics]) => {
      const readings = metrics.totalReadings || 0;
      const quality = metrics.qualityLevel || 'unknown';
      const isFallback = metrics.isFallback || false;
      
      let status = '✅';
      if (metrics.error) status = '❌';
      else if (isFallback) status = '⚠️';
      
      console.log(`   ${status} ${duration}: ${readings} readings (${quality})`);
      if (isFallback) {
        console.log(`      └─ Fallback data: ${metrics.error || 'Unknown reason'}`);
      }
    });
    
    // Processing statistics
    console.log(`\n📈 Processing Statistics:`);
    console.log(`   ✅ Successful: ${processingStats.successful}/6`);
    console.log(`   ⚠️ Fallback: ${processingStats.fallback}/6`);
    console.log(`   ❌ Failed: ${processingStats.failed}/6`);
    console.log(`   📊 Total readings: ${processingStats.totalReadings}`);
    console.log(`   ⏱️ Processing time: ${new Date() - processingStats.startTime}ms`);
    
    return {
      results,
      processingStats,
      patientId,
      imei,
      endDate: endDate.toISOString().split('T')[0]
    };
    
  } catch (error) {
    console.error(`❌ Error processing all durations for ${imei}:`, error);
    throw error;
  }
};

/**
 * Get duration metrics for a patient with enhanced validation
 * @param {string} imei - Device IMEI
 * @returns {Object} All duration metrics
 */
export const getDurationMetricsForPatient = async (imei) => {
  try {
    const patientId = `patient_${imei}`;
    const metrics = await getDurationMetrics(patientId);
    
    console.log(`📊 Retrieved duration metrics for IMEI: ${imei}`);
    return metrics;
    
  } catch (error) {
    console.error(`❌ Error getting duration metrics for ${imei}:`, error);
    throw error;
  }
};

/**
 * Get duration metrics with validation and fallback logic
 * @param {string} imei - Device IMEI
 * @param {string} duration - Specific duration to retrieve (optional)
 * @returns {Object} Validated metrics with quality information
 */
export const getValidatedDurationMetrics = async (imei, duration = null) => {
  try {
    const patientId = `patient_${imei}`;
    const db = await getRBPDB();
    const collection = db.collection('bp_duration_metrics');
    
    const metricsDoc = await collection.findOne({ patientId });
    if (!metricsDoc) {
      console.log(`📋 No metrics found for IMEI: ${imei}, creating new document`);
      const newDoc = await getDurationMetrics(patientId);
      return {
        patientId,
        lastUpdated: newDoc.lastUpdated,
        durations: {},
        healthStatus: 'no_data',
        message: 'No duration metrics available'
      };
    }
    
    const durations = duration ? [duration] : Object.keys(DURATIONS);
    const validatedDurations = {};
    let needsRepair = false;
    
    for (const dur of durations) {
      const metrics = metricsDoc[`metrics${dur}`];
      if (!metrics) {
        console.log(`⚠️ No ${dur} metrics found for IMEI: ${imei}`);
        validatedDurations[dur] = {
          status: 'missing',
          metrics: null,
          quality: { score: 0, level: 'missing' }
        };
        needsRepair = true;
        continue;
      }
      
      // Validate stored metrics
      const validation = validateStoredMetrics(metrics, dur);
      const quality = getDataQualityScore(metrics);
      
      if (!validation.isValid) {
        console.log(`🔧 ${dur} metrics validation failed, attempting repair`);
        const repairResult = await validateAndRepairMetrics(patientId, dur);
        
        if (repairResult.repaired) {
          validatedDurations[dur] = {
            status: 'repaired',
            metrics: repairResult.repairedMetrics,
            quality: getDataQualityScore(repairResult.repairedMetrics),
            repairReason: repairResult.reason
          };
        } else {
          validatedDurations[dur] = {
            status: 'invalid',
            metrics: metrics,
            quality: quality,
            validationErrors: validation.errors
          };
        }
        needsRepair = true;
      } else {
        validatedDurations[dur] = {
          status: 'valid',
          metrics: metrics,
          quality: quality
        };
      }
    }
    
    // Determine overall health status
    const validCount = Object.values(validatedDurations).filter(d => d.status === 'valid').length;
    const totalCount = Object.keys(validatedDurations).length;
    const healthStatus = validCount === totalCount ? 'healthy' : 
                        validCount > totalCount / 2 ? 'degraded' : 'critical';
    
    const result = {
      patientId,
      lastUpdated: metricsDoc.lastUpdated,
      durations: validatedDurations,
      healthStatus,
      needsRepair,
      totalDurations: totalCount,
      validDurations: validCount,
      processingStats: metricsDoc.processingStats || null
    };
    
    console.log(`📊 Retrieved validated metrics for IMEI: ${imei} (${healthStatus})`);
    return result;
    
  } catch (error) {
    console.error(`❌ Error getting validated duration metrics for ${imei}:`, error);
    throw error;
  }
};

/**
 * Get data health report for a patient
 * @param {string} imei - Device IMEI
 * @returns {Object} Comprehensive health report
 */
export const getPatientDataHealthReport = async (imei) => {
  try {
    const patientId = `patient_${imei}`;
    const healthReport = await getDataHealthReport(patientId);
    
    console.log(`🏥 Generated health report for IMEI: ${imei} (${healthReport.status})`);
    return healthReport;
    
  } catch (error) {
    console.error(`❌ Error generating health report for ${imei}:`, error);
    throw error;
  }
};

/**
 * Force refresh all durations for a patient
 * @param {string} imei - Device IMEI
 * @param {Date} endDate - End date (optional, defaults to today)
 * @returns {Object} Refresh results
 */
export const forceRefreshAllDurations = async (imei, endDate = new Date()) => {
  try {
    console.log(`🔄 Force refreshing all durations for IMEI: ${imei}`);
    
    // Process all durations
    const results = await processAllDurations(imei, endDate);
    
    // Get updated health report
    const healthReport = await getPatientDataHealthReport(imei);
    
    console.log(`✅ Force refresh completed for IMEI: ${imei}`);
    console.log(`📊 Health status: ${healthReport.status}`);
    
    return {
      ...results,
      healthReport,
      refreshType: 'force',
      refreshedAt: new Date()
    };
    
  } catch (error) {
    console.error(`❌ Error force refreshing durations for ${imei}:`, error);
    throw error;
  }
};

/**
 * Get metrics for specific duration with fallback logic
 * @param {string} imei - Device IMEI
 * @param {string} duration - Duration to retrieve
 * @param {boolean} allowFallback - Whether to return fallback data if primary fails
 * @returns {Object} Duration metrics with metadata
 */
export const getDurationMetricsWithFallback = async (imei, duration, allowFallback = true) => {
  try {
    const patientId = `patient_${imei}`;
    const db = await getRBPDB();
    const collection = db.collection('bp_duration_metrics');
    
    const metricsDoc = await collection.findOne({ patientId });
    if (!metricsDoc) {
      if (allowFallback) {
        console.log(`📋 No metrics document found, creating fallback for ${duration}`);
        const fallbackMetrics = createFallbackMetrics(
          duration, 
          'No metrics document found', 
          calculateDateRange(duration)
        );
        return {
          duration,
          metrics: fallbackMetrics,
          source: 'fallback',
          quality: getDataQualityScore(fallbackMetrics),
          isReliable: false
        };
      } else {
        throw new Error(`No metrics found for patient ${patientId}`);
      }
    }
    
    const metrics = metricsDoc[`metrics${duration}`];
    if (!metrics) {
      if (allowFallback) {
        console.log(`📋 No ${duration} metrics found, creating fallback`);
        const fallbackMetrics = createFallbackMetrics(
          duration, 
          `No ${duration} metrics found`, 
          calculateDateRange(duration)
        );
        return {
          duration,
          metrics: fallbackMetrics,
          source: 'fallback',
          quality: getDataQualityScore(fallbackMetrics),
          isReliable: false
        };
      } else {
        throw new Error(`No ${duration} metrics found for patient ${patientId}`);
      }
    }
    
    // Validate metrics
    const validation = validateStoredMetrics(metrics, duration);
    const quality = getDataQualityScore(metrics);
    
    if (!validation.isValid && allowFallback) {
      console.log(`🔧 ${duration} metrics invalid, creating fallback`);
      const fallbackMetrics = createFallbackMetrics(
        duration, 
        `Validation failed: ${validation.errors.join(', ')}`, 
        metrics.dateRange || calculateDateRange(duration)
      );
      return {
        duration,
        metrics: fallbackMetrics,
        source: 'fallback',
        quality: getDataQualityScore(fallbackMetrics),
        isReliable: false,
        validationErrors: validation.errors
      };
    }
    
    return {
      duration,
      metrics: metrics,
      source: validation.isValid ? 'primary' : 'repaired',
      quality: quality,
      isReliable: quality.isReliable,
      validation: validation
    };
    
  } catch (error) {
    console.error(`❌ Error getting ${duration} metrics with fallback for ${imei}:`, error);
    throw error;
  }
};

/**
 * Test duration calculator with sample data
 * @param {string} testImei - Test IMEI
 */
export const testDurationCalculator = async (testImei = '352847091234567') => {
  console.log(`\n🧪 Testing Duration Calculator with IMEI: ${testImei}`);
  
  try {
    const results = await processAllDurations(testImei);
    
    console.log(`\n✅ Duration Calculator test completed`);
    console.log(`📊 Results summary:`);
    Object.entries(results).forEach(([duration, metrics]) => {
      console.log(`   ${duration}: ${metrics.totalReadings} readings`);
    });
    
    return results;
    
  } catch (error) {
    console.error(`❌ Duration Calculator test failed:`, error);
    throw error;
  }
};
