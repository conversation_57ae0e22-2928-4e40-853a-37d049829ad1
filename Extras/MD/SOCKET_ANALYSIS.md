## Socket.IO Implementation Analysis – CardioWell

### Executive Summary

CardioWell uses Socket.IO to power real-time updates across provider and patient dashboards. The backend (`cardiowell-backend/`) initializes a single Socket.IO server, exposes it on the Express app (`app.set('socketio', ...)`), and implements event handlers for streaming clinic patient overviews, on-demand refreshes, patient dashboard retrieval, and an iframe view. The frontend (`patientCareReact-master/`) connects via `socket.io-client` from multiple React components, listening for updates and triggering refresh requests. A Withings webhook flow emits a server→client `withingsDataUpdate` event to prompt UI updates.

Security is event-level only (basic checks using Mongoose models) with no dedicated Socket.IO authentication middleware or rooms/namespaces. Intervals are created per-connection without explicit cleanup, and client components do not consistently disconnect sockets on unmount. Recommendations are included below to improve security, resource management, and observability.

---

### 1) Socket Usage Discovery

- Backend (cardiowell-backend/)
  - Server initialization and handlers: `cardiowell-backend/app.mjs` (L238–L301)
  - Withings webhook to socket bridge:
    - Controller acquires server instance: `cardiowell-backend/withings/controller/update.mjs` (L17, L39)
    - Service emits broadcast: `cardiowell-backend/withings/service/onNotification.mjs` (L56–L65)
  - Package version: `cardiowell-backend/package.json` → "socket.io": "^2.3.0"

- Frontend (patientCareReact-master/)
  - Provider dashboard: `patientCareReact-master/src/components/DashboardPage.js` (socket usage around L45–L47, L224–L274)
  - Patient dashboard: `patientCareReact-master/src/components/PatientDashboard.js` (socket usage around L37–L41, L155–L168)
  - IFrame page: `patientCareReact-master/src/components/IFramePage.js` (socket usage around L60–L62, L970–L1042)
  - Dev proxy for websockets: `patientCareReact-master/local-proxy.js` (L14–L21) – proxies `/socket.io` with `ws: true`
  - Package version: `patientCareReact-master/package.json` → "socket.io-client": "^2.3.0"

Code excerpts

```javascript
// cardiowell-backend/app.mjs (L238–L246)
const httpServer = http.Server(app)
const ioServer = io(httpServer)
app.set("socketio", ioServer)
ioServer.on("connection", function (socket) {
  // handlers below
})
```

```javascript
// cardiowell-backend/app.mjs (L246–L257)
socket.on("patientData", function (emittedData) {
  Provider.findOne({ _id: emittedData.providerID }, function (err, result) {
    if (result) {
      setInterval(async function () {
        const patientArray = await getPatientOverviewsForClinc(
          emittedData.clinic,
          emittedData.providerID,
        )
        socket.emit("patientData", patientArray)
      }, 10000)
    } else {
      socket.emit("patientData", "Error: Not Authenticated")
    }
  })
})
```

```javascript
// cardiowell-backend/withings/service/onNotification.mjs (L60–L64)
const patientId = userData.patientId
getPatientDataById(patientId).then(patient => {
  const clinic = patient?.patientObj?.clinic
  clinic && socket.emit("withingsDataUpdate", { patientId, clinic })
})
```

```javascript
// patientCareReact-master/src/components/DashboardPage.js (L224–L241)
socket = openSocket(WEBSOCKET_URL)
const emittedData = {
  clinic: clinic,
  providerID: sessionStorage.getItem('providerID'),
}
socket.emit('patientData', emittedData)
socket.on('patientData', (data) => {
  if (data === 'Error: Not Authenticated') { /* ... */ }
  else if (data.constructor === Array) {
    setPatients(data)
    setSelectedPatientId((currId) => {
      if (currId) {
        socket.emit('patientDashboardData', { id: currId })
      }
      return currId
    })
  }
})
```

---

### 2) Functional Analysis (Event Catalog)

- Event: `patientData`
  - Direction: client→server; server→client (streamed every 10s)
  - Purpose: Stream clinic patient overviews for provider dashboard
  - Trigger/conditions: client emits `{ clinic, providerID }`; server validates `Provider` by `_id`
  - Handler: `cardiowell-backend/app.mjs` (L246–L257)
  - Responses: success → `Array<PatientOverview>`; failure → string "Error: Not Authenticated"

- Event: `patientDataUpdate`
  - Direction: client→server; server→client (one-time)
  - Purpose: On-demand refresh of clinic patient overviews (e.g., after Withings update)
  - Trigger/conditions: client emits `{ clinic, providerID }`; server validates `Provider` by `_id`
  - Handler: `cardiowell-backend/app.mjs` (L263–L275)
  - Responses: success → `Array<PatientOverview>`; failure → string "Error: Not Authenticated"

- Event: `patientDashboardData`
  - Direction: client→server; server→client (one-time)
  - Purpose: Retrieve a single patient’s full dashboard object
  - Trigger/conditions: client emits `{ id }`
  - Handler: `cardiowell-backend/app.mjs` (L278–L285)
  - Responses: success → `patient.patientObj`; errors are `console.error`-ed (no error emit)
  - Note: No explicit auth/authorization checks in this handler

- Event: `iFrameData`
  - Direction: client→server; server→client (streamed every 10s)
  - Purpose: Provide clinic-wide patient list for IFrame view
  - Trigger/conditions: client emits `{ clinicID, clinic }`; server validates `Clinic` by `_id`
  - Handler: `cardiowell-backend/app.mjs` (L287–L297)
  - Responses: success → `Array<PatientOverview>`; failure → string "Error: Not Authenticated"

- Event: `withingsDataUpdate`
  - Direction: server→client broadcast
  - Purpose: Notify frontends of new Withings data; UIs then request refreshed data
  - Emit locations:
    - `cardiowell-backend/withings/controller/update.mjs` acquires socket via `request.app.get('socketio')` (L17, L39) and passes to service
    - `cardiowell-backend/withings/service/onNotification.mjs` emits `{ patientId, clinic }` (L60–L64)
  - Typical client handling:
    - Provider dashboard: on `withingsDataUpdate`, if clinic matches, emit `patientDataUpdate` (DashboardPage.js L263–L266)
    - Patient dashboard: on `withingsDataUpdate`, if `patientId` matches, emit `patientDashboardData` (PatientDashboard.js L164–L167)

Payload structures

- `patientData` request: `{ clinic: string, providerID: string }`
- `patientData` response: `Array<PatientOverview>` | "Error: Not Authenticated"
- `patientDataUpdate` request/response: same as `patientData`
- `patientDashboardData` request: `{ id: string }`
- `patientDashboardData` response: `PatientFull` (shape of `patient.patientObj`)
- `iFrameData` request: `{ clinicID: string, clinic: string }`
- `iFrameData` response: `Array<PatientOverview>` | "Error: Not Authenticated"
- `withingsDataUpdate` payload: `{ patientId: string, clinic: string }`

---

### 3) Architecture Integration

- REST endpoints and routes
  - Withings webhook controllers (`/routes/withings.mjs`) call `update`/`devUpdate` which pull the Socket.IO server via `request.app.get('socketio')` and forward to `onNotification` for broadcasting (update.mjs L17–L27, L39–L49).
  - Other REST endpoints are used for initial data fetches in React (e.g., `getClinicPatientOverviews`, `getPatientData`) before sockets stream updates.

- Database operations and models
  - `Provider` model used to validate `providerID` for `patientData`/`patientDataUpdate` (app.mjs L247–L275)
  - `Clinic` model used to validate `clinicID` for `iFrameData` (app.mjs L287–L297)
  - Data retrieval helpers: `getPatientsForClinic`, `getPatientOverviewsForClinc`, `getPatientDataById` (imports at app.mjs L36–L39)

- Frontend components and state management
  - `DashboardPage.js`: streams clinic patients and updates selected patient data on each stream and on-demand refresh
  - `PatientDashboard.js`: refreshes single patient data on Withings updates
  - `IFramePage.js`: streams clinic list for embedded view
  - State updates use React `useState`/`useMemo`; socket connection stored in a module/global variable in components

- Authentication and authorization
  - Express uses sessions/passport for HTTP; sockets do not use handshake auth middleware
  - Event-level checks: `patientData`/`patientDataUpdate` validate `Provider`; `iFrameData` validates `Clinic`
  - `patientDashboardData` has no explicit validation

- Middleware and error handling
  - No Socket.IO-specific middleware
  - Express uses Sentry error handler (app.mjs L167) and `errorhandler` in development (L168–L170)

---

### 4) Connection Lifecycle

- Establishment
  - Server: `ioServer = io(httpServer)`; `ioServer.on('connection', ...)` (app.mjs L238–L245)
  - Client: `openSocket(WEBSOCKET_URL)` where `WEBSOCKET_URL = process.env.REACT_APP_BASE_URL` (common.js L1)
  - Dev proxy: `/socket.io` proxied to API with websockets enabled (local-proxy.js L14–L21)

- Authentication/authorization on connect
  - None at the Socket.IO layer; authorization performed per-event for some events

- Persistence and reconnection
  - Relies on `socket.io-client` defaults; no custom backoff/timeout logic implemented

- Disconnection and cleanup
  - Server: logs "User Disconnected" (app.mjs L242–L244); no interval cleanup for `setInterval` timers created inside handlers
  - Client: `DashboardPage` calls `socket.disconnect()` on logout (L412), but other components do not explicitly clean up on unmount

- Room/namespace management
  - Not used; all emits are global or socket-specific

---

### 5) Error Handling and Reliability

- Error handling patterns
  - Server emits string "Error: Not Authenticated" on failed provider/clinic checks
  - Exceptions in `patientDashboardData` are caught and logged; no error message is emitted back to client

- Timeout and retry
  - No explicit application-level timeouts or retries beyond Socket.IO defaults

- Fallback strategies
  - UIs perform initial data fetch via REST, so stale data remains visible if socket updates fail

- Logging and monitoring
  - Minimal console logging on disconnect; no structured logging for socket events
  - Sentry integrated for Express, but not specifically for Socket.IO events

---

### Data Flow Diagram

```mermaid
sequenceDiagram
  participant Device as Withings
  participant API as Backend (Express)
  participant SIO as Socket.IO Server
  participant Prov as Provider UI
  participant Pat as Patient UI

  Device->>API: POST /routes/withings/update
  API->>SIO: socket.emit('withingsDataUpdate', {patientId, clinic})
  SIO-->>Prov: withingsDataUpdate
  SIO-->>Pat: withingsDataUpdate
  Prov->>SIO: emit('patientDataUpdate', {clinic, providerID})
  SIO-->>Prov: patientDataUpdate [Array]
  Prov->>SIO: emit('patientDashboardData', {id})
  SIO-->>Prov: patientDashboardData [Object]
```

---

### Code References (paths and line numbers)

- Server setup and handlers: `cardiowell-backend/app.mjs` (L238–L301)
- Withings update → broadcast: `cardiowell-backend/withings/controller/update.mjs` (L17, L39); `cardiowell-backend/withings/service/onNotification.mjs` (L60–L64)
- Data services used by handlers: `cardiowell-backend/users/service/patientData.mjs` (imported at app.mjs L36–L39)
- Frontend sockets:
  - `patientCareReact-master/src/components/DashboardPage.js` (L45–L47, L224–L274)
  - `patientCareReact-master/src/components/PatientDashboard.js` (L37–L41, L155–L168)
  - `patientCareReact-master/src/components/IFramePage.js` (L60–L62, L970–L1042)
  - Proxy: `patientCareReact-master/local-proxy.js` (L14–L21)

---

### Recommendations and Best Practices

1) Authentication and Authorization
- Add Socket.IO auth middleware (e.g., JWT/session) on connection; reject unauthenticated sockets
- Validate `patientDashboardData` requests against user roles and provider/patient relationships

2) Scoping and Efficiency
- Use rooms per clinic/patient/provider (e.g., `clinic:<id>`, `patient:<id>`)
- Scope broadcasts: `io.to('clinic:<id>').emit('withingsDataUpdate', ...)` instead of global emits

3) Interval and Resource Management
- Replace per-socket `setInterval` with a shared scheduler or server-side push on changes
- Track and clear timers on `disconnect` to avoid orphaned intervals

4) Client Lifecycle
- Ensure each component cleans up listeners and disconnects or reuses a shared socket on unmount
- Avoid creating multiple connections in the same component (`DashboardPage` opens twice)

5) Observability
- Add structured logs for socket connects/disconnects, joins/leaves, emits, and errors
- Integrate Socket.IO event/error tracking with Sentry or similar

6) Versioning and Maintenance
- Consider upgrading to Socket.IO v4+ (server and client) for security and features
- Centralize event names as shared constants to avoid typos


