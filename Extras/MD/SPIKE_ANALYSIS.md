### Spike Analysis (Part 1) — 2025-08-19 00:00:00Z–00:00:03Z

- **Scope**: First chunk of logs around midnight UTC for `careportal.cardiowell.io` (Heroku router + app dynos `web.1` and `web.2`).
- **Context signals**: Two web dynos active; frequent per-second app noise log present.

### High-signal observations
- **Telemetry POSTs (forwardtelemetry)**
  - Two near-simultaneous POSTs to `/routes/transtek/bloodGlucose/forwardtelemetry` routed to different dynos:
    - ~00:00:00.456 → `web.1`, 200, service ~355ms, bytes 36
    - ~00:00:00.518 → `web.2`, 200, service ~367ms, bytes 36
  - App logs show payloads for the same device `************` (IMEI `016323000413532`) with identical `createdAt=**********` but different `status` values:
    - `bat=19, sig=21, tp=249, at_t=11`
    - `bat=0, sig=6, tp=221, at_t=9`
  - App emitted "data updated in the db" on both dynos within the same second.

- **Short-link and static assets**
  - `GET /s/vMhitPNx` → 200, ~146ms; corresponding app log present.
  - Static assets (`manifest.json`, `main.*.js`, `main.*.css`, PNG, SVG) mostly `304` with 1–2ms service → normal cache behavior.

- **Socket.IO activity**
  - Multiple polling requests succeed (200) but several calls return **400** quickly:
    - Examples:
      - `GET /socket.io/?...&sid=-I-Ue0wfp7gDyIkNAAQe` → 400
      - `POST /socket.io/?...&sid=-I-Ue0wfp7gDyIkNAAQe` → 400
      - `GET /socket.io/?transport=websocket&sid=40uNfTSotM1IjiU2AAQd` → 400
  - Mix of HTTP/2 for polling and HTTP/1.1 for websocket upgrade; service times 1–6ms on 400s; bytes 18–41.

- **Auth endpoint**
  - `POST /routes/users/auth/magic-link` → 401 in ~213ms (likely expected without credentials).

### Anomalies / potential spikes
- **Duplicate-instant telemetry for a single device**
  - Two POSTs for the same device and timestamp within ~61ms routed to different dynos may indicate either:
    - Legitimate rapid successive status emits, or
    - Upstream duplication/retry behavior. Impact: potential double-processing if idempotency safeguards are missing.

- **Socket.IO 400 churn**
  - Repeated 400s on polling/websocket handshake suggest session-id mismatch, sticky session issues, or transport negotiation failures. On Heroku, websocket upgrades require proper config and may suffer if session affinity is not maintained.

- **Noisy per-second log**
  - `"You will see this message every second"` from both dynos adds noise and can mask spikes.

### Impact
- Telemetry requests succeed (200) with moderate service times (~355–367ms) and DB update logs present.
- Socket.IO 400s may degrade realtime UX and generate superfluous router traffic.

### Recommendations (immediate checks)
- **Idempotency for telemetry**: Ensure `/forwardtelemetry` uses a deterministic idempotency key (e.g., `deviceId + createdAt`) to de-dup writes.
- **Dyno routing/session affinity**: Verify websocket support and sticky sessions; confirm Socket.IO server and client versions are compatible and CORS/origin settings are correct. Inspect 400 bodies server-side for exact reasons.
- **Reduce log noise**: Remove or lower the frequency of the per-second heartbeat log in production to surface meaningful spikes.
- **Alerting**: Add an alert on spike of 4xx for `/socket.io/*` and duplicate telemetry for same `(deviceId, createdAt)` within a short window.

### Notable raw excerpts
```text
at=info method=POST path="/routes/transtek/bloodGlucose/forwardtelemetry" ... dyno=web.1 ... service=355ms status=200 bytes=36
at=info method=POST path="/routes/transtek/bloodGlucose/forwardtelemetry" ... dyno=web.2 ... service=367ms status=200 bytes=36
```
```text
app/web.1 POST ... {"deviceId":"************","createdAt":**********,..."bat":19,...}
app/web.2 POST ... {"deviceId":"************","createdAt":**********,..."bat":0,...}
```
```text
GET /socket.io/?EIO=3&transport=polling&t=...&sid=-I-Ue0wfp7gDyIkNAAQe ... status=400 bytes=41
GET /socket.io/?EIO=3&transport=websocket&sid=40uNfTSotM1IjiU2AAQd ... status=400 bytes=18
```

— End of Part 1. Will append with subsequent chunks.

