# R Analytics Heroku Deployment Guide

## Overview
This guide explains how to deploy the Cardiowell Analytics R Plumber API to Heroku.

## Files Created/Modified
- `Cardiowell-Analytics/Procfile` - Tells Hero<PERSON> how to start the web server
- `Cardiowell-Analytics/start.sh` - Startup script (already exists with correct permissions)
- `Cardiowell-Analytics/init.R` - R package dependencies for Heroku R buildpack

## Required Environment Variables

Set these environment variables in your Heroku app dashboard or using the Heroku CLI:

### Required Variables
```bash
# Heroku sets PORT automatically - DO NOT set this manually
# HOST=0.0.0.0  # Optional - defaults to 0.0.0.0 in start.sh

# Application-specific variables (set these in Heroku):
ANALYTICS_VERSION=v1.0.0-r
R_ANALYTICS_TOKEN=SYQEsz{3swa5]8?|.AjT?HYQ4ro.2T
```

### Setting Environment Variables

#### Via Heroku Dashboard:
1. Go to your Heroku app dashboard
2. Navigate to Settings tab
3. Click "Reveal Config Vars"
4. Add the variables above

#### Via Heroku CLI:
```bash
heroku config:set ANALYTICS_VERSION=v1.0.0-r
heroku config:set R_ANALYTICS_TOKEN='SYQEsz{3swa5]8?|.AjT?HYQ4ro.2T'
```

## Deployment Steps

1. **Commit the Procfile:**
   ```bash
   git add Cardiowell-Analytics/Procfile
   git add Cardiowell-Analytics/init.R
   git commit -m "Add Procfile and init.R for Heroku deployment"
   ```

2. **Push to Heroku:**
   ```bash
   git push heroku main
   # or if your default branch is master:
   git push heroku master
   ```

3. **Scale the web dyno:**
   ```bash
   heroku ps:scale web=1
   ```

4. **Verify deployment:**
   ```bash
   heroku ps
   heroku logs --tail
   ```

5. **Test the API:**
   ```bash
   # Get your app URL
   heroku apps:info
   
   # Test health endpoint
   curl https://your-app-name.herokuapp.com/v1/health
   ```

## Expected API Endpoints

Once deployed, your API will be available at:
- `GET /v1/health` - Health check
- `GET /v1/version` - Version information
- `POST /v1/bp/compute-durations` - Blood pressure analysis
- `POST /v1/bp/compute/day` - Day-based BP analysis
- `POST /v1/bp/compute/duration` - Duration-based BP analysis
- `POST /v1/bp/recompute-batch` - Batch recomputation

## Troubleshooting

### Check logs:
```bash
heroku logs --tail
```

### Common issues:
1. **"No process types"** - Make sure Procfile is in Cardiowell-Analytics directory
2. **Port binding errors** - Ensure HOST=0.0.0.0 and using $PORT
3. **R package errors** - Check if all required R packages are available in Heroku R buildpack

### Verify environment variables:
```bash
heroku config
```

## Buildpack Information

Heroku should automatically detect this as an R application and use the R buildpack. If not, you can set it manually:

```bash
heroku buildpacks:set https://github.com/virtualstaticvoid/heroku-buildpack-r.git
```

### R Package Dependencies

The `Cardiowell-Analytics/init.R` file ensures the following packages are installed:
- `plumber` - Web API framework
- `jsonlite` - JSON parsing
- `dplyr` - Data manipulation
- `lubridate` - Date/time handling

The Heroku R buildpack will automatically execute `init.R` during the build process.

## Security Notes

- The R_ANALYTICS_TOKEN is used for API authentication
- Health and version endpoints are accessible without authentication
- All other endpoints require Bearer token authentication

## File Structure

```
Cardiowell-Analytics/
├── Procfile          # Heroku process definition
├── init.R            # R package dependencies
├── start.sh          # Startup script
├── plumber.R         # Main API router
├── bp.R              # Blood pressure analysis endpoints
├── .env              # Local environment variables (not deployed)
└── README.md         # Documentation
```
