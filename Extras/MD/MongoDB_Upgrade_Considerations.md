# MongoDB Upgrade Considerations: Version 6 to 8 Migration Guide

## Table of Contents

1. [Pre-Upgrade Awareness](#pre-upgrade-awareness)
2. [Codebase Changes Required](#codebase-changes-required)
3. [Critical Checks and Considerations](#critical-checks-and-considerations)

---

## Pre-Upgrade Awareness

### Version Compatibility and Upgrade Path

#### **Critical Upgrade Path Requirements**
- **Direct upgrades from MongoDB 6.0 to 8.0 are NOT supported**
- **Required upgrade path: 6.0 → 7.0 → 8.0**
- Each upgrade step requires Feature Compatibility Version (FCV) verification
- All replica set members must be running version 7.0 before upgrading to 8.0

#### **Version Support Timeline**
- MongoDB 6.0: Requires upgrade to 7.0 first
- MongoDB 7.0: Direct upgrade to 8.0 supported
- Sequential upgrades mandatory - no version skipping allowed

### Major Breaking Changes in MongoDB 7.0

#### **Queryable Encryption Incompatibility**
- **Critical**: MongoDB 7.0 Queryable Encryption GA is incompatible with 6.x Public Preview
- **Action Required**: Drop collections using `encryptedFields` before upgrading
- **Impact**: Complete re-implementation of Queryable Encryption required

#### **Legacy Components Removal**
- **mongo shell removed**: Replaced with `mongosh`
- **Legacy opcodes deprecated**: OP_INSERT, OP_DELETE, OP_UPDATE, OP_KILL_CURSORS
- **Driver updates mandatory**: Ensure compatible driver versions before upgrade

#### **Authentication Changes**
- **SCRAM-SHA-1 disabled by default** in FIPS mode
- **Intra-cluster authentication**: Only SCRAM-SHA-256 supported
- **LDAP authentication changes**: Review LDAP configurations

### Major Breaking Changes in MongoDB 8.0

#### **Query Behavior Changes**
- **`null` vs `undefined` handling**: Queries for `null` no longer match `undefined` fields
- **Impact**: Applications using `undefined` values may experience unexpected results
- **Mitigation**: Update data and queries to handle this behavior change

#### **Write Concern Changes**
- **"majority" write concern**: Returns acknowledgment when majority writes to oplog (not applies)
- **Performance improvement**: Faster "majority" writes but potential read consistency implications
- **Impact**: Immediate reads from secondaries may not reflect recent writes

#### **Deprecated Features**
- **Index filters deprecated**: Replace with Query Settings
- **Server-side JavaScript functions deprecated**: $accumulator, $function, $where
- **LDAP authentication deprecated**: Plan migration strategy
- **Hedged reads deprecated**: No longer default for `nearest` read preference

### Security and Compliance Considerations

#### **TLS/SSL Changes**
- **MongoDB 6.0+**: Enhanced certificate validation
- **Risk**: Improperly configured TLS may skip peer certificate validation
- **Action**: Verify TLS configuration before upgrade

#### **Audit Log Enhancements**
- **MongoDB 6.0**: Audit log encryption available (Enterprise)
- **MongoDB 8.0**: OCSF schema support for standardized audit logs
- **Planning**: Review audit requirements and configurations

#### **Queryable Encryption Evolution**
- **MongoDB 7.0**: GA version incompatible with 6.x preview
- **MongoDB 8.0**: Range queries support (≥ ≤ > < operators)
- **Migration**: Complete re-implementation required for Queryable Encryption users

---

## Codebase Changes Required

### Database Connection Configuration

#### **Current Project Configuration Analysis**
Based on project analysis, the current setup uses:
```javascript
// Current configuration in app.mjs and multiple service files
mongoose.connect(process.env.mongoUri, {
  useUnifiedTopology: true,
  useNewUrlParser: true,
  useFindAndModify: false,
})
```

#### **Recommended MongoDB 8.0 Configuration**
```javascript
// Enhanced configuration for MongoDB 8.0
await mongoose.connect(process.env.mongoUri, {
  useUnifiedTopology: true,
  useNewUrlParser: true,
  useFindAndModify: false,
  maxPoolSize: 10,
  serverSelectionTimeoutMS: 5000,
  socketTimeoutMS: 45000,
  bufferMaxEntries: 0,
  // New MongoDB 8.0 optimizations
  monitorCommands: true,
  autoEncryption: false // Configure if using Queryable Encryption
})
```

### Driver Compatibility Updates

#### **Current Project Status**
- **MongoDB Driver**: ^3.3.3 (CRITICAL - Requires upgrade)
- **Mongoose**: ^5.9.10 (CRITICAL - Requires upgrade)

#### **Required Driver Versions for MongoDB 8.0**
```json
{
  "dependencies": {
    "mongodb": "^6.0.0",  // MongoDB 8.0 requires driver 6.x
    "mongoose": "^8.0.0"  // Compatible with MongoDB 8.0
  }
}
```

#### **Driver Migration Steps**
1. **Phase 1: Update to MongoDB 7.0 compatible versions**
   ```json
   {
     "mongodb": "^5.9.0",
     "mongoose": "^7.6.0"
   }
   ```

2. **Phase 2: Update to MongoDB 8.0 compatible versions**
   ```json
   {
     "mongodb": "^6.3.0",
     "mongoose": "^8.1.0"
   }
   ```

### Query and Aggregation Changes

#### **Null vs Undefined Handling (MongoDB 8.0)**
```javascript
// BEFORE (MongoDB 6.0/7.0)
db.collection.find({ field: null })
// Returns documents where field is null, undefined, or missing

// AFTER (MongoDB 8.0)
db.collection.find({ field: null })
// Only returns documents where field is null or missing (NOT undefined)

// Migration strategy
db.collection.find({ 
  $or: [
    { field: null },
    { field: { $type: "undefined" } }
  ]
})
```

#### **Index Filter to Query Settings Migration**
```javascript
// DEPRECATED (Index Filters)
db.runCommand({
  planCacheSetFilter: "collectionName",
  query: { status: "active" },
  indexes: [{ name: 1, status: 1 }]
})

// NEW (Query Settings - MongoDB 8.0)
db.adminCommand({
  setQuerySettings: {
    find: "collectionName",
    filter: { status: "active" },
    $db: "databaseName"
  },
  settings: {
    indexHints: [{ name: 1, status: 1 }]
  }
})
```

### Time Series Collections Updates

#### **MongoDB 6.0 Time Series Limitations**
- Limited delete and update operations
- Basic secondary index support

#### **MongoDB 7.0+ Enhancements**
```javascript
// Enhanced time series operations
db.timeseries.deleteMany({
  timestamp: { $lt: new Date("2023-01-01") }
})

// Advanced aggregations with better performance
db.timeseries.aggregate([
  { $match: { device: "sensor1" } },
  { $group: { _id: "$location", avgTemp: { $avg: "$temperature" } } }
])
```

### Aggregation Pipeline Updates

#### **New MongoDB 7.0 Operators**
```javascript
// New $median and $percentile operators
db.metrics.aggregate([
  {
    $group: {
      _id: "$category",
      medianValue: { $median: "$value" },
      p95: { $percentile: { input: "$value", p: [0.95], method: "approximate" } }
    }
  }
])
```

#### **MongoDB 8.0 Express Query Optimization**
```javascript
// Simple _id queries automatically use EXPRESS_IXSCAN
db.collection.find({ _id: ObjectId("...") })
// No code changes needed - automatic optimization
```

### Authentication and Security Updates

#### **LDAP Configuration Changes**
```javascript
// MongoDB 8.0 - LDAP deprecated, plan migration
// Current LDAP configuration will work but log deprecation warnings
// Consider migrating to OIDC or other authentication methods

// Enhanced OIDC support (MongoDB 7.0+)
const oidcConfig = {
  oidcIdentityProviders: [{
    issuer: "https://your-provider.com",
    audience: "your-audience",
    clientId: "your-client-id"
  }]
}
```

### Error Handling Updates

#### **Enhanced Error Codes**
```javascript
// MongoDB 8.0 - Improved error handling for pipeline limits
try {
  await db.collection.aggregate(largePipeline)
} catch (error) {
  if (error.code === 16389) { // Pipeline stage limit exceeded
    // Handle pipeline size limit error
    console.log("Pipeline exceeds stage limit")
  }
}
```

---

## Critical Checks and Considerations

### Pre-Upgrade Data Integrity Verification

#### **1. Database Health Assessment**
```bash
# Check replica set status
db.runCommand("replSetGetStatus")

# Verify all nodes are healthy
db.runCommand("serverStatus").repl

# Check for any replication lag
db.printSlaveReplicationInfo()
```

#### **2. Index Analysis and Performance Review**
Based on project analysis, the following collections need immediate attention:

```javascript
// CRITICAL: Add missing indexes identified in project analysis
// PulseOximeterData - Missing indexes causing performance issues
db.PulseOximeterData.createIndex({ "imei": 1, "time": -1 })
db.PulseOximeterData.createIndex({ "imei": 1, "isTest": 1, "time": -1 })

// CellularBloodGlucoseData - High query targeting ratio
db.CellularBloodGlucoseData.createIndex({ "imei": 1, "ts": -1 })
db.CellularBloodGlucoseData.createIndex({ "imei": 1, "isTest": 1, "ts": -1 })

// Transtek_WS - Performance bottleneck
db.Transtek_WS.createIndex({ "imei": 1, "ts": -1 })
db.Transtek_WS.createIndex({ "imei": 1, "isTest": 1, "ts": -1 })
```

#### **3. Data Backup Strategy**
```bash
# Comprehensive backup before upgrade
mongodump --uri="mongodb://connection-string" --out=/backup/pre-upgrade-$(date +%Y%m%d)

# Verify backup integrity
mongorestore --dry-run --uri="mongodb://test-connection" /backup/pre-upgrade-*
```

### Feature Compatibility Version (FCV) Management

#### **FCV Upgrade Process**
```javascript
// 1. Check current FCV
db.adminCommand({ getParameter: 1, featureCompatibilityVersion: 1 })

// 2. Upgrade FCV after binary upgrade (7.0)
db.adminCommand({ setFeatureCompatibilityVersion: "7.0", confirm: true })

// 3. Upgrade FCV after binary upgrade (8.0)
db.adminCommand({ setFeatureCompatibilityVersion: "8.0", confirm: true })
```

#### **FCV Rollback Considerations**
- FCV changes require `confirm: true` parameter (MongoDB 7.0+)
- Rolling back FCV may require support assistance
- Test FCV changes in staging environment first

### Application Testing Strategy

#### **1. Staging Environment Setup**
```bash
# Create staging cluster matching production
# 1. Restore production data to staging
# 2. Update staging to target MongoDB version
# 3. Run comprehensive test suite
# 4. Monitor application behavior
```

#### **2. Critical Test Cases**
```javascript
// Test null vs undefined behavior (MongoDB 8.0)
const testData = [
  { _id: 1, name: null },
  { _id: 2, name: undefined },
  { _id: 3 } // missing field
]

// Verify query behavior changes
const results = await db.test.find({ name: null })
// Should return only _id: 1 and _id: 3 in MongoDB 8.0
```

#### **3. Performance Validation**
```javascript
// Monitor query performance improvements
db.setProfilingLevel(2)

// Test time series performance (expected 200%+ improvement in 8.0)
const timeSeriesQueries = [
  { $match: { timestamp: { $gte: startDate } } },
  { $group: { _id: "$device", count: { $sum: 1 } } }
]
```

### Driver and Dependency Updates

#### **1. Driver Compatibility Matrix**
| MongoDB Version | Min Driver Version | Recommended Version |
|-----------------|-------------------|-------------------|
| 6.0 | 4.0+ | 4.17+ |
| 7.0 | 5.0+ | 5.9+ |
| 8.0 | 6.0+ | 6.3+ |

#### **2. Application Dependencies Review**
```json
// Check all MongoDB-related dependencies
{
  "mongodb": "Check compatibility",
  "mongoose": "Major version updates required",
  "@mongodb-js/mongodb-driver": "Verify compatibility",
  "mongodb-memory-server": "Update for testing"
}
```

### Security and Compliance Verification

#### **1. Authentication System Validation**
```javascript
// Verify SCRAM-SHA-256 configuration
db.runCommand({ getParameter: 1, authenticationMechanisms: 1 })

// Test authentication after upgrade
db.auth("username", "password")
```

#### **2. Encryption Configuration**
```javascript
// For Queryable Encryption users (complete reimplementation required)
const clientEncryption = new ClientEncryption({
  keyVaultNamespace: "encryption.__keyVault",
  kmsProviders: kmsProviders,
  // MongoDB 8.0 supports range queries
  schemaMap: {
    "mydb.sensitive": {
      encryptMetadata: {
        encryptionAlgorithm: "Range" // New in 8.0
      }
    }
  }
})
```

### Monitoring and Observability

#### **1. Performance Metrics to Monitor**
```javascript
// Key metrics during and after upgrade
const metricsToMonitor = [
  "db.serverStatus().opcounters",
  "db.serverStatus().wiredTiger.cache",
  "db.serverStatus().connections",
  "db.serverStatus().network",
  // New in MongoDB 8.0
  "db.serverStatus().opWorkingTime",
  "db.serverStatus().queryAnalyzers"
]
```

#### **2. Index Performance Verification**
```javascript
// Verify query targeting improvements
db.setProfilingLevel(1, { slowms: 100 })

// Monitor for query targeting alerts reduction
// Target: < 100:1 scanned vs returned documents ratio
```

### Post-Upgrade Validation

#### **1. Data Integrity Verification**
```javascript
// Verify data consistency across all collections
db.runCommand({ validate: "collectionName", full: true })

// Check for any orphaned documents (sharded clusters)
db.runCommand({ cleanupOrphaned: "db.collection" })
```

#### **2. Application Performance Verification**
```bash
# Expected performance improvements in MongoDB 8.0:
# - 36% faster reads
# - 59% higher update throughput  
# - 200%+ faster time series queries
# - 17% improvement for Express queries

# Monitor application logs for:
# - Query execution times
# - Index usage patterns
# - Error rates
# - Memory usage patterns
```

#### **3. New Feature Utilization**
```javascript
// Test new MongoDB 8.0 features
// 1. Query Settings (replacing Index Filters)
db.adminCommand({
  setQuerySettings: { /* query shape */ },
  settings: { reject: true } // Block problematic queries
})

// 2. Enhanced time series operations
db.timeseries.deleteMany({ old: true })

// 3. Range queries on encrypted fields (if using Queryable Encryption)
db.encrypted.find({ 
  encryptedField: { $gte: 100, $lte: 200 } 
})
```

### Rollback Planning

#### **1. Rollback Procedures**
```bash
# Binary rollback (if needed)
# Note: Only single-version rollbacks supported
# 8.0 -> 7.0 (supported)
# 8.0 -> 6.0 (NOT supported)

# FCV rollback (may require support assistance)
db.adminCommand({ 
  setFeatureCompatibilityVersion: "7.0", 
  confirm: true 
})
```

#### **2. Rollback Limitations**
- **Backward-incompatible features**: Must be removed before rollback
- **Data written with new features**: May be inaccessible in older versions
- **Index format changes**: New indexes may not work with older versions
- **Time series collections**: Special considerations for rollback

### Success Metrics and Validation

#### **Expected Improvements Post-Upgrade**
1. **Performance Gains**:
   - 70-90% reduction in query scan ratios (after index implementation)
   - 30-50% improvement in patient dashboard loading
   - 40-70% improvement in device measurement queries

2. **Operational Benefits**:
   - Improved query optimization with Express paths
   - Better memory management with upgraded TCMalloc
   - Enhanced monitoring with new metrics

3. **Developer Experience**:
   - Persistent Query Settings (replacing Index Filters)
   - Improved error handling and diagnostics
   - Better aggregation performance

#### **Go/No-Go Criteria**
- ✅ All critical queries perform within acceptable limits
- ✅ No data integrity issues detected
- ✅ Authentication and authorization working correctly
- ✅ Application error rates within normal ranges
- ✅ Performance improvements measurable and significant
- ✅ No blocking issues for business operations

---

## Conclusion

Upgrading from MongoDB 6 to 8 is a significant undertaking that requires careful planning and execution. The performance improvements and new features in MongoDB 8.0 offer substantial benefits, but the breaking changes and deprecated features require thorough preparation.

**Key Success Factors:**
1. **Follow the sequential upgrade path**: 6.0 → 7.0 → 8.0
2. **Update drivers and dependencies** before beginning the upgrade
3. **Address performance issues** (particularly the identified indexing gaps) before upgrade
4. **Test thoroughly** in staging environments
5. **Plan for breaking changes** in query behavior and deprecated features
6. **Have a rollback strategy** ready, understanding the limitations

The investment in proper upgrade planning will pay dividends in improved performance, better scalability, and access to cutting-edge database features that will support your application's growth for years to come. 