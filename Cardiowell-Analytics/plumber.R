# plumber API entrypoint

library(plumber)

# Null-coalescing helper
`%||%` <- function(x, y) {
  if (is.null(x) || identical(x, "")) y else x
}

# Auth filter using shared token; allow health/version without auth
#* @filter tokenAuth
function(req, res) {
  path <- req$PATH_INFO %||% ""
  if (startsWith(path, "/v1/health") || startsWith(path, "/v1/version") ||
        startsWith(path, "/__docs__") || identical(path, "/openapi.json")) {
    forward()
  }
  token_expected <- Sys.getenv("R_ANALYTICS_TOKEN", unset = "")
  if (identical(token_expected, "")) {
    # No token configured → allow (development convenience)
    forward()
  }
  # Safely read Authorization header (can be missing)
  headers <- req$HEADERS
  hdr_auth <- NULL
  if (!is.null(headers) && is.list(headers) &&
        !is.null(headers[["authorization"]])) {
    hdr_auth <- headers[["authorization"]]
  }
  auth <- (req$HTTP_AUTHORIZATION %||% hdr_auth) %||% ""
  bearer <- paste0("Bearer ", token_expected)
  if (!identical(auth, bearer)) {
    res$status <- 401
    return(list(error = "unauthorized"))
  }
  forward()
}

#* Health check
#* @get /v1/health
function() {
  list(
    status = "ok",
    service = "cardiowell-analytics",
    timestamp = as.character(Sys.time())
  )
}

#* Version endpoint
#* @get /v1/version
function() {
  list(version = Sys.getenv("ANALYTICS_VERSION", unset = "0.1.0"))
}

# Build primary router from this file
root_pr <- pr()

# Mount bp.R endpoints explicitly so their annotated routes are registered
if (file.exists("bp.R")) {
  bp_pr <- pr("bp.R")
  # Mount at root to preserve paths like /v1/bp/compute/duration
  root_pr <- pr_mount(root_pr, "/", bp_pr)
}



#* Construct and return the Plumber router
#* @plumber
function(pr) {
  # Ensure bp.R is mounted when this file is plumbed
  if (file.exists("bp.R")) {
    bp_pr <- pr("bp.R")
    pr <- pr_mount(pr, "/", bp_pr)
  }
  pr
}
