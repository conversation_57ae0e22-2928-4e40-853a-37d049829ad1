# Load environment variables from .env file (local development only)
if [ -f .env ]; then
    echo "Loading environment variables from .env file (local development)..."
    export $(cat .env | grep -v '^#' | xargs)
else
    echo "No .env file found - using system environment variables (production)"
fi

# Display loaded environment variables for debugging
echo "R_ANALYTICS_TOKEN: ${R_ANALYTICS_TOKEN:+[SET]}"  # Shows [SET] if token exists, hides actual value
echo "ANALYTICS_VERSION: ${ANALYTICS_VERSION}"
echo "PORT: ${PORT}"
echo "HOST: ${HOST}"

# Start the R service
echo "Starting R analytics service..."
Rscript -e "pr <- plumber::pr('plumber.R'); plumber::pr_run(pr, host=Sys.getenv('HOST','0.0.0.0'), port=as.integer(Sys.getenv('PORT','8000')))"
