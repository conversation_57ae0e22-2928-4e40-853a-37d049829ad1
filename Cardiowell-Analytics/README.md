# Cardiowell-Analytics

R-based analytics service for blood pressure data processing and analysis. This service provides REST API endpoints for computing blood pressure metrics, classifications, and statistical analysis.

## Table of Contents
- [Prerequisites](#prerequisites)
- [Installation](#installation)
- [Configuration](#configuration)
- [Running the Service](#running-the-service)
- [API Endpoints](#api-endpoints)
- [Development](#development)
- [Production Deployment](#production-deployment)
- [Troubleshooting](#troubleshooting)

## Prerequisites

### System Requirements
- **R** (version 4.0 or higher)
- **Bash** (for startup script)
- **curl** (for testing, optional)

### R Packages Required
The following R packages need to be installed:

```r
# Install required packages
install.packages(c(
  "plumber",     # REST API framework
  "jsonlite",    # JSON processing
  "dplyr",       # Data manipulation
  "lubridate"    # Date/time handling
))
```

## Installation

### 1. Clone the Repository
```bash
git clone <repository-url>
cd Cardiowell-Analytics
```

### 2. Install R Dependencies
Open R console and run:
```r
install.packages(c("plumber", "jsonlite", "dplyr", "lubridate"))
```

Or use the R command line:
```bash
Rscript -e "install.packages(c('plumber', 'jsonlite', 'dplyr', 'lubridate'), repos='https://cran.rstudio.com/')"
```

### 3. Make Startup Script Executable
```bash
chmod +x start.sh
```

## Configuration

### Local Development
Create a `.env` file in the project root:
```bash
export PORT=8000
export HOST=0.0.0.0
export ANALYTICS_VERSION=v1.0.0-r
export R_ANALYTICS_TOKEN='your-secret-token-here'
```

### Production (Heroku/Cloud)
Set environment variables in your deployment platform:
- `PORT`: Service port (default: 8000)
- `HOST`: Service host (default: 0.0.0.0)
- `ANALYTICS_VERSION`: Version identifier
- `R_ANALYTICS_TOKEN`: Authentication token for API security

## Running the Service

### Using the Startup Script (Recommended)
```bash
./start.sh
```

### Manual Start (Alternative)
```bash
Rscript -e "pr <- plumber::pr('plumber.R'); plumber::pr_run(pr, host=Sys.getenv('HOST','0.0.0.0'), port=as.integer(Sys.getenv('PORT','8000')))"
```

### Verify Service is Running
```bash
curl http://localhost:8000/v1/health
# Expected response: {"status":["ok"],"service":["cardiowell-analytics"],"timestamp":["..."]}
```

## API Endpoints

### Public Endpoints (No Authentication Required)
- `GET /v1/health` - Health check
- `GET /v1/version` - Service version
- `GET /__docs__/` - API documentation (Swagger UI)
- `GET /openapi.json` - OpenAPI specification

### Protected Endpoints (Require Authentication)
- `POST /v1/bp/compute/duration` - Compute blood pressure metrics for a duration
- `POST /v1/bp/compute/day` - Compute daily blood pressure analysis

### Authentication
Protected endpoints require a Bearer token in the Authorization header:
```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
     -H "Content-Type: application/json" \
     -X POST http://localhost:8000/v1/bp/compute/duration \
     -d '{"patient_id": "patient_123", "duration": "7Days", "readings": [...]}'
```

### Example API Usage
```bash
# Health check
curl http://localhost:8000/v1/health

# Get version
curl http://localhost:8000/v1/version

# Compute BP metrics (with authentication)
curl -H "Authorization: Bearer YOUR_TOKEN" \
     -H "Content-Type: application/json" \
     -X POST http://localhost:8000/v1/bp/compute/duration \
     -d '{
       "patient_id": "patient_123",
       "duration": "7Days",
       "analysis_version": "v1.0.0-r",
       "readings": [
         {
           "sbp": 120,
           "dbp": 80,
           "pulse": 72,
           "reading_time": "2025-10-01T10:00:00Z"
         }
       ]
     }'
```

## Development

### Project Structure
```
Cardiowell-Analytics/
├── README.md          # This file
├── start.sh          # Startup script
├── .env              # Environment variables (local only)
├── plumber.R         # Main API router and authentication
└── bp.R              # Blood pressure analysis functions
```

### Running in Development Mode
1. Create `.env` file with your configuration
2. Start the service: `./start.sh`
3. Access Swagger docs: http://localhost:8000/__docs__/
4. Test endpoints using curl or Postman

### Code Linting
The project uses R linting standards:
- Lines should not exceed 80 characters
- Use implicit return (avoid explicit `return()` statements)
- Proper indentation (2 spaces)

## Production Deployment

### Heroku Deployment
1. **Set Config Vars:**
```bash
heroku config:set R_ANALYTICS_TOKEN="your-production-token"
heroku config:set ANALYTICS_VERSION="v1.0.0-r"
heroku config:set PORT=8000
heroku config:set HOST="0.0.0.0"
```

2. **Deploy:**
```bash
git add .
git commit -m "Deploy R analytics service"
git push heroku main
```

3. **Verify Deployment:**
```bash
curl https://your-app.herokuapp.com/v1/health
```

### Docker Deployment
```dockerfile
FROM r-base:4.0.0

# Install required R packages
RUN R -e "install.packages(c('plumber', 'jsonlite', 'dplyr', 'lubridate'), repos='https://cran.rstudio.com/')"

# Copy application files
COPY . /app
WORKDIR /app

# Make startup script executable
RUN chmod +x start.sh

# Expose port
EXPOSE 8000

# Start the service
CMD ["./start.sh"]
```

## Troubleshooting

### Common Issues

#### 1. R Packages Not Found
**Error:** `Error in library(plumber) : there is no package called 'plumber'`

**Solution:**
```bash
Rscript -e "install.packages(c('plumber', 'jsonlite', 'dplyr', 'lubridate'), repos='https://cran.rstudio.com/')"
```

#### 2. Permission Denied on start.sh
**Error:** `bash: ./start.sh: Permission denied`

**Solution:**
```bash
chmod +x start.sh
```

#### 3. Port Already in Use
**Error:** `Error: Failed to create server`

**Solution:**
```bash
# Find process using port 8000
lsof -i :8000
# Kill the process
kill -9 <PID>
# Or use a different port
export PORT=8001
./start.sh
```

#### 4. Authentication Errors (401 Unauthorized)
**Error:** `{"error":["unauthorized"]}`

**Solutions:**
- Verify `R_ANALYTICS_TOKEN` is set correctly
- Check that the token matches between client and server
- Ensure the Authorization header format: `Bearer YOUR_TOKEN`

#### 5. Environment Variables Not Loading
**Symptoms:** Service shows default values instead of configured ones

**Solutions:**
- Verify `.env` file exists and has correct format
- Check file permissions: `chmod 644 .env`
- Ensure no spaces around `=` in `.env` file
- For production, verify config vars are set in deployment platform

### Debug Mode
To run with verbose logging:
```bash
# Set debug environment variable
export PLUMBER_DEBUG=true
./start.sh
```

### Checking Service Status
```bash
# Check if service is running
curl -f http://localhost:8000/v1/health || echo "Service is down"

# Check service version and config
curl http://localhost:8000/v1/version

# Test authentication
curl -H "Authorization: Bearer YOUR_TOKEN" \
     -X POST http://localhost:8000/v1/bp/compute/duration \
     -H "Content-Type: application/json" \
     -d '{"test": "data"}'
```

## Environment Variables Reference

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `PORT` | Service port | 8000 | No |
| `HOST` | Service host | 0.0.0.0 | No |
| `ANALYTICS_VERSION` | Version identifier | v1.0.0-r | No |
| `R_ANALYTICS_TOKEN` | Authentication token | (empty) | Yes* |

*Required for production. If empty, authentication is disabled (development mode).

## Integration with Cardiowell Backend

This R analytics service is designed to work with the Cardiowell backend. The backend should be configured with:

```bash
# Backend environment variables
FEATURE_R_ANALYTICS=true
R_ANALYTICS_BASE_URL=http://localhost:8000
R_ANALYTICS_TOKEN=your-matching-token
R_ANALYTICS_TIMEOUT_MS=15000
```

## Support

For issues and questions:
1. Check this README for common solutions
2. Verify all prerequisites are installed
3. Check service logs for error messages
4. Ensure environment variables are correctly set

## License

[Add your license information here]
