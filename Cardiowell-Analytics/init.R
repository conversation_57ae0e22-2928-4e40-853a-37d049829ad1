install.packages(c(
  "plumber",
  "jsonlite",
  "dplyr",
  "lubridate"
), repos = "https://cran.rstudio.com/")

cat("Verifying package installations...\n")
required_packages <- c("plumber", "jsonlite", "dplyr", "lubridate")

for (pkg in required_packages) {
  if (require(pkg, character.only = TRUE, quietly = TRUE)) {
    cat(paste("✓", pkg, "installed successfully\n"))
  } else {
    cat(paste("✗", pkg, "installation failed\n"))
    quit(status = 1)
  }
}

cat("All required packages installed successfully!\n")
