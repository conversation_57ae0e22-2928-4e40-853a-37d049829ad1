import mongoose from "mongoose"

const FeatureToggleSchema = new mongoose.Schema(
  {
    feature: { type: String, required: true },
    scope: {
      type: String,
      required: true,
      enum: ["clinic", "patient"],
    },
    scopeId: { type: String, required: true },
    value: {
      type: String,
      required: true,
      enum: ["enabled", "disabled", "inherit"],
    },
  },
  { timestamps: true },
)

FeatureToggleSchema.index({ feature: 1, scope: 1, scopeId: 1 }, { unique: true })
FeatureToggleSchema.index({ scope: 1, scopeId: 1 }, { unique: false })

export const FeatureToggle = mongoose.model(
  "FeatureToggle",
  FeatureToggleSchema,
  "feature-toggles",
)
