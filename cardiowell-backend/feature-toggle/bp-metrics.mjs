import { findToggleByScope, clinicScope } from './feature-toggle.collection.mjs'
import { Device } from '../models/device.mjs'

/**
 * Determine if BP Metrics is enabled for the device's clinic.
 * Defaults to disabled if clinic or toggle not found.
 */
export const isBPMetricsEnabledForImei = async imei => {
  const device = await Device.findOne({ imei }).lean().exec()
  if (!device || !device.clinic) {
    return false
  }
  const clinicId = device.clinic
  const toggle = await findToggleByScope('BPMetrics', clinicScope(clinicId))
  if (!toggle) return false
  return toggle.value === 'enabled'
}


