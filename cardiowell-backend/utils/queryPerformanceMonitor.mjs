import mongoose from "mongoose"

/**
 * Monitor and log slow aggregation queries
 */
export const monitorAggregationPerformance = async (
  collection,
  pipeline,
  operationName = "Unknown",
) => {
  const startTime = Date.now()

  try {
    // Add explain stage to understand query execution
    const explanation = await collection
      .aggregate([...pipeline])
      .explain("executionStats")

    const result = await collection.aggregate(pipeline).toArray()
    const endTime = Date.now()
    const executionTime = endTime - startTime

    // Log performance metrics
    if (executionTime > 200) {
      // Log if query takes more than 200ms
      console.warn(`Slow Query Detected: ${operationName}`, {
        executionTimeMs: executionTime,
        documentsExamined: explanation?.stages?.[0]?.totalDocsExamined || "N/A",
        documentsReturned: result.length,
        indexesUsed: explanation?.stages?.[0]?.indexesUsed || "N/A",
        timestamp: new Date().toISOString(),
      })
    }

    return result
  } catch (error) {
    const endTime = Date.now()
    console.error(`Query Error: ${operationName}`, {
      executionTimeMs: endTime - startTime,
      error: error.message,
      timestamp: new Date().toISOString(),
    })
    throw error
  }
}

/**
 * Enhanced aggregation with performance monitoring
 */
export const performanceAwareAggregate = async (model, pipeline, operationName) => {
  return monitorAggregationPerformance(model.collection, pipeline, operationName)
}
