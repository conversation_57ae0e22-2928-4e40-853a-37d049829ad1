import axios from "axios"

/**
 * Colors for terminal output in logs
 */
const colors = {
  reset: "\x1b[0m",
  red: "\x1b[31m",
  yellow: "\x1b[33m",
  green: "\x1b[32m",
  blue: "\x1b[34m",
  cyan: "\x1b[36m",
}

/**
 * Formats duration with appropriate color coding for slow APIs
 * @param {number} duration - Duration in milliseconds
 * @returns {string} Formatted duration string with color codes
 */
function formatDuration(duration) {
  if (duration > 5000) {
    // Red for very slow (>5s)
    return `${colors.red}${duration}ms${colors.reset}`
  } else if (duration > 2000) {
    // Yellow for slow (>2s)
    return `${colors.yellow}${duration}ms${colors.reset}`
  } else if (duration > 1000) {
    // Cyan for moderate (>1s)
    return `${colors.cyan}${duration}ms${colors.reset}`
  } else {
    // Green for fast (<1s)
    return `${colors.green}${duration}ms${colors.reset}`
  }
}

/**
 * Wraps axios requests with timing and logging
 * @param {Object} config - Axios config object
 * @param {string} customLabel - Optional custom label for the API call
 * @returns {Promise} Axios response
 */
export async function timedApiCall(config, customLabel = null) {
  const start = Date.now()
  const url = config.url || config
  const method = (config.method || "GET").toUpperCase()
  const label = customLabel || `${method} ${url}`

  try {
    const response = await axios(config)
    const duration = Date.now() - start

    console.log(`[EXT-API] ${label} - ${formatDuration(duration)}`)

    return response
  } catch (error) {
    const duration = Date.now() - start

    console.error(
      `[EXT-API] ${colors.red}ERROR${colors.reset} ${label} - ${formatDuration(duration)} - ${error.message}`,
    )

    throw error
  }
}

/**
 * Wraps any async function with timing
 * @param {Function} fn - Async function to wrap
 * @param {string} label - Label for the operation
 * @returns {Function} Wrapped function
 */
export function timedOperation(fn, label) {
  return async (...args) => {
    const start = Date.now()

    try {
      const result = await fn(...args)
      const duration = Date.now() - start

      console.log(`[OPERATION] ${label} - ${formatDuration(duration)}`)

      return result
    } catch (error) {
      const duration = Date.now() - start

      console.error(
        `[OPERATION] ${colors.red}ERROR${colors.reset} ${label} - ${formatDuration(duration)} - ${error.message}`,
      )

      throw error
    }
  }
}

/**
 * Simple timing wrapper for any operation
 * @param {Function} operation - Function to time
 * @param {string} label - Label for the operation
 * @returns {Promise} Result of the operation
 */
export async function timeOperation(operation, label) {
  const start = Date.now()

  try {
    const result = await operation()
    const duration = Date.now() - start

    console.log(`[TIMER] ${label} - ${formatDuration(duration)}`)

    return result
  } catch (error) {
    const duration = Date.now() - start

    console.error(
      `[TIMER] ${colors.red}ERROR${colors.reset} ${label} - ${formatDuration(duration)} - ${error.message}`,
    )

    throw error
  }
}

/**
 * Sanitizes request data by removing sensitive information
 * @param {Object} data - Request data to sanitize
 * @returns {Object} Sanitized data
 */
function sanitizeRequestData(data) {
  if (!data || typeof data !== "object") return data

  const sensitive = [
    "password",
    "token",
    "secret",
    "key",
    "auth",
    "credit",
    "ssn",
    "email",
  ]
  const sanitized = { ...data }

  function sanitizeObject(obj) {
    if (!obj || typeof obj !== "object") return obj

    for (const [key, value] of Object.entries(obj)) {
      const lowerKey = key.toLowerCase()
      if (sensitive.some(s => lowerKey.includes(s))) {
        obj[key] = "[REDACTED]"
      } else if (typeof value === "object" && value !== null) {
        sanitizeObject(value)
      }
    }
  }

  sanitizeObject(sanitized)
  return sanitized
}

/**
 * Watchdog middleware to catch requests that might timeout on Heroku
 * Logs requests that are still running after 29 seconds (before H12 timeout)
 */
export function watchdogMiddleware() {
  return (req, res, next) => {
    const startTime = Date.now()
    const requestId = Math.random().toString(36).substr(2, 9)
    let finished = false
    let watchdogTimer = null

    // Store request context for timeout logging
    const requestContext = {
      id: requestId,
      method: req.method,
      url: req.originalUrl,
      userAgent: req.get("User-Agent"),
      ip: req.ip || req.connection.remoteAddress,
      params: sanitizeRequestData(req.params),
      query: sanitizeRequestData(req.query),
      body: sanitizeRequestData(req.body),
      headers: {
        "content-type": req.get("Content-Type"),
        "content-length": req.get("Content-Length"),
        "x-forwarded-for": req.get("X-Forwarded-For"),
      },
    }

    // Mark request as finished when response completes
    const markFinished = () => {
      finished = true
      if (watchdogTimer) {
        clearTimeout(watchdogTimer)
        watchdogTimer = null
      }
    }

    res.on("finish", markFinished)
    res.on("close", markFinished)
    res.on("error", markFinished)

    // Watchdog timer - fires at 29s to catch likely H12 timeouts
    watchdogTimer = setTimeout(() => {
      if (!finished) {
        const duration = Date.now() - startTime

        console.error(
          `${colors.red}[H12-TIMEOUT-LIKELY]${colors.reset} Request ${requestId} still running after ${duration}ms`,
        )
        console.error(
          `${colors.red}[TIMEOUT-CONTEXT]${colors.reset} ${JSON.stringify(
            {
              ...requestContext,
              duration,
              timestamp: new Date().toISOString(),
            },
            null,
            2,
          )}`,
        )

        // Optional: Send to external monitoring (Sentry, etc.)
        if (global.sentryClient) {
          global.sentryClient.captureMessage(
            `Potential H12 timeout: ${req.method} ${req.originalUrl}`,
            {
              level: "error",
              extra: { ...requestContext, duration },
            },
          )
        }
      }
    }, 29000) // 29 seconds - before Heroku's 30s limit

    // Also set earlier warning for very slow requests
    setTimeout(() => {
      if (!finished) {
        const duration = Date.now() - startTime
        console.warn(
          `${colors.yellow}[SLOW-REQUEST]${colors.reset} Request ${requestId} running for ${duration}ms - ${req.method} ${req.originalUrl}`,
        )
      }
    }, 15000) // 15 second warning

    // Store request context in res.locals for use by other middleware
    res.locals.requestContext = requestContext
    res.locals.requestStartTime = startTime

    next()
  }
}

/**
 * Database query timing wrapper that integrates with request context
 * @param {Function} queryFn - Database query function
 * @param {string} queryDescription - Description of the query
 * @param {Object} req - Express request object (optional)
 * @returns {Promise} Query result
 */
export async function timedDbQuery(queryFn, queryDescription, req = null) {
  const start = Date.now()
  const requestId = req?.res?.locals?.requestContext?.id || "standalone"

  try {
    const result = await queryFn()
    const duration = Date.now() - start

    const logMessage = `[DB-QUERY] ${queryDescription} - ${formatDuration(duration)} (req: ${requestId})`

    if (duration > 10000) {
      // > 10 seconds
      console.error(`${colors.red}${logMessage}${colors.reset}`)
    } else if (duration > 5000) {
      // > 5 seconds
      console.warn(`${colors.yellow}${logMessage}${colors.reset}`)
    } else {
      console.log(logMessage)
    }

    return result
  } catch (error) {
    const duration = Date.now() - start
    console.error(
      `[DB-QUERY] ${colors.red}ERROR${colors.reset} ${queryDescription} - ${formatDuration(duration)} (req: ${requestId}) - ${error.message}`,
    )
    throw error
  }
}

/**
 * Creates a comprehensive slow request logger that combines all timing info
 * @param {number} thresholdMs - Threshold in milliseconds to consider a request slow
 * @returns {Function} Express middleware
 */
export function slowRequestLogger(thresholdMs = 5000) {
  return (req, res, next) => {
    const originalSend = res.send
    const startTime = Date.now()

    res.send = function (data) {
      const duration = Date.now() - startTime

      if (duration > thresholdMs) {
        const context = res.locals.requestContext || {}
        console.error(
          `${colors.red}[SLOW-REQUEST-COMPLETE]${colors.reset} ${req.method} ${req.originalUrl} completed in ${formatDuration(duration)}`,
        )
        console.error(
          `${colors.red}[SLOW-CONTEXT]${colors.reset} ${JSON.stringify(
            {
              ...context,
              duration,
              responseSize: data ? data.length : 0,
              statusCode: res.statusCode,
              completedAt: new Date().toISOString(),
            },
            null,
            2,
          )}`,
        )
      }

      return originalSend.call(this, data)
    }

    next()
  }
}
