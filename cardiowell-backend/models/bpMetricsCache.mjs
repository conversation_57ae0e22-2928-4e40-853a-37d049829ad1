import mongoose from "mongoose"

const BpMetricsCacheSchema = new mongoose.Schema(
  {
    patientId: { type: mongoose.Schema.Types.ObjectId, index: true },
    imei: { type: String, required: true, index: true },
    rangeStart: { type: String, required: false },
    rangeEnd: { type: String, required: false },
    lastReadingTsIncluded: { type: Number, required: false },
    computedAt: { type: Date, required: true, default: () => new Date() },
    version: { type: String, required: true, default: "1.0.0" },
    source: { type: String, required: true, default: "on-demand-dashboard" },
    data: { type: Object, required: true },
  },
  { timestamps: true },
)

BpMetricsCacheSchema.index(
  { patientId: 1, imei: 1, rangeStart: 1, rangeEnd: 1 },
  { unique: false },
)
BpMetricsCacheSchema.index({ imei: 1, lastReadingTsIncluded: -1 })

export const BpMetricsCache = mongoose.model(
  "BpMetricsCache",
  BpMetricsCacheSchema,
  "bp_metrics_cache",
)


