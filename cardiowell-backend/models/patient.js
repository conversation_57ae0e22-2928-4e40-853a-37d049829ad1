const mongoose = require("mongoose")

const PatientSchema = new mongoose.Schema(
  {
    firstName: String,
    lastName: String,
    cellNumber: String,
    homeNumber: String,
    MRN: String,
    email: String,
    address: String,
    city: String,
    state: String,
    zip: String,
    timeZone: String,
    bpIMEI: String,
    ttBpIMEI: String,
    adBpIMEI: String,
    weightIMEI: String,
    ttWeightIMEI: String,
    glucoseIMEI: String,
    pulseIMEI: String,
    iGlucoseDeviceId: String,
    clinic: String,
    password: String,
    height: String,
    weight: String,
    username: String,
    selectedBpDevice: String,
    selectedWeightDevice: String,
    deviceNotificationsEnabled: {
      type: Boolean,
      default: false,
    },
    thresholdId: String,
    programId: String,
    targetWeight: String,
    birthdate: String,
    gender: String,
    ethnicity: String,
    maritalStatus: String,
    education: String,
    employment: String,
    income: String,
    language: String,
    socialConnectedness: String,
    lastUpdated: Number,
    allergies: [String],
    chronicConditions: [String],
    hypertensionMedications: [String],
    medications: [String],
    medicationTime: [
      {
        medication: String,
        time: String,
      },
    ],
    patientZip: String,
    pregnant: Boolean,
    showTestData: Boolean,
    registrationCompleted: {
      type: Boolean,
      default: false,
    },
  },
  {
    toObject: { virtuals: true },
    toJSON: { virtuals: true },
  },
)

PatientSchema.methods.getAnyBpIMEI = function () {
  if (this.bpIMEI) {
    return this.bpIMEI
  }
  if (this.ttBpIMEI) {
    return this.ttBpIMEI
  }
  if (this.adBpIMEI) {
    return this.adBpIMEI
  }
  return null
}

PatientSchema.virtual("role").get(function () {
  return "patient"
})

PatientSchema.index({ bpIMEI: 1 })
PatientSchema.index({ ttBpIMEI: 1 })
PatientSchema.index({ adBpIMEI: 1 })
PatientSchema.index({ weightIMEI: 1 })
PatientSchema.index({ ttWeightIMEI: 1 })
PatientSchema.index({ glucoseIMEI: 1 })
PatientSchema.index({ pulseIMEI: 1 })
PatientSchema.index({ clinic: 1 })
PatientSchema.index({ email: 1 })
PatientSchema.index({ username: 1 })

module.exports = mongoose.model("Patient", PatientSchema)
