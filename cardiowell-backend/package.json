{"name": "patient-care-backend", "version": "1.0.0", "description": "", "main": "app.mjs", "scripts": {"start": "node app.mjs", "test": "vitest", "lint": "eslint '**/*.{mjs,js}'", "build:loader": "rollup -c", "pkg:loader": "pkg dist/testDataLoader.js --targets node14-linux-x64,node14-macos-x64,node14-win-x64 --output dist/testDataLoader", "auth:patient": "node scripts/authPatient.mjs auth", "bp:init": "node scripts/test-bp-pipeline.mjs init", "bp:init-schema": "node scripts/r-bp/initDatabaseSchema.mjs", "bp:test-ram": "node scripts/test-bp-pipeline.mjs test-ram", "bp:test-ram-all": "node scripts/test-bp-pipeline.mjs test-ram --all", "bp:process-day": "node scripts/test-bp-pipeline.mjs process-day", "bp:process-all": "node scripts/test-bp-pipeline.mjs process-all", "bp:list-days": "node scripts/test-bp-pipeline.mjs list-days", "bp:get-results": "node scripts/test-bp-pipeline.mjs get-results", "analytics:health": "node scripts/r-bp/phase6Tester.mjs", "analytics:emit": "node scripts/r-bp/phase6Tester.mjs 352847091234567"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@date-fns/utc": "^1.2.0", "@faker-js/faker": "^8.4.1", "@sendgrid/mail": "^6.5.5", "@sentry/node": "^9.1.0", "@sentry/profiling-node": "^9.1.0", "axios": "^1.6.8", "bcrypt": "^5.0.0", "body-parser": "^1.18.2", "bufferutil": "^4.0.1", "cli-table3": "^0.6.5", "commander": "^13.1.0", "connect-busboy": "0.0.2", "connect-mongo": "^5.1.0", "cookie-parser": "^1.4.5", "cors": "^2.8.4", "cron": "^1.8.2", "csv-parser": "^3.2.0", "date-fns": "^3.6.0", "date-fns-tz": "^3.1.3", "dotenv": "^8.2.0", "errorhandler": "^1.0.0", "escape-string-regexp": "^5.0.0", "express": "^4.16.3", "express-jwt": "^6.0.0", "express-session": "^1.17.1", "express-validator": "^7.0.1", "file-system": "^2.2.2", "form-data": "^4.0.2", "fs-extra": "^8.1.0", "jsonwebtoken": "^8.5.1", "logger": "0.0.1", "moment": "^2.25.1", "moment-timezone": "^3.1.3", "mongodb": "^3.3.3", "mongoose": "^5.9.10", "morgan": "^1.9.1", "multer": "^1.4.5-lts.1", "nanoid": "^5.0.7", "node-fetch": "^2.6.1", "node-imei": "^1.0.8", "node-schedule": "^1.3.2", "nodemailer": "^6.3.1", "nodemon": "^2.0.7", "openai": "^4.78.1", "passport": "^0.4.0", "passport-local": "^1.0.0", "passport-local-mongoose": "^5.0.1", "path": "^0.12.7", "query-string": "^9.0.0", "request": "^2.88.0", "response-time": "^2.3.4", "socket.io": "^2.3.0", "stripe": "^8.29.0", "twilio": "^3.43.0", "utf-8-validate": "^5.0.2", "uuid": "^11.0.5", "zod": "^3.24.2"}, "devDependencies": {"@eslint/compat": "^1.2.3", "@rollup/plugin-commonjs": "^28.0.3", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^16.0.1", "eslint": "^9.15.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.37.2", "open": "^10.2.0", "pkg": "^5.8.1", "prettier": "^3.3.3", "rollup": "^4.36.0", "vitest": "^1.4.0", "vitest-fetch-mock": "^0.2.2"}, "pkg": {"targets": ["node14-linux-x64", "node14-macos-x64", "node14-win-x64"], "output": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "packageManager": "yarn@1.22.22"}