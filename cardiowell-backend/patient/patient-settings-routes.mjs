import { Router } from "express"
import { authenticatedPatient } from "../routes/authenticatedPatient.mjs"
import { isBPBuddyEnabled } from "../patient-assistant/feature-toggle.mjs"

export const patientSettingsRouter = Router()

patientSettingsRouter.route("/settings").get(authenticatedPatient, async (req, res) => {
  const patient = req.user
  const features = {
    BPBuddy: await isBPBuddyEnabled(patient),
  }

  return res.status(200).json({
    features,
  })
})
