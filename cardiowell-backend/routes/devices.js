var express = require("express")
var router = express.Router()
const mongoose = require("mongoose")
const axios = require("axios")
var Customer = require("../models/customer")
const WithingsBloodPressureData = require("../models/withingsBloodPressureData")
const TranstekBloodGlucoseData = require("../models/cellularBloodGlucoseData")
const WithingsUserData = require("../models/withingsUserData")

// Phase 2: Import BP Event System (using dynamic import for CommonJS compatibility)
let emitNewBPReading = null;
(async () => {
  try {
    const { emitNewBPReading: emitBP } = await import("../scripts/r-bp/bpEventSystem.mjs");
    emitNewBPReading = emitBP;
    console.log("✅ BP Event System loaded in devices.js");
  } catch (error) {
    console.error("❌ Failed to load BP Event System:", error);
  }
})();

mongoose.connect(process.env.mongoUri, {
  useUnifiedTopology: true,
  useNewUrlParser: true,
  useFindAndModify: false,
})
mongoose.Promise = global.Promise
const db = mongoose.connection

const apiKey = "WUkhtE6te6Ib9TXKBNkoyL0dADiUUpsItKUZRQ595f5YIqnIl232tGUjB3Pvxent"

router.post("/deviceData", function (request, response) {
  for (var key in request.body) {
    if (Object.prototype.hasOwnProperty.call(request.body, key)) {
      if (key === "values") {
        var objValues = request.body[key]
        Customer.findOne({ imei: request.body["imei"] }, function (err, customer) {
          if (err) {
            return response.status(500).send({ message: "Error: " + err })
          }
          if (customer) {
            axios
              .post(customer.endpoint, request.body)
              .then(res => {
                if (parseInt(res.status) / 100 !== 2) {
                  if (objValues["pulse"]) {
                    var collection = db.collection("BT_BPM")
                    var date = new Date()
                    collection.save(
                      {
                        imei: request.body["imei"],
                        ts: parseInt(request.body["ts"]),
                        batteryVoltage: parseInt(request.body["batteryVoltage"]),
                        signalStrength: parseInt(request.body["signalStrength"]),
                        systolic: parseInt(objValues["systolic"]),
                        diastolic: parseInt(objValues["diastolic"]),
                        pulse: parseInt(objValues["pulse"]),
                        unit: parseInt(objValues["unit"]),
                        _created_at: date.toISOString(),
                        _updated_at: date.toISOString(),
                      },
                      function (error, model) {
                        if (error) {
                          return response.status(500).send({
                            message:
                              "CardioWell failed to create new object, with error code: " +
                              error.message,
                          })
                        } else {
                          // Phase 2: Emit BP event for R analysis processing
                          if (emitNewBPReading) {
                            emitNewBPReading(request.body["imei"], 'generic', 'BT_BPM', new Date().toISOString());
                          }
                        }
                      },
                    )
                    Customer.findOneAndUpdate(
                      { imei: request.body["imei"] },
                      {
                        $set: {
                          status: "fail",
                        },
                      },
                      function (error, models) {
                        if (error) {
                          return response.status(500).send({
                            message:
                              "CardioWell failed to create new object, with error code: " +
                              error.message,
                          })
                        } else {
                          return response.status(201).send({
                            message: "CardioWell bloodPressureMonitorData POST!",
                          })
                        }
                      },
                    )
                  } else {
                    var collection = db.collection("BT_WS") // eslint-disable-line no-redeclare
                    var date = new Date() // eslint-disable-line no-redeclare
                    collection.save(
                      {
                        imei: request.body["imei"],
                        ts: parseInt(request.body["ts"]),
                        batteryVoltage: parseInt(request.body["batteryVoltage"]),
                        signalStrength: parseInt(request.body["signalStrength"]),
                        unit: parseInt(objValues["unit"]),
                        weight: parseInt(objValues["weight"]),
                        _created_at: date.toISOString(),
                        _updated_at: date.toISOString(),
                      },
                      function (error, model) {
                        if (error) {
                          return response.status(500).send({
                            message:
                              "CardioWell failed to create new object, with error code: " +
                              error.message,
                          })
                        }
                      },
                    )
                    Customer.findOneAndUpdate(
                      { imei: request.body["imei"] },
                      {
                        $set: {
                          status: "fail",
                        },
                      },
                      function (error, models) {
                        if (error) {
                          return response.status(500).send({
                            message:
                              "CardioWell failed to create new object, with error code: " +
                              error.message,
                          })
                        } else {
                          return response
                            .status(201)
                            .send({ message: "CardioWell weightScaleData POST!" })
                        }
                      },
                    )
                  }
                } else {
                  Customer.findOneAndUpdate(
                    { imei: request.body["imei"] },
                    {
                      $set: {
                        status: "pass",
                      },
                    },
                    function (error, models) {
                      if (error) {
                        return response.status(500).send({
                          message:
                            "CardioWell failed to create new object, with error code: " +
                            error.message,
                        })
                      }
                    },
                  )
                }
              })
              .catch(error => {
                console.error(error)
              })
          } else {
            if (objValues["pulse"]) {
              var collection = db.collection("BT_BPM")
              var date = new Date()
              collection.save(
                {
                  imei: request.body["imei"],
                  ts: parseInt(request.body["ts"]),
                  batteryVoltage: parseInt(request.body["batteryVoltage"]),
                  signalStrength: parseInt(request.body["signalStrength"]),
                  systolic: parseInt(objValues["systolic"]),
                  diastolic: parseInt(objValues["diastolic"]),
                  pulse: parseInt(objValues["pulse"]),
                  unit: parseInt(objValues["unit"]),
                  _created_at: date.toISOString(),
                  _updated_at: date.toISOString(),
                },
                function (error, model) {
                  if (error) {
                    return response.status(500).send({
                      message:
                        "CardioWell failed to create new object, with error code: " +
                        error.message,
                    })
                  } else {
                    // Phase 2: Emit BP event for R analysis processing
                    if (emitNewBPReading) {
                      emitNewBPReading(request.body["imei"], 'generic', 'BT_BPM', new Date().toISOString());
                    }
                    
                    return response
                      .status(201)
                      .send({ message: "CardioWell bloodPressureMonitorData POST!" })
                  }
                },
              )
            } else {
              var collection = db.collection("BT_WS") // eslint-disable-line no-redeclare
              var date = new Date() // eslint-disable-line no-redeclare
              collection.save(
                {
                  imei: request.body["imei"],
                  ts: parseInt(request.body["ts"]),
                  batteryVoltage: parseInt(request.body["batteryVoltage"]),
                  signalStrength: parseInt(request.body["signalStrength"]),
                  unit: parseInt(objValues["unit"]),
                  weight: parseInt(objValues["weight"]),
                  _created_at: date.toISOString(),
                  _updated_at: date.toISOString(),
                },
                function (error, model) {
                  if (error) {
                    return response.status(500).send({
                      message:
                        "CardioWell failed to create new object, with error code: " +
                        error.message,
                    })
                  } else {
                    return response
                      .status(201)
                      .send({ message: "CardioWell weightScaleData POST!" })
                  }
                },
              )
            }
          }
        })
      }
    }
  }
})

const authorize = (request, response, next) => {
  const authHeader = request.headers["authorization"]
  if (!authHeader || authHeader !== apiKey) {
    return response.status(401).json({ error: "Forbidden" })
  }
  return next()
}

const verifyBody = (request, response, next) => {
  if (!request.body) {
    return response.status(400).send({ message: "Missing request body" })
  }

  if (!request.body["deviceType"]) {
    return response.status(400).send({ message: "Please specify device type" })
  }

  if (!request.body["deviceId"] && !request.body["imei"] && !request.body["macAddress"]) {
    return response.status(400).send({ message: "Please provide a device identifier" })
  }

  return next()
}

router.get("/data", authorize, verifyBody, async (request, response) => {
  try {
    const body = request.body
    const deviceType = body["deviceType"]
    if (body["imei"]) {
      const imei = body["imei"]
      // Blood Pressure
      if (deviceType == 1) {
        const arrayBpm = await db
          .collection("BT_BPM")
          .find({ imei })
          .sort({ _created_at: -1 })
          .toArray()
        if (arrayBpm && arrayBpm.length > 0) {
          return response.status(200).send({ data: arrayBpm })
        }
        const ttArrayBpm = await db
          .collection("Transtek_BPM")
          .find({ imei })
          .sort({ ts: -1 })
          .toArray()
        if (ttArrayBpm && ttArrayBpm.length > 0) {
          return response.status(200).send({ data: ttArrayBpm })
        }
      }
      // Glucose
      if (deviceType == 2) {
        const arrayGlucose = await db
          .collection("CellularBloodGlucoseData")
          .find({ imei })
          .sort({ ts: -1 })
          .toArray()
        if (arrayGlucose && arrayGlucose.length > 0) {
          return response.status(200).send({ data: arrayGlucose })
        }
      }
      // Weight
      if (deviceType == 3) {
        const arrayWS = await db
          .collection("BT_WS")
          .find({ imei })
          .sort({ _created_at: -1 })
          .toArray()
        if (arrayWS && arrayWS.length > 0) {
          return response.status(200).send({ data: arrayWS })
        }
        const ttArrayWS = await db
          .collection("Transtek_WS")
          .find({ imei })
          .sort({ ts: -1 })
          .toArray()
        if (ttArrayWS && ttArrayWS.length > 0) {
          return response.status(200).send({ data: ttArrayWS })
        }
      }
      // Pulse
      if (deviceType == 4) {
        const arrayPulse = await db
          .collection("CellularBloodGlucoseData")
          .find({ imei })
          .sort({ ts: -1 })
          .toArray()
        if (arrayPulse && arrayPulse.length > 0) {
          return response.status(200).send({ data: arrayPulse })
        }
      }

      // Withings Device
    } else if (body["deviceId"]) {
      if (deviceType == 1) {
        const data = await WithingsBloodPressureData.find({
          deviceId: body["deviceId"],
        }).sort({ created: -1 })
        if (data && data.length > 0) {
          return response.status(200).send({ data })
        }
      }

      // Withings Device
    } else if (body["macAddress"]) {
      if (deviceType == 1) {
        const data = await WithingsUserData.findOne({
          "devices.macAddress": body["macAddress"],
        }).then(user => {
          if (user) {
            const devices = user.devices.map(device => device.deviceId)
            return WithingsBloodPressureData.find({ deviceId: { $in: devices } })
          }
          return null
        })
        if (data && data.length > 0) {
          return response.status(200).send({ data })
        }
      }
    }
    return response.status(404).send({ message: "Device not found" })
  } catch (err) {
    console.error(err)
    return response.status(500).json(err)
  }
})

router.post("/forwarddata", authorize, verifyBody, async (request, response) => {
  // send data to a callback url
})

module.exports = router
