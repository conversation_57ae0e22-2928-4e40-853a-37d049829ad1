import { Router } from 'express';
// Minimal Phase 1: Use normalized fetcher and local compute utilities.
import { fetchBPReadings } from '../scripts/r-bp/dataFetcher.mjs';
import { computeMetricsFromReadings } from '../scripts/r-bp/computeMetrics.mjs';
import { saveMetricsToCache, getLatestMetricsFromCache } from '../scripts/r-bp/cacheService.mjs';
import { isBPMetricsEnabledForImei } from '../feature-toggle/bp-metrics.mjs'

const bpMetricsRouter = Router();

// GET /api/bp-metrics/:imei
// Computes Phase 1 metrics on demand for optional start/end/tz
bpMetricsRouter.get('/:imei', async (req, res) => {
  try {
    const { imei } = req.params
    const { start, end, tz } = req.query

    const rangeStart = start || null
    const rangeEnd = end || null
    const timezone = tz || 'UTC'

    // Feature toggle enforcement at clinic level
    const enabled = await isBPMetricsEnabledForImei(imei)
    if (!enabled) {
      return res.status(403).json({ success: false, message: 'BP Metrics feature is disabled for this clinic', data: null })
    }

    const readings = await fetchBPReadings(imei, rangeStart, rangeEnd)

    const metrics = computeMetricsFromReadings(readings, {
      tz: timezone,
      rangeStart: rangeStart || null,
      rangeEnd: rangeEnd || null,
    })

    // Best-effort cache write (non-blocking failure)
    try {
      const lastTs = Array.isArray(readings) && readings.length > 0
        ? (readings[readings.length - 1].ts || new Date(readings[readings.length - 1].readingAtUTC).getTime())
        : null
      await saveMetricsToCache({
        patientId: null,
        imei,
        rangeStart: rangeStart || null,
        rangeEnd: rangeEnd || null,
        lastReadingTsIncluded: lastTs,
        data: metrics,
      })
    } catch (e) {
      console.warn('bp-metrics cache write failed:', e?.message)
    }

    const response = {
      success: true,
      requestTime: new Date().toISOString(),
      ...metrics,
    }

    return res.status(200).json(response)
  } catch (error) {
    console.error('API Error getting duration metrics:', error)
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      data: null,
    })
  }
})

/**
 * GET /api/bp-metrics/:imei/summary
 * Get metrics summary for a patient
 */
// Keep placeholder endpoints but return 501 for now to avoid breaking callers.
bpMetricsRouter.get('/:imei/summary', async (req, res) => {
  try {
    const { imei } = req.params
    const { start, end } = req.query
    const doc = await getLatestMetricsFromCache(imei, { rangeStart: start || null, rangeEnd: end || null })
    if (!doc) return res.status(200).json({ success: true, message: 'No cached metrics', data: null })
    return res.status(200).json({ success: true, data: doc })
  } catch (e) {
    console.error('bp-metrics summary error:', e)
    return res.status(500).json({ success: false, message: 'Internal server error', data: null })
  }
})

/**
 * GET /api/bp-metrics/:imei/:duration
 * Get detailed metrics for a specific duration
 */
bpMetricsRouter.get('/:imei/:duration', async (req, res) => {
  return res.status(501).json({ success: false, message: 'Not implemented in Phase 1', data: null })
})

/**
 * GET /api/bp-metrics/patients/all
 * Get all patients with available metrics
 */
bpMetricsRouter.get('/patients/all', async (req, res) => {
  return res.status(501).json({ success: false, message: 'Not implemented in Phase 1', data: null })
})

/**
 * GET /api/bp-metrics/health
 * Health check endpoint
 */
bpMetricsRouter.get('/health', async (req, res) => {
  try {
    res.status(200).json({
      success: true,
      message: 'BP Metrics API is healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Health check failed',
      error: error.message
    });
  }
});

export default bpMetricsRouter;

/**
 * POST /api/bp-metrics/:imei/refresh
 * Triggers recomputation for all durations
 */
bpMetricsRouter.post('/:imei/refresh', async (req, res) => {
  return res.status(501).json({ success: false, message: 'Not implemented in Phase 1' })
})

/**
 * GET /api/bp-metrics/:imei/:duration/table
 * Returns flattened table of readings for charts/exports
 */
bpMetricsRouter.get('/:imei/:duration/table', async (req, res) => {
  return res.status(501).json({ success: false, message: 'Not implemented in Phase 1', data: null })
})

/**
 * GET /api/bp-metrics/:imei/:duration/histogram
 * Returns histogram bins for SBP/DBP
 */
bpMetricsRouter.get('/:imei/:duration/histogram', async (req, res) => {
  return res.status(501).json({ success: false, message: 'Not implemented in Phase 1', data: null })
})

/**
 * GET /api/bp-metrics/:imei/:duration/scatter
 * Returns scatter points of SBP vs DBP
 */
bpMetricsRouter.get('/:imei/:duration/scatter', async (req, res) => {
  return res.status(501).json({ success: false, message: 'Not implemented in Phase 1', data: null })
})






