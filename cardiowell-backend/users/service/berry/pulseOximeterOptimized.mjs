// Optimized pulse oximeter lookup - only runs when patient has pulseIMEI device
export const pulseOximeterOptimizedStage = (milliseconds, isOverview, isTest) => {
  return [
    {
      $lookup: {
        from: "PulseOximeterData",
        let: {
          imei: "$pulseIMEI",
        },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ["$imei", "$$imei"] },
                  { $ne: ["$isTest", true] },
                  { $gte: ["$time", milliseconds] },
                ],
              },
            },
          },
          { $sort: { time: -1 } },
          ...(isOverview ? [{ $limit: 5 }] : []),
        ],
        as: "pulse",
      },
    },
  ]
}

// Conditional lookup with device check
export const conditionalPulseOximeterStage = (milliseconds, isOverview, isTest) => {
  return [
    {
      $lookup: {
        from: "PulseOximeterData",
        let: {
          imei: "$pulseIMEI",
        },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ["$imei", "$$imei"] },
                  { $ne: ["$isTest", true] },
                  { $gte: ["$time", milliseconds] },
                ],
              },
            },
          },
          { $sort: { time: -1 } },
          ...(isOverview ? [{ $limit: 5 }] : []),
        ],
        as: "pulse",
      },
    },
  ]
}
