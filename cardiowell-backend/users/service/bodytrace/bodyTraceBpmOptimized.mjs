// Optimized BodyTrace BPM lookup - only runs when patient has bpIMEI device
export const bodyTraceBpmOptimizedStage = (milliseconds, isOverview, isTest) => {
  return [
    {
      $lookup: {
        from: "BT_BPM",
        let: {
          imei: "$bpIMEI",
        },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ["$imei", "$$imei"] },
                  { $ne: ["$isTest", true] },
                  { $gte: ["$ts", milliseconds] },
                ],
              },
            },
          },
          { $sort: { _created_at: -1 } },
          ...(isOverview ? [{ $limit: 5 }] : []),
        ],
        as: "bpm",
      },
    },
  ]
}

// Conditional lookup - only runs if patient has bpIMEI
export const conditionalBodyTraceBpmStage = (milliseconds, isOverview, isTest) => {
  return [
    {
      $lookup: {
        from: "BT_BPM",
        let: {
          imei: "$bpIMEI",
          hasDevice: { $ne: ["$bpIMEI", null] },
        },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ["$imei", "$$imei"] },
                  { $ne: ["$isTest", true] },
                  { $gte: ["$ts", milliseconds] },
                ],
              },
            },
          },
          { $sort: { _created_at: -1 } },
          ...(isOverview ? [{ $limit: 5 }] : []),
        ],
        as: "bpm",
      },
    },
  ]
}
