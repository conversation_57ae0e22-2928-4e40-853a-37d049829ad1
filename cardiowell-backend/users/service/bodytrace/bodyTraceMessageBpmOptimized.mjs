// Optimized BodyTrace message BPM lookup - only runs when patient has bpIMEI device
export const bodyTraceMessageBpmOptimizedStage = (milliseconds, isOverview, isTest) => {
  const matchBpmValues = [
    { $ne: [{ $type: "$message.values.systolic" }, "missing"] },
    { $ne: [{ $type: "$message.values.diastolic" }, "missing"] },
  ]

  return [
    {
      $lookup: {
        from: "bodytracemessages",
        let: {
          imei: "$bpIMEI",
        },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ["$message.imei", "$$imei"] },
                  { $ne: ["$message.isTest", true] },
                  ...matchBpmValues,
                  { $gte: ["$message.ts", milliseconds] },
                ],
              },
            },
          },
          { $sort: { createdAt: -1 } },
          ...(isOverview ? [{ $limit: 5 }] : []),
        ],
        as: "btMessagesBpm",
      },
    },
  ]
}

// Conditional lookup with device check
export const conditionalBodyTraceMessageBpmStage = (milliseconds, isOverview, isTest) => {
  const matchBpmValues = [
    { $ne: [{ $type: "$message.values.systolic" }, "missing"] },
    { $ne: [{ $type: "$message.values.diastolic" }, "missing"] },
  ]

  return [
    {
      $lookup: {
        from: "bodytracemessages",
        let: {
          imei: "$bpIMEI",
        },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ["$message.imei", "$$imei"] },
                  { $ne: ["$message.isTest", true] },
                  ...matchBpmValues,
                  { $gte: ["$message.ts", milliseconds] },
                ],
              },
            },
          },
          { $sort: { createdAt: -1 } },
          ...(isOverview ? [{ $limit: 5 }] : []),
        ],
        as: "btMessagesBpm",
      },
    },
  ]
}
