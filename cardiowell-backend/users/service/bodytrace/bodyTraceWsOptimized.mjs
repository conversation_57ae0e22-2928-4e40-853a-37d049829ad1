// Optimized BodyTrace weight scale lookup - only runs when patient has weightIMEI device
export const bodyTraceWsOptimizedStage = (milliseconds, isOverview, isTest) => {
  return [
    {
      $lookup: {
        from: "BT_WS",
        let: {
          imei: "$weightIMEI",
        },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ["$imei", "$$imei"] },
                  { $ne: ["$isTest", true] },
                  { $gte: ["$ts", milliseconds] },
                ],
              },
            },
          },
          { $sort: { _created_at: -1 } },
          ...(isOverview ? [{ $limit: 5 }] : []),
        ],
        as: "ws",
      },
    },
  ]
}

// Conditional lookup with device check
export const conditionalBodyTraceWsStage = (milliseconds, isOverview, isTest) => {
  return [
    {
      $lookup: {
        from: "BT_WS",
        let: {
          imei: "$weightIMEI",
        },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ["$imei", "$$imei"] },
                  { $ne: ["$isTest", true] },
                  { $gte: ["$ts", milliseconds] },
                ],
              },
            },
          },
          { $sort: { _created_at: -1 } },
          ...(isOverview ? [{ $limit: 5 }] : []),
        ],
        as: "ws",
      },
    },
  ]
}
