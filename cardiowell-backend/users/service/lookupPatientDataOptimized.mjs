import moment from "moment"
import { optimizedProjectionStage } from "./optimizedProjection.mjs"
import { conditionalLookupStages } from "./optimizedLookupStages.mjs"
import { lookupPatientProfileTimes } from "../../profileTime/service/lookupPatientProfileTimes.mjs"

// Optimized patient data lookup with conditional device queries
export const lookupPatientDataOptimized = (providerId, isTest = false) => {
  const time = moment().subtract(30, "days").toDate()
  const milliseconds = time.getTime()

  return [
    // Initial patient match
    {
      $match: {
        clinic: { $exists: true, $ne: null },
      },
    },

    // Conditional device lookups - only run for patients with those devices
    ...conditionalLookupStages(milliseconds, false, isTest),

    // Threshold lookup
    {
      $lookup: {
        from: "testthresholds",
        let: {
          searchId: {
            $convert: {
              input: "$thresholdId",
              to: "objectId",
            },
          },
        },
        pipeline: [
          {
            $match: {
              $expr: {
                $eq: ["$_id", "$$searchId"],
              },
            },
          },
          { $limit: 1 },
        ],
        as: "threshold",
      },
    },

    // Profile times lookup
    {
      $lookup: {
        from: "patientprofiletimes",
        let: {
          patientId: {
            $convert: {
              input: "$_id",
              to: "string",
              format: "string",
            },
          },
          providerId: providerId,
        },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ["$$patientId", "$patientId"] },
                  { $eq: ["$$providerId", "$providerId"] },
                  { $gte: ["$ts", milliseconds] },
                ],
              },
            },
          },
          {
            $group: {
              _id: "$patientId",
              totalTime: { $sum: "$time" },
            },
          },
        ],
        as: "rt",
      },
    },

    // Unwind arrays
    {
      $unwind: {
        path: "$threshold",
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $unwind: {
        path: "$rt",
        preserveNullAndEmptyArrays: true,
      },
    },

    // Optimized projection - only fields used by frontend
    optimizedProjectionStage,
  ]
}
