import moment from "moment"
import { minimalProjectionStage } from "./optimizedProjection.mjs"
import { conditionalLookupStages } from "./optimizedLookupStages.mjs"

// Optimized patient overview lookup with conditional device queries
export const lookupPatientOverviewOptimized = (providerId, isTest = false) => {
  const time = moment().subtract(30, "days").toDate()
  const milliseconds = time.getTime()

  return [
    // Initial patient match
    {
      $match: {
        clinic: { $exists: true, $ne: null },
      },
    },

    // Conditional device lookups - only run for patients with those devices
    ...conditionalLookupStages(milliseconds, true, isTest),

    // Optimized projection - only essential fields for overview
    minimalProjectionStage,
  ]
}
