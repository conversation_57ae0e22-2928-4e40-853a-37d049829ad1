// Optimized Transtek glucose lookup - only runs when patient has glucoseIMEI device
export const transtekGlucoseOptimizedStage = (milliseconds, isOverview, isTest) => {
  return [
    {
      $lookup: {
        from: "CellularBloodGlucoseData",
        let: {
          imei: "$glucoseIMEI",
        },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ["$imei", "$$imei"] },
                  { $ne: ["$isTest", true] },
                  { $gte: ["$ts", milliseconds] },
                ],
              },
            },
          },
          { $sort: { ts: -1 } },
          ...(isOverview ? [{ $limit: 5 }] : []),
        ],
        as: "glucose",
      },
    },
  ]
}

// Conditional lookup with device check
export const conditionalTranstekGlucoseStage = (milliseconds, isOverview, isTest) => {
  return [
    {
      $lookup: {
        from: "CellularBloodGlucoseData",
        let: {
          imei: "$glucoseIMEI",
        },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ["$imei", "$$imei"] },
                  { $ne: ["$isTest", true] },
                  { $gte: ["$ts", milliseconds] },
                ],
              },
            },
          },
          { $sort: { ts: -1 } },
          ...(isOverview ? [{ $limit: 5 }] : []),
        ],
        as: "glucose",
      },
    },
  ]
}
