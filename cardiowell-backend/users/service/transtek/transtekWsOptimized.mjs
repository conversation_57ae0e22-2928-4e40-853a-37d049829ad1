// Optimized Transtek weight scale lookup - only runs when patient has ttWeightIMEI device
export const transtekWsOptimizedStage = (milliseconds, isOverview, isTest) => {
  return [
    {
      $lookup: {
        from: "Transtek_WS",
        let: {
          imei: "$ttWeightIMEI",
        },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ["$imei", "$$imei"] },
                  { $ne: ["$isTest", true] },
                  { $gte: ["$ts", milliseconds] },
                ],
              },
            },
          },
          { $sort: { ts: -1 } },
          ...(isOverview ? [{ $limit: 5 }] : []),
        ],
        as: "ttWs",
      },
    },
  ]
}

// Conditional lookup with device check
export const conditionalTranstekWsStage = (milliseconds, isOverview, isTest) => {
  return [
    {
      $lookup: {
        from: "Transtek_WS",
        let: {
          imei: "$ttWeightIMEI",
        },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ["$imei", "$$imei"] },
                  { $ne: ["$isTest", true] },
                  { $gte: ["$ts", milliseconds] },
                ],
              },
            },
          },
          { $sort: { ts: -1 } },
          ...(isOverview ? [{ $limit: 5 }] : []),
        ],
        as: "ttWs",
      },
    },
  ]
}
