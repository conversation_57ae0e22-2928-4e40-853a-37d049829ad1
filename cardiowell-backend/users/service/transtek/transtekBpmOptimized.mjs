// Optimized Transtek BPM lookup - only runs when patient has ttBpIMEI device
export const transtekBpmOptimizedStage = (milliseconds, isOverview, isTest) => {
  return [
    {
      $lookup: {
        from: "Transtek_BPM",
        let: {
          imei: "$ttBpIMEI",
        },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ["$imei", "$$imei"] },
                  { $ne: ["$isTest", true] },
                  { $gte: ["$ts", milliseconds] },
                ],
              },
            },
          },
          { $sort: { ts: -1 } },
          ...(isOverview ? [{ $limit: 5 }] : []),
        ],
        as: "ttBpm",
      },
    },
  ]
}

// Conditional lookup with device check
export const conditionalTranstekBpmStage = (milliseconds, isOverview, isTest) => {
  return [
    {
      $lookup: {
        from: "Transtek_BPM",
        let: {
          imei: "$ttBpIMEI",
        },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ["$imei", "$$imei"] },
                  { $ne: ["$isTest", true] },
                  { $gte: ["$ts", milliseconds] },
                ],
              },
            },
          },
          { $sort: { ts: -1 } },
          ...(isOverview ? [{ $limit: 5 }] : []),
        ],
        as: "ttBpm",
      },
    },
  ]
}
