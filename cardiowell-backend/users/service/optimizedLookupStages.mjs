import { bodyTraceBpmOptimizedStage } from "./bodytrace/bodyTraceBpmOptimized.mjs"
import { bodyTraceMessageBpmOptimizedStage } from "./bodytrace/bodyTraceMessageBpmOptimized.mjs"
import { bodyTraceWsOptimizedStage } from "./bodytrace/bodyTraceWsOptimized.mjs"
import { transtekBpmOptimizedStage } from "./transtek/transtekBpmOptimized.mjs"
import { transtekWsOptimizedStage } from "./transtek/transtekWsOptimized.mjs"
import { pulseOximeterOptimizedStage } from "./berry/pulseOximeterOptimized.mjs"
import { transtekGlucoseOptimizedStage } from "./transtek/transtekGlucoseOptimized.mjs"

// Optimized lookup stages that only run when patients have the corresponding devices
export const optimizedLookupStages = (milliseconds, isOverview, isTest) => {
  return [
    // Blood Pressure - BodyTrace (only if bpIMEI exists)
    ...bodyTraceBpmOptimizedStage(milliseconds, isOverview, isTest),
    ...bodyTraceMessageBpmOptimizedStage(milliseconds, isOverview, isTest),

    // Blood Pressure - Transtek (only if ttBpIMEI exists)
    ...transtekBpmOptimizedStage(milliseconds, isOverview, isTest),

    // Weight Scale - BodyTrace (only if weightIMEI exists)
    ...bodyTraceWsOptimizedStage(milliseconds, isOverview, isTest),

    // Weight Scale - Transtek (only if ttWeightIMEI exists)
    ...transtekWsOptimizedStage(milliseconds, isOverview, isTest),

    // Pulse Oximeter (only if pulseIMEI exists)
    ...pulseOximeterOptimizedStage(milliseconds, isOverview, isTest),

    // Glucose Monitor (only if glucoseIMEI exists)
    ...transtekGlucoseOptimizedStage(milliseconds, isOverview, isTest),
  ]
}

// Conditional lookup stages with device existence checks
export const conditionalLookupStages = (milliseconds, isOverview, isTest) => {
  return [
    // Blood Pressure - BodyTrace (conditional)
    {
      $lookup: {
        from: "BT_BPM",
        let: {
          imei: "$bpIMEI",
          hasDevice: { $ne: ["$bpIMEI", null] },
        },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ["$imei", "$$imei"] },
                  { $ne: ["$isTest", true] },
                  { $gte: ["$ts", milliseconds] },
                ],
              },
            },
          },
          { $sort: { _created_at: -1 } },
          ...(isOverview ? [{ $limit: 5 }] : []),
        ],
        as: "bpm",
      },
    },

    // Blood Pressure - BodyTrace Messages (conditional)
    {
      $lookup: {
        from: "bodytracemessages",
        let: {
          imei: "$bpIMEI",
          hasDevice: { $ne: ["$bpIMEI", null] },
        },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ["$message.imei", "$$imei"] },
                  { $ne: ["$message.isTest", true] },
                  { $ne: [{ $type: "$message.values.systolic" }, "missing"] },
                  { $ne: [{ $type: "$message.values.diastolic" }, "missing"] },
                  { $gte: ["$message.ts", milliseconds] },
                ],
              },
            },
          },
          { $sort: { createdAt: -1 } },
          ...(isOverview ? [{ $limit: 5 }] : []),
        ],
        as: "btMessagesBpm",
      },
    },

    // Blood Pressure - Transtek (conditional)
    {
      $lookup: {
        from: "Transtek_BPM",
        let: {
          imei: "$ttBpIMEI",
          hasDevice: { $ne: ["$ttBpIMEI", null] },
        },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ["$imei", "$$imei"] },
                  { $ne: ["$isTest", true] },
                  { $gte: ["$ts", milliseconds] },
                ],
              },
            },
          },
          { $sort: { ts: -1 } },
          ...(isOverview ? [{ $limit: 5 }] : []),
        ],
        as: "ttBpm",
      },
    },

    // Weight Scale - BodyTrace (conditional)
    {
      $lookup: {
        from: "BT_WS",
        let: {
          imei: "$weightIMEI",
          hasDevice: { $ne: ["$weightIMEI", null] },
        },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ["$imei", "$$imei"] },
                  { $ne: ["$isTest", true] },
                  { $gte: ["$ts", milliseconds] },
                ],
              },
            },
          },
          { $sort: { _created_at: -1 } },
          ...(isOverview ? [{ $limit: 5 }] : []),
        ],
        as: "ws",
      },
    },

    // Weight Scale - BodyTrace Messages (conditional)
    {
      $lookup: {
        from: "bodytracemessages",
        let: {
          imei: "$weightIMEI",
          hasDevice: { $ne: ["$weightIMEI", null] },
        },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ["$message.imei", "$$imei"] },
                  { $ne: [{ $type: "$message.values.weight" }, "missing"] },
                  { $ne: ["$isTest", true] },
                  { $gte: ["$message.ts", milliseconds] },
                ],
              },
            },
          },
          { $sort: { createdAt: -1 } },
          ...(isOverview ? [{ $limit: 5 }] : []),
        ],
        as: "btMessagesWs",
      },
    },

    // Weight Scale - Transtek (conditional)
    {
      $lookup: {
        from: "Transtek_WS",
        let: {
          imei: "$ttWeightIMEI",
          hasDevice: { $ne: ["$ttWeightIMEI", null] },
        },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ["$imei", "$$imei"] },
                  { $ne: ["$isTest", true] },
                  { $gte: ["$ts", milliseconds] },
                ],
              },
            },
          },
          { $sort: { ts: -1 } },
          ...(isOverview ? [{ $limit: 5 }] : []),
        ],
        as: "ttWs",
      },
    },

    // Pulse Oximeter (conditional)
    {
      $lookup: {
        from: "PulseOximeterData",
        let: {
          imei: "$pulseIMEI",
          hasDevice: { $ne: ["$pulseIMEI", null] },
        },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ["$imei", "$$imei"] },
                  { $ne: ["$isTest", true] },
                  { $gte: ["$time", milliseconds] },
                ],
              },
            },
          },
          { $sort: { time: -1 } },
          ...(isOverview ? [{ $limit: 5 }] : []),
        ],
        as: "pulse",
      },
    },

    // Glucose Monitor (conditional)
    {
      $lookup: {
        from: "CellularBloodGlucoseData",
        let: {
          imei: "$glucoseIMEI",
          hasDevice: { $ne: ["$glucoseIMEI", null] },
        },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ["$imei", "$$imei"] },
                  { $ne: ["$isTest", true] },
                  { $gte: ["$ts", milliseconds] },
                ],
              },
            },
          },
          { $sort: { ts: -1 } },
          ...(isOverview ? [{ $limit: 5 }] : []),
        ],
        as: "glucose",
      },
    },
  ]
}
