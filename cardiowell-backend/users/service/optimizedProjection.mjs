// Optimized projection stage - only includes fields used by the frontend dashboard
export const optimizedProjectionStage = {
  $project: {
    _id: 0,
    id: "$_id",
    name: { $concat: ["$firstName", " ", "$lastName"] },

    // Core patient information (used in dashboard)
    clinic: 1,
    cellNumber: 1,
    email: 1,
    mrn: "$MRN",
    timeZone: "$timeZone",
    timeZone2: "$timeZone",
    deviceNotificationsEnabled: 1,

    // Device IMEIs (only used ones)
    bpIMEI: 1,
    ttBpIMEI: 1,
    weightIMEI: 1,
    pulseIMEI: 1,
    glucoseIMEI: 1,

    // Device preferences (used for device selection logic)
    selectedBpDevice: 1,
    selectedWeightDevice: 1,

    // Patient demographics (used in dashboard)
    city: 1,
    state: 1,
    address: 1,
    zip: 1,
    height: 1,
    weight: 1,
    targetWeight: 1,
    birthdate: 1,
    gender: 1,
    ethnicity: 1,

    // Medical information (used in dashboard)
    allergies: 1,
    chronicConditions: 1,
    hypertensionMedications: 1,

    // Program and registration (used in dashboard)
    programId: 1,
    registrationCompleted: 1,
    lastUpdated: 1,
    showTestData: 1,

    // Device measurements (used in dashboard)
    bpm: 1,
    btMessagesBpm: 1,
    ttBpm: 1,
    ws: 1,
    btMessagesWs: 1,
    ttWs: 1,
    pulse: 1,
    glucose: 1,

    // Thresholds (used in dashboard)
    threshold: 1,

    // Profile times (used in dashboard)
    rt: {
      $ifNull: ["$rt.totalTime", 0],
    },
  },
}

// Minimal projection for overview - only essential fields
export const minimalProjectionStage = {
  $project: {
    _id: 0,
    id: "$_id",
    name: { $concat: ["$firstName", " ", "$lastName"] },
    clinic: 1,
    cellNumber: 1,
    email: 1,
    mrn: "$MRN",
    timeZone: "$timeZone",
    deviceNotificationsEnabled: 1,

    // Only essential device fields
    bpIMEI: 1,
    weightIMEI: 1,
    pulseIMEI: 1,
    glucoseIMEI: 1,

    // Only essential measurements
    bpm: 1,
    ws: 1,
    pulse: 1,
    glucose: 1,

    // Essential patient info
    height: 1,
    weight: 1,
    programId: 1,
    registrationCompleted: 1,
    lastUpdated: 1,
  },
}
