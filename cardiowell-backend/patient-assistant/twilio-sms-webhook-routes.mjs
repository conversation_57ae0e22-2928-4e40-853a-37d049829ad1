import { Router } from "express"
import twilio from "twilio"
import { phoneContact, textFromPatient } from "../models/message.mjs"
import Patient from "../models/patient.js"
import { processNewMessage } from "./assistant.mjs"
import queryString from "query-string"
import { Sentry } from "../observability/sentry.mjs"
import { textPatientWithoutSaving } from "../deviceUpdates/service/sendText.mjs"
import { isBPBuddyEnabled } from "./feature-toggle.mjs"

export const twilioSmsWebhookRouter = Router()

const validateTwilioRequest = (req, res, next) => {
  const twilioSignature = req.headers["x-twilio-signature"]
  const url = `https://${req.get("host")}${req.originalUrl}`

  if (!req.rawBody) {
    console.error("No raw body found for Twilio validation.")
    return res.status(400).send("Bad Request: Missing raw body.")
  }

  // Parse the raw body into a key-value object
  const params = queryString.parse(req.rawBody)

  const isValid = twilio.validateRequest(
    process.env.twilioToken,
    twilioSignature,
    url,
    params,
  )

  if (isValid) {
    next()
  } else {
    res.status(403).send("Invalid request. Access denied.")
  }
}

const processTwilioMessage = async req => {
  return Sentry.startSpan({ op: "task", name: "Process Twilio Message" }, async () => {
    try {
      const bpBuddyEnabled = process.env.FEATURE_BP_BUDDY_ENABLED === "true"
      if (!bpBuddyEnabled) {
        return
      }

      const patientSender = await Sentry.startSpan(
        { op: "db.mongo.query", name: "DB Query: Find Patient (Sender)" },
        async () => {
          return Patient.findOne({ cellNumber: req.body.From })
        },
      )

      if (!patientSender) {
        console.log(`Unknown sender: ${req.body.From}`)
        return
      }

      const message = textFromPatient(
        patientSender._id,
        phoneContact(req.body.From),
        phoneContact(req.body.To),
        req.body.Body,
      )
      await Sentry.startSpan(
        { op: "db.mongo.save", name: "DB Query: Save Incoming Message" },
        async () => {
          return message.save()
        },
      )

      if (!(await isBPBuddyEnabled(patientSender))) {
        console.log(
          `BP Buddy not enabled for patient: ${patientSender._id} that sent an SMS`,
        )
        return
      }

      const response = await Sentry.startSpan(
        {
          op: "assistant.process_message",
          name: "Assistant: Process Incoming Message",
        },
        async () => {
          return processNewMessage(patientSender, message)
        },
      )

      await Sentry.startSpan(
        { op: "twilio.send_message", name: "Twilio: Sending response" },
        async () => {
          return textPatientWithoutSaving(req.body.From, response.messageText)
        },
      )

      console.log(`Response to ${req.body.From}: ${response.messageText}`)
      return
    } catch (error) {
      console.error("Error processing Twilio message:", error)
      return
    }
  })
}

twilioSmsWebhookRouter.route("/sms").post(validateTwilioRequest, async (req, res) => {
  try {
    setImmediate(async () => {
      Sentry.startNewTrace(async () => {
        await processTwilioMessage(req)
      })
    })
    const twiml = new twilio.twiml.MessagingResponse()
    return res.type("text/xml").send(twiml.toString())
  } catch (error) {
    console.error("Error handling SMS webhook:", error)
    return res.status(500).send("Internal Server Error")
  }
})
