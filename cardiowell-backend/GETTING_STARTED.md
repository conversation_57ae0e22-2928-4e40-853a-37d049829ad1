# Cardiowell Backend Getting Started

The Cardiowell Backend contains the server application code for Cardiowell's applications. It handles requests from the front-end as well as API calls from external services. It also makes external API calls to third party services and is connected to [MongoDB](https://cloud.mongodb.com/v2/5e4ded66af4352567f365fa6).

## Installation

**Requirements**
* Node Version > v20.13.0
* Yarn > v1.22.22

**Installation**
1. Clone the repo
	```sh
	<NAME_EMAIL>:Cardiowell/cardiowell-backend.git
	```
2. Install Node Packages with Yarn
	```sh
	yarn
	```
3. Create a `.env` file with the following
	```
	NODE_ENV="development"
	```
4. Navigate to the [Heroku dashboard](https://dashboard.heroku.com/apps/cardiowell-application) and go to the **Settings** tab
5. Click on **Reveal Config Vars** and copy all variables into the `.env` file. For example:
	```
	mongoPass="copy_mongodb_password_here"
	sendgridAPI="copy_sendgrid_api_here"
	twilioToken="copy_twilio_token_here"
	```

## Deployment

1. Push the changes into the `master` branch of the server code
2. Navigate to the **cardiowell-application** project in Heroku: <br />[https://dashboard.heroku.com/apps/cardiowell-application](https://dashboard.heroku.com/apps/cardiowell-application)
3. Go the the **Deploy** tab, and scroll down to the **Manual Deploy** option
4. Choose the **master** branch in the dropdown, then hit **Deploy Branch**
5. View project at https://careportal.cardiowell.io

## Available Scripts

In the project directory, you can run:  

### `yarn start`

Runs the app in the development mode

Open [http://localhost:8081](http://localhost:8081) to view the current build in the browser.

### `yarn test`
  
Launches the Vitest test runner

### `yarn auth:patient <idOrPhone>`

Creates a patient session in your default browser.

```sh
yarn auth:patient 64fba2bbfff797cc5a30133c
```

## Getting Started

## Run Mongo (optional helper)

Use docker compose in `cardiowell-backend` to start Mongo and mongo-express.

```
docker compose up -d mongo mongo-express
```

## Run Analytics (R) microservice locally

The analytics microservice lives in a sibling folder `../cardiowell-analytics`.

- Build and run via docker:

```
docker compose up --build analytics
```

- The service exposes `http://localhost:8000` with endpoints:
  - `GET /v1/health`
  - `POST /v1/bp/compute-durations`
  - `POST /v1/bp/recompute-batch`

## Configure backend to use analytics

Set env variables for the backend process:

```
ANALYTICS_ENABLED=true
ANALYTICS_BASE_URL=http://localhost:8000
# ANALYTICS_TOKEN=your-shared-secret (optional)
ANALYTICS_TIMEOUT_MS=15000
ANALYTICS_RETRY_MAX=2
```

When new BP readings arrive, the backend event system will call the analytics service and persist results to `bp_duration_metrics` in the `R_BP` database.
