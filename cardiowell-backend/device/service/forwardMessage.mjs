import axios from "axios"
import { timedApiCall } from "../../utils/apiTiming.mjs"

export const FORWARD_STATUS_SUCCESSFUL = "successful"
export const FORWARD_STATUS_FAILED = "failed"
export const FORWARD_STATUS_NOT_SENT = "notSent"

// TODO rename message to payload
export const forwardMessage = async ({ message, endpoint }) => {
  try {
    const needAuthentication = Boolean(endpoint.keyName && endpoint.keyValue)

    const reponse = await timedApiCall(
      {
        method: "post",
        url: endpoint.url,
        data: message,
        validateStatus: () => true,
        headers: needAuthentication
          ? {
              [endpoint.keyName]: endpoint.keyValue,
            }
          : undefined,
      },
      `Forward to ${endpoint.name || endpoint.url}`,
    )

    const endpointData = {
      name: endpoint.name,
      url: endpoint.url,
    }

    if (reponse.status >= 200 && reponse.status <= 300) {
      return {
        status: FORWARD_STATUS_SUCCESSFUL,
        data: reponse.data,
        endpoint: endpointData,
      }
    }
    return {
      status: FORWARD_STATUS_FAILED,
      data: reponse.data,
      endpoint: endpointData,
    }
  } catch (err) {
    return {
      status: FORWARD_STATUS_FAILED,
      data: err,
    }
  }
}
