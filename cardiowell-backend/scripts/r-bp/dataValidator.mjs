import dotenv from 'dotenv'
import { getRBPDB } from './mongoConnection.mjs'
import { processDuration } from './durationCalculator.mjs'
import { updateDurationMetrics } from './mongoConnection.mjs'

dotenv.config()

const REQUIRED_TOP_LEVEL_FIELDS = [
  'success',
  'analysis_version',
  'computed_at',
  'last_input_at',
  'total_readings',
  'basic_stats',
  'variability_stats',
  'range_stats',
  'pressure_stats',
  'time_of_day',
  'classification_counts',
]

export function validateRAnalysisResult(result, durationOrDayLabel = '') {
  const errors = []
  const warnings = []

  if (!result || typeof result !== 'object') {
    return { isValid: false, errors: ['Empty result'], warnings }
  }

  if (result.success !== true) errors.push('success must be true')

  for (const key of REQUIRED_TOP_LEVEL_FIELDS) {
    if (!(key in result)) errors.push(`missing field: ${key}`)
  }

  if (result.total_readings != null && result.total_readings < 0) warnings.push('total_readings negative')

  const versionExpected = process.env.CURRENT_ANALYSIS_VERSION
  if (versionExpected && result.analysis_version && result.analysis_version !== versionExpected) {
    warnings.push(`analysis_version mismatch: expected ${versionExpected}, got ${result.analysis_version}`)
  }

  return { isValid: errors.length === 0, errors, warnings }
}

export function getDataQualityScore(metrics) {
  const total = metrics.totalReadings || 0
  let score = 50
  if (total === 0) score = 0
  else if (total < 5) score = 40
  else if (total < 15) score = 60
  else if (total < 30) score = 75
  else score = 90
  const level = score >= 80 ? 'high' : score >= 60 ? 'medium' : score > 0 ? 'low' : 'missing'
  return { score, qualityLevel: level, isReliable: level !== 'missing' && level !== 'low' }
}

export function createFallbackMetrics(duration, reason, dateRange) {
  return {
    duration,
    totalReadings: 0,
    dateRange,
    basicStats: {},
    variabilityStats: {},
    rangeStats: {},
    pressureStats: {},
    circadianStats: {},
    timeOfDay: {},
    classificationCounts: {},
    alerts: [],
    combinedStats: {},
    lastReading: null,
    computedAt: new Date(),
    isFallback: true,
    error: reason,
  }
}

export function validateStoredMetrics(metrics, duration) {
  if (!metrics || typeof metrics !== 'object') return { isValid: false, errors: ['missing metrics'] }
  const errs = []
  if (!('totalReadings' in metrics)) errs.push('missing totalReadings')
  if (!('computedAt' in metrics)) errs.push('missing computedAt')
  return { isValid: errs.length === 0, errors: errs }
}

export async function getDataHealthReport(patientId) {
  try {
    const db = await getRBPDB()
    const coll = db.collection('bp_duration_metrics')
    const doc = await coll.findOne({ patientId })
    if (!doc) return { patientId, status: 'no_data', generatedAt: new Date() }

    const durationKeys = Object.keys(doc).filter(k => k.startsWith('metrics'))
    const missing = []
    let valid = 0
    let total = 0
    let mostRecent = null
    for (const key of durationKeys) {
      total += 1
      const m = doc[key]
      if (!m) { missing.push(key); continue }
      const val = validateStoredMetrics(m, key)
      if (val.isValid) valid += 1
      const ts = m.computedAt ? new Date(m.computedAt) : null
      if (ts && (!mostRecent || ts > mostRecent)) mostRecent = ts
    }
    const status = valid === total ? 'healthy' : valid >= Math.ceil(total / 2) ? 'degraded' : 'critical'
    return {
      patientId,
      status,
      durationsPresent: total - missing.length,
      durationsMissing: missing,
      lastUpdated: doc.lastUpdated || null,
      mostRecentComputedAt: mostRecent,
      generatedAt: new Date(),
    }
  } catch (err) {
    return { patientId, status: 'error', error: err.message, generatedAt: new Date() }
  }
}

function extractImeiFromPatientId(patientId) {
  if (typeof patientId !== 'string') return null
  if (patientId.startsWith('patient_')) return patientId.substring('patient_'.length)
  return patientId
}

export async function validateAndRepairMetrics(patientId, duration) {
  try {
    const db = await getRBPDB()
    const coll = db.collection('bp_duration_metrics')
    const doc = await coll.findOne({ patientId })
    const key = `metrics${duration}`
    const current = doc ? doc[key] : null
    const validation = validateStoredMetrics(current, duration)
    if (validation.isValid) {
      return { repaired: false, repairedMetrics: current, reason: 'already_valid' }
    }
    const imei = extractImeiFromPatientId(patientId)
    if (!imei) {
      return { repaired: false, repairedMetrics: null, reason: 'unable_to_extract_imei' }
    }
    const recomputed = await processDuration(imei, duration)
    await updateDurationMetrics(patientId, duration, recomputed)
    return { repaired: true, repairedMetrics: recomputed, reason: 'recomputed' }
  } catch (err) {
    return { repaired: false, repairedMetrics: null, reason: `repair_failed: ${err.message}` }
  }
}


