import { getCardiowellDB } from './mongoConnection.mjs'
import { createHash } from 'crypto'

/**
 * Fetch BP readings from ad_bpms collection for a specific IMEI
 * @param {string} imei - Device IMEI
 * @param {string} startDate - Start date in YYYY-MM-DD format (optional)
 * @param {string} endDate - End date in YYYY-MM-DD format (optional)
 * @returns {Array} Array of normalized BP readings
 */
export const fetchBPReadings = async (imei, startDate = null, endDate = null) => {
  try {
    const db = await getCardiowellDB()
    const collection = db.collection('ad_bpms')

    // Build query
    const query = { 'payload.imei': imei }

    // Add date range if provided (support both ISO/Date strings and epoch milliseconds)
    if (startDate && endDate) {
      const startIso = startDate + 'T00:00:00.000Z'
      const endIso = endDate + 'T23:59:59.999Z'
      const startMs = new Date(startIso).getTime()
      const endMs = new Date(endIso).getTime()

      query.$or = [
        { 'payload.timestamp': { $gte: startIso, $lte: endIso } },
        { 'payload.timestamp': { $gte: startMs, $lte: endMs } },
      ]
    }

    console.log(`Fetching BP readings for IMEI: ${imei}`)
    console.log(`Query:`, JSON.stringify(query, null, 2))

    // Fetch readings
    const readings = await collection.find(query).sort({ 'payload.timestamp': 1 }).toArray()

    console.log(`Found ${readings.length} readings`)

    if (readings.length === 0) {
      console.log('No readings found for the specified criteria')
      return []
    }

    // Normalize readings
    const normalizedReadings = readings
      .map((reading) => {
        const payload = reading.payload

        // Validate required fields
        if (!payload.sys || !payload.dia || !payload.timestamp) {
          console.warn(`Skipping invalid reading: ${JSON.stringify(payload)}`)
          return null
        }

        // Convert timestamp to UTC
        const readingTime = new Date(payload.timestamp)

        // Create normalized reading
        const normalized = {
          patientId: `patient_${imei}`,
          readingAtUTC: readingTime.toISOString(),
          sbp: parseInt(payload.sys),
          dbp: parseInt(payload.dia),
          pulse: payload.pulse ? parseInt(payload.pulse) : null,
          deviceType: 'ad',
          deviceId: imei,
          source: 'ad_bpms',
          sourceReadingId: reading._id.toString(),
          isTest: payload.isTest || false,
          unit: payload.unit || 0,
        }

        // Create content hash for deduplication
        const contentKey = `${normalized.patientId}:${Math.floor(
          readingTime.getTime() / 1000,
        )}:${normalized.sbp}:${normalized.dbp}:${normalized.pulse || ''}`
        normalized.contentHash = createHash('sha256').update(contentKey).digest('hex')

        // Create source hash if sourceReadingId exists
        if (normalized.sourceReadingId) {
          const sourceKey = `${normalized.source}:${normalized.sourceReadingId}`
          normalized.sourceHash = createHash('sha256').update(sourceKey).digest('hex')
        }

        return normalized
      })
      .filter((reading) => reading !== null)

    console.log(`Normalized ${normalizedReadings.length} readings`)

    if (normalizedReadings.length > 0) {
      console.log('Sample normalized reading:')
      console.log(JSON.stringify(normalizedReadings[0], null, 2))
    }

    return normalizedReadings
  } catch (error) {
    console.error('Error fetching BP readings:', error)
    throw error
  }
}

/**
 * Get date range for a specific day
 * @param {string} day - Date in YYYY-MM-DD format
 * @returns {Object} Object with start and end dates
 */
export const getDayRange = (day) => {
  const start = new Date(day + 'T00:00:00.000Z')
  const end = new Date(day + 'T23:59:59.999Z')
  return { start, end }
}

/**
 * Group readings by day
 * @param {Array} readings - Array of normalized readings
 * @returns {Object} Object with day as key and readings as value
 */
export const groupReadingsByDay = (readings) => {
  const grouped = {}
  readings.forEach((reading) => {
    const day = reading.readingAtUTC.split('T')[0]
    if (!grouped[day]) grouped[day] = []
    grouped[day].push(reading)
  })
  return grouped
}

/**
 * Get available days for a patient
 * @param {string} imei - Device IMEI
 * @returns {Array} Array of available days
 */
export const getAvailableDays = async (imei) => {
  try {
    const db = await getCardiowellDB()
    const collection = db.collection('ad_bpms')
    const readings = await collection.find({ 'payload.imei': imei }).toArray()
    const dayCounts = {}
    readings.forEach((reading) => {
      const timestamp = reading.payload.timestamp
      let day
      if (typeof timestamp === 'string') {
        day = timestamp.split('T')[0]
      } else if (timestamp instanceof Date) {
        day = timestamp.toISOString().split('T')[0]
      } else {
        const date = new Date(timestamp)
        day = date.toISOString().split('T')[0]
      }
      dayCounts[day] = (dayCounts[day] || 0) + 1
    })
    const result = Object.keys(dayCounts)
      .sort()
      .map((day) => ({ day, count: dayCounts[day] }))
    return result
  } catch (error) {
    console.error('Error getting available days:', error)
    throw error
  }
}


