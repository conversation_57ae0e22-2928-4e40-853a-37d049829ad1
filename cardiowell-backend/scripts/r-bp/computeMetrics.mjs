const clampNumber = value => (Number.isFinite(value) ? value : null)

const normalizeMmHg = n => {
  if (n == null) return null
  const num = Number(n)
  if (!Number.isFinite(num)) return null
  return num > 300 ? Math.round(num / 100) : Math.round(num)
}

export const classifyStage = (sys, dia) => {
  if (sys == null || dia == null) return null
  if (sys >= 180 || dia >= 120) return "crisis"
  if (sys >= 140 || dia >= 90) return "stage2"
  if (sys >= 130 || dia >= 80) return "stage1"
  if (sys >= 120 && dia < 80) return "elevated"
  return "normal"
}

const isOutlier = (sys, dia) => {
  if (sys == null || dia == null) return true
  if (dia < 40 || dia > 140) return true
  if (sys < 70 || sys > 260) return true
  if (dia >= sys) return true
  return false
}

// Derive timezone-specific components without external deps
const getZonedParts = (ts, tz) => {
  const date = typeof ts === "number" ? new Date(ts) : new Date(ts)
  const fmt = new Intl.DateTimeFormat("en-US", {
    timeZone: tz || "UTC",
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
    hour12: false,
  })
  const parts = fmt.formatToParts(date).reduce((acc, p) => {
    acc[p.type] = p.value
    return acc
  }, {})
  return {
    year: Number(parts.year),
    month: Number(parts.month),
    day: Number(parts.day),
    hour: Number(parts.hour),
  }
}

const isMorning = h => {
  return h >= 4 && h < 12
}

const isEvening = h => {
  return h >= 16 && h <= 23
}

export function computeMetricsFromReadings(readings, { tz = "UTC", rangeStart, rangeEnd } = {}) {
  const result = {
    totals: { totalReadings: 0, daysWithReadings: 0, firstReadingAt: null, lastReadingAt: null },
    aggregates: { avgSys: null, avgDia: null, avgPulse: null },
    extremes: { maxSys: null, maxSysAt: null, minSys: null, minSysAt: null, maxDia: null, maxDiaAt: null, minDia: null, minDiaAt: null },
    stagesPct: { normal: 0, elevated: 0, stage1: 0, stage2: 0, crisis: 0 },
    timeOfDay: { morning: { avgSys: null, avgDia: null }, evening: { avgSys: null, avgDia: null } },
    computedForRange: { start: rangeStart, end: rangeEnd },
    tzApplied: tz,
  }

  if (!Array.isArray(readings) || readings.length === 0) {
    return { dataAvailable: false, ...result }
  }

  let sumSys = 0
  let sumDia = 0
  let sumPulse = 0
  let count = 0

  let morningSumSys = 0
  let morningSumDia = 0
  let morningCount = 0
  let eveningSumSys = 0
  let eveningSumDia = 0
  let eveningCount = 0

  const daySet = new Set()
  const stageCounts = { normal: 0, elevated: 0, stage1: 0, stage2: 0, crisis: 0 }

  let maxSys = -Infinity, maxSysAt = null
  let minSys = Infinity, minSysAt = null
  let maxDia = -Infinity, maxDiaAt = null
  let minDia = Infinity, minDiaAt = null

  for (const r of readings) {
    const sys = normalizeMmHg(r.sbp)
    const dia = normalizeMmHg(r.dbp)
    const pulse = r.pulse != null ? Number(r.pulse) : null
    const parts = getZonedParts(r.readingAtUTC ?? r.timestamp ?? r.ts, tz)

    if (isOutlier(sys, dia)) {
      continue
    }

    const dayKey = `${parts.year}-${String(parts.month).padStart(2, "0")}-${String(parts.day).padStart(2, "0")}`
    daySet.add(dayKey)

    // totals/aggregates
    sumSys += sys
    sumDia += dia
    if (pulse != null && Number.isFinite(pulse)) sumPulse += pulse
    count += 1

    // extremes
    if (sys > maxSys) { maxSys = sys; maxSysAt = r.readingAtUTC ?? r.timestamp ?? r.ts }
    if (sys < minSys) { minSys = sys; minSysAt = r.readingAtUTC ?? r.timestamp ?? r.ts }
    if (dia > maxDia) { maxDia = dia; maxDiaAt = r.readingAtUTC ?? r.timestamp ?? r.ts }
    if (dia < minDia) { minDia = dia; minDiaAt = r.readingAtUTC ?? r.timestamp ?? r.ts }

    // stages
    const stage = classifyStage(sys, dia)
    if (stage) stageCounts[stage] += 1

    // time of day
    if (isMorning(parts.hour)) {
      morningSumSys += sys
      morningSumDia += dia
      morningCount += 1
    } else if (isEvening(parts.hour)) {
      eveningSumSys += sys
      eveningSumDia += dia
      eveningCount += 1
    }
  }

  if (count === 0) {
    return { dataAvailable: false, ...result }
  }

  result.totals.totalReadings = count
  result.totals.daysWithReadings = daySet.size
  const first = readings[0]
  const last = readings[readings.length - 1]
  result.totals.firstReadingAt = first?.readingAtUTC ?? first?.timestamp ?? first?.ts ?? null
  result.totals.lastReadingAt = last?.readingAtUTC ?? last?.timestamp ?? last?.ts ?? null

  result.aggregates.avgSys = Math.round(sumSys / count)
  result.aggregates.avgDia = Math.round(sumDia / count)
  result.aggregates.avgPulse = clampNumber(sumPulse ? Math.round(sumPulse / count) : null)

  result.extremes.maxSys = maxSys
  result.extremes.maxSysAt = maxSysAt
  result.extremes.minSys = minSys
  result.extremes.minSysAt = minSysAt
  result.extremes.maxDia = maxDia
  result.extremes.maxDiaAt = maxDiaAt
  result.extremes.minDia = minDia
  result.extremes.minDiaAt = minDiaAt

  const pct = k => Number(((stageCounts[k] / count) * 100).toFixed(1))
  result.stagesPct = {
    normal: pct("normal"),
    elevated: pct("elevated"),
    stage1: pct("stage1"),
    stage2: pct("stage2"),
    crisis: pct("crisis"),
  }

  result.timeOfDay.morning.avgSys = morningCount ? Math.round(morningSumSys / morningCount) : null
  result.timeOfDay.morning.avgDia = morningCount ? Math.round(morningSumDia / morningCount) : null
  result.timeOfDay.evening.avgSys = eveningCount ? Math.round(eveningSumSys / eveningCount) : null
  result.timeOfDay.evening.avgDia = eveningCount ? Math.round(eveningSumDia / eveningCount) : null

  return { dataAvailable: true, ...result }
}


