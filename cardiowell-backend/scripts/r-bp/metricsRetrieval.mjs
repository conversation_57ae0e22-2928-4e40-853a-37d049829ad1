import { getRBPDB } from './mongoConnection.mjs'
import { getAllDurationMetrics } from './mongoConnection.mjs'
import { processAllDurations } from './durationCalculator.mjs'

export async function getDurationMetrics(imei) {
  try {
    const patientId = `patient_${imei}`
    const db = await getRBPDB()
    const collection = db.collection('bp_duration_metrics')
    const doc = await collection.findOne({ patientId })
    if (!doc) return { success: false, error: 'No metrics found' }
    return { success: true, data: doc }
  } catch (err) {
    return { success: false, error: err.message }
  }
}

export async function getMetricsSummary(imei) {
  try {
    const { success, data, error } = await getDurationMetrics(imei)
    if (!success) return { success, error }
    const summary = {
      patientId: data.patientId,
      lastUpdated: data.lastUpdated,
      durations: Object.keys(data).filter(k => k.startsWith('metrics')).reduce((acc, key) => {
        const d = data[key]
        if (d && typeof d === 'object') acc[key] = { totalReadings: d.totalReadings || 0, qualityLevel: d.qualityLevel || 'unknown' }
        return acc
      }, {}),
    }
    return { success: true, data: summary }
  } catch (err) {
    return { success: false, error: err.message }
  }
}

export async function getDetailedMetrics(imei, duration) {
  try {
    const patientId = `patient_${imei}`
    const db = await getRBPDB()
    const collection = db.collection('bp_duration_metrics')
    const doc = await collection.findOne({ patientId })
    if (!doc) return { success: false, error: 'No metrics found' }
    const key = `metrics${duration}`
    const metrics = doc[key]
    if (!metrics) return { success: false, error: `No ${duration} metrics found` }
    return { success: true, data: metrics }
  } catch (err) {
    return { success: false, error: err.message }
  }
}

// Table of data (flatten series)
export function buildMetricsTable(metrics) {
  const series = Array.isArray(metrics?.series) ? metrics.series : []
  return series.map(r => ({
    time: r.time,
    sbp: typeof r.sbp === 'number' ? r.sbp : null,
    dbp: typeof r.dbp === 'number' ? r.dbp : null,
    map: typeof r.map === 'number' ? r.map : null,
    pulse: typeof r.pulse === 'number' ? r.pulse : null,
    classification: r.classification || null,
  }))
}

// Histogram bins for SBP and DBP
export function buildHistogram(metrics, opts = {}) {
  const series = Array.isArray(metrics?.series) ? metrics.series : []
  const valuesS = series.map(r => Number.isFinite(r.sbp) ? r.sbp : null).filter(v => v != null)
  const valuesD = series.map(r => Number.isFinite(r.dbp) ? r.dbp : null).filter(v => v != null)
  const binCount = Math.max(5, Math.min(30, parseInt(opts.binCount || 10, 10)))
  const makeBins = (values) => {
    if (!values.length) return []
    const minV = Math.min(...values)
    const maxV = Math.max(...values)
    const span = maxV - minV || 1
    const width = span / binCount
    const bins = new Array(binCount).fill(0)
    for (const v of values) {
      let idx = Math.floor((v - minV) / width)
      if (idx >= binCount) idx = binCount - 1
      if (idx < 0) idx = 0
      bins[idx] += 1
    }
    const edges = Array.from({ length: binCount + 1 }, (_, i) => +(minV + i * width).toFixed(1))
    return { bins, edges, min: minV, max: maxV, binWidth: +width.toFixed(2) }
  }
  return {
    sbp: makeBins(valuesS),
    dbp: makeBins(valuesD),
    total: series.length,
  }
}

// Scatter plot data points
export function buildScatter(metrics) {
  const series = Array.isArray(metrics?.series) ? metrics.series : []
  return series
    .filter(r => Number.isFinite(r.sbp) && Number.isFinite(r.dbp))
    .map(r => ({
      sbp: r.sbp,
      dbp: r.dbp,
      time: r.time,
      classification: r.classification || null,
    }))
}

export async function getAllPatientsWithMetrics() {
  try {
    const db = await getRBPDB()
    const collection = db.collection('bp_duration_metrics')
    const docs = await collection.find({}, { projection: { patientId: 1, lastUpdated: 1 } }).toArray()
    return { success: true, data: docs }
  } catch (err) {
    return { success: false, error: err.message }
  }
}

export async function refreshAllDurations(imei) {
  const result = await processAllDurations(imei)
  return { success: true, data: result }
}

export default { getDurationMetrics, getMetricsSummary, getDetailedMetrics, getAllPatientsWithMetrics }


