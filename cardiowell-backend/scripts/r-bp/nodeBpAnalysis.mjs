import { createHash } from 'crypto';

// Utility helpers
const toNumber = (v) => {
  const n = Number(v);
  return Number.isFinite(n) ? n : null;
};

const mean = (arr) => arr.length ? arr.reduce((a, b) => a + b, 0) / arr.length : null;
const stddev = (arr) => {
  if (arr.length < 2) return null;
  const m = mean(arr);
  const variance = arr.reduce((acc, v) => acc + Math.pow(v - m, 2), 0) / (arr.length - 1);
  return Math.sqrt(variance);
};

const minVal = (arr) => arr.length ? Math.min(...arr) : null;
const maxVal = (arr) => arr.length ? Math.max(...arr) : null;

const classifyBp = (sbp, dbp) => {
  if (sbp == null || dbp == null) return 'unknown';
  if (sbp < 120 && dbp < 80) return 'normal';
  if ((sbp >= 120 && sbp < 130) && dbp < 80) return 'elevated';
  if ((sbp >= 130 && sbp < 140) || (dbp >= 80 && dbp < 90)) return 'stage1_hypertension';
  if (sbp >= 140 || dbp >= 90) return 'stage2_hypertension';
  return 'unknown';
};

const mapFromSbpDbp = (sbp, dbp) => {
  if (sbp == null || dbp == null) return null;
  return Math.round((dbp + (sbp - dbp) / 3) * 10) / 10;
};

const byTimeOfDay = (readings) => {
  const buckets = { morning: [], afternoon: [], evening: [], night: [] };
  readings.forEach(r => {
    const t = new Date(r.reading_time);
    const hour = t.getUTCHours();
    if (hour >= 6 && hour < 12) buckets.morning.push(r);
    else if (hour >= 12 && hour < 18) buckets.afternoon.push(r);
    else if (hour >= 18 && hour < 22) buckets.evening.push(r);
    else buckets.night.push(r);
  });
  const statsFor = (arr, key) => {
    const sbps = arr.map(x => toNumber(x.sbp)).filter(x => x != null);
    const dbps = arr.map(x => toNumber(x.dbp)).filter(x => x != null);
    return {
      count: arr.length,
      mean_sbp: mean(sbps),
      mean_dbp: mean(dbps)
    };
  };
  return {
    morning: statsFor(buckets.morning),
    afternoon: statsFor(buckets.afternoon),
    evening: statsFor(buckets.evening),
    night: statsFor(buckets.night)
  };
};

export const analyzeReadings = (inputData) => {
  const nowIso = new Date().toISOString();
  const readings = (inputData.readings || [])
    .map(r => ({
      sbp: toNumber(r.sbp),
      dbp: toNumber(r.dbp),
      pulse: toNumber(r.pulse),
      reading_time: r.reading_time
    }))
    .filter(r => r.sbp != null && r.dbp != null && r.reading_time);

  const sbps = readings.map(r => r.sbp);
  const dbps = readings.map(r => r.dbp);
  const pulses = readings.map(r => r.pulse).filter(v => v != null);
  const maps = readings.map(r => mapFromSbpDbp(r.sbp, r.dbp)).filter(v => v != null);

  const latest = readings.length
    ? readings.slice().sort((a, b) => new Date(b.reading_time) - new Date(a.reading_time))[0]
    : null;

  const classes = readings.map(r => classifyBp(r.sbp, r.dbp));
  const classification_counts = classes.reduce((acc, c) => {
    acc[c] = (acc[c] || 0) + 1;
    return acc;
  }, { normal: 0, elevated: 0, stage1_hypertension: 0, stage2_hypertension: 0, unknown: 0 });

  const series = readings.map(r => ({
    time: r.reading_time,
    sbp: r.sbp,
    dbp: r.dbp,
    map: mapFromSbpDbp(r.sbp, r.dbp),
    pulse: r.pulse || null,
    classification: classifyBp(r.sbp, r.dbp)
  }));

  const result = {
    success: true,
    analysis_version: inputData.analysis_version || 'v1.0.0-node',
    computed_at: nowIso,
    last_input_at: latest ? new Date(latest.reading_time).toISOString() : nowIso,
    patient_id: inputData.patient_id,
    day: inputData.day || null,
    duration: inputData.duration || null,
    total_readings: readings.length,
    basic_stats: {
      mean_sbp: mean(sbps),
      mean_dbp: mean(dbps),
      mean_pulse: mean(pulses),
      map: mean(maps)
    },
    variability_stats: {
      sbp_sd: stddev(sbps),
      dbp_sd: stddev(dbps),
      pulse_sd: stddev(pulses)
    },
    range_stats: {
      sbp_min: minVal(sbps),
      sbp_max: maxVal(sbps),
      sbp_range: (minVal(sbps) != null && maxVal(sbps) != null) ? maxVal(sbps) - minVal(sbps) : null,
      dbp_min: minVal(dbps),
      dbp_max: maxVal(dbps),
      dbp_range: (minVal(dbps) != null && maxVal(dbps) != null) ? maxVal(dbps) - minVal(dbps) : null
    },
    pressure_stats: {
      pulse_pressure: (mean(sbps) != null && mean(dbps) != null) ? Math.round((mean(sbps) - mean(dbps)) * 10) / 10 : null
    },
    circadian_stats: {},
    time_of_day: byTimeOfDay(readings),
    classification_counts,
    alerts: [],
    combined_stats: {},
    latest_reading: latest ? {
      time: latest.reading_time,
      sbp: latest.sbp,
      dbp: latest.dbp,
      map: mapFromSbpDbp(latest.sbp, latest.dbp),
      pulse: latest.pulse || null
    } : null,
    series
  };

  return result;
};

export default analyzeReadings;



