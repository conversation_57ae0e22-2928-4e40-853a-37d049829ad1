import { createHash } from 'crypto';
import { getRBPDB, createRBPIndexes } from './mongoConnection.mjs';
import { fetchBPReadings, groupReadingsByDay, getAvailableDays } from './dataFetcher.mjs';
import * as rAnalytics from '../../utils/rAnalyticsClient.mjs';
import dotenv from 'dotenv';

dotenv.config();

const FEATURE_R_ANALYTICS = String(process.env.FEATURE_R_ANALYTICS || 'false') === 'true';

// R-only analysis. If R is unavailable or returns error, throw.
const callBPAnalysis = async (inputData) => {
  if (!FEATURE_R_ANALYTICS) {
    throw new Error('R analytics feature disabled')
  }
  const rResult = await rAnalytics.computeDay(inputData);
  if (!rResult || rResult.success !== true) {
    throw new Error('R analytics failed or returned invalid response')
  }
  return rResult;
};

/**
 * Save raw readings to R_BP database
 * @param {Array} readings - Array of normalized readings
 */
const saveRawReadings = async (readings) => {
  const db = await getRBPDB();
  const collection = db.collection('bp_readings');
  
  console.log(`Saving ${readings.length} raw readings to R_BP database`);
  
  for (const reading of readings) {
    try {
      // Use upsert to avoid duplicates
      await collection.updateOne(
        { contentHash: reading.contentHash },
        { $set: reading },
        { upsert: true }
      );
    } catch (error) {
      console.error(`Error saving reading with hash ${reading.contentHash}:`, error);
    }
  }
  
  console.log('Raw readings saved successfully');
};

/**
 * Save processed analysis results to R_BP database
 * @param {Object} analysisResult - Results from R analysis
 * @param {string} patientId - Patient ID
 * @param {string} day - Day in YYYY-MM-DD format
 */
const saveProcessedResults = async (analysisResult, patientId, day) => {
  const db = await getRBPDB();
  const collection = db.collection('bp_processed');
  
  const processedDoc = {
    patientId,
    day,
    analysisVersion: analysisResult.analysis_version,
    computedAt: new Date(analysisResult.computed_at),
    lastInputAt: new Date(analysisResult.last_input_at),
    totalReadings: analysisResult.total_readings,
    // Store all the enhanced metrics
    basicStats: analysisResult.basic_stats,
    variabilityStats: analysisResult.variability_stats,
    rangeStats: analysisResult.range_stats,
    pressureStats: analysisResult.pressure_stats,
    circadianStats: analysisResult.circadian_stats,
    timeOfDay: analysisResult.time_of_day,
    classificationCounts: analysisResult.classification_counts,
    alerts: analysisResult.alerts,
    combinedStats: analysisResult.combined_stats,
    latestReading: analysisResult.latest_reading,
    series: analysisResult.series,
    createdAt: new Date(),
    updatedAt: new Date()
  };
  
  console.log(`Saving processed results for patient ${patientId}, day ${day}`);
  
  // Upsert by patientId and day
  await collection.updateOne(
    { patientId, day },
    { $set: processedDoc },
    { upsert: true }
  );
  
  console.log('Processed results saved successfully');
};

/**
 * Update BP summary for patient
 * @param {Object} analysisResult - Results from R analysis
 * @param {string} patientId - Patient ID
 */
const updateBPSummary = async (analysisResult, patientId) => {
  const db = await getRBPDB();
  const collection = db.collection('bp_summary');
  
  const summary = {
    patientId,
    lastUpdated: new Date(),
    totalReadings: analysisResult.total_readings,
    latestReading: analysisResult.latest_reading,
    // Store enhanced metrics in summary
    basicStats: analysisResult.basic_stats,
    variabilityStats: analysisResult.variability_stats,
    rangeStats: analysisResult.range_stats,
    pressureStats: analysisResult.pressure_stats,
    circadianStats: analysisResult.circadian_stats,
    timeOfDay: analysisResult.time_of_day,
    classificationCounts: analysisResult.classification_counts,
    alerts: analysisResult.alerts,
    combinedStats: analysisResult.combined_stats,
    dataFreshness: analysisResult.last_input_at
  };
  
  console.log(`Updating BP summary for patient ${patientId}`);
  
  // Upsert by patientId
  await collection.updateOne(
    { patientId },
    { $set: summary },
    { upsert: true }
  );
  
  console.log('BP summary updated successfully');
};

/**
 * Process BP data for a specific day
 * @param {string} imei - Device IMEI
 * @param {string} day - Day in YYYY-MM-DD format
 */
export const processBPDay = async (imei, day) => {
  try {
    console.log(`\n=== Processing BP data for IMEI: ${imei}, Day: ${day} ===`);
    
    // Fetch readings for the day
    const readings = await fetchBPReadings(imei, day, day);
    
    if (readings.length === 0) {
      console.log(`No readings found for ${imei} on ${day}`);
      return;
    }
    
    // Save raw readings
    await saveRawReadings(readings);
    
    // Prepare data for analysis
    const patientId = `patient_${imei}`;
    const inputData = {
      patient_id: patientId,
      day: day,
      analysis_version: process.env.CURRENT_ANALYSIS_VERSION || 'v1.0.0-node',
      readings: readings.map(reading => ({
        sbp: reading.sbp,
        dbp: reading.dbp,
        pulse: reading.pulse,
        reading_time: reading.readingAtUTC
      }))
    };
    
    const analysisResult = await callBPAnalysis(inputData);
    if (!analysisResult.success) throw new Error(`Node analysis failed`);
    
    // Save processed results
    await saveProcessedResults(analysisResult, patientId, day);
    
    // Update summary
    await updateBPSummary(analysisResult, patientId);
    
    console.log(`✅ Successfully processed BP data for ${imei} on ${day}`);
    console.log(`📊 Basic Stats: SBP=${analysisResult.basic_stats.mean_sbp}, DBP=${analysisResult.basic_stats.mean_dbp}, MAP=${analysisResult.basic_stats.map}`);
    console.log(`📈 Classifications: Normal=${analysisResult.classification_counts.normal}, Stage1=${analysisResult.classification_counts.stage1_hypertension}`);
    console.log(`🔬 Advanced Metrics: Variability SBP=${analysisResult.variability_stats.sbp_sd}, Range SBP=${analysisResult.range_stats.sbp_range}`);
    console.log(`⏰ Time Analysis: Evening SBP=${analysisResult.time_of_day.evening_sbp}, Night SBP=${analysisResult.time_of_day.night_sbp}`);
    console.log(`🚨 Alerts: ${analysisResult.alerts.length > 0 ? analysisResult.alerts.join(', ') : 'None'}`);
    
  } catch (error) {
    console.error(`❌ Error processing BP data for ${imei} on ${day}:`, error);
    throw error;
  }
};

/**
 * Process all available days for a patient
 * @param {string} imei - Device IMEI
 */
export const processAllDays = async (imei) => {
  try {
    console.log(`\n=== Processing all available days for IMEI: ${imei} ===`);
    
    // Get available days
    const availableDays = await getAvailableDays(imei);
    
    if (availableDays.length === 0) {
      console.log(`No data found for IMEI: ${imei}`);
      return;
    }
    
    console.log(`Found ${availableDays.length} days with data:`);
    availableDays.forEach(day => {
      console.log(`  - ${day.day}: ${day.count} readings`);
    });
    
    // Process each day
    for (const dayInfo of availableDays) {
      await processBPDay(imei, dayInfo.day);
    }
    
    console.log(`\n✅ Successfully processed all ${availableDays.length} days for IMEI: ${imei}`);
    
  } catch (error) {
    console.error(`❌ Error processing all days for ${imei}:`, error);
    throw error;
  }
};

/**
 * Initialize R_BP database with indexes
 */
export const initializeDatabase = async () => {
  try {
    console.log('Initializing R_BP database...');
    await createRBPIndexes();
    console.log('✅ R_BP database initialized successfully');
  } catch (error) {
    console.error('❌ Error initializing database:', error);
    throw error;
  }
};

/**
 * Get processed results for a patient
 * @param {string} imei - Device IMEI
 * @param {string} day - Day in YYYY-MM-DD format (optional)
 * @returns {Object} Processed results
 */
export const getProcessedResults = async (imei, day = null) => {
  try {
    const db = await getRBPDB();
    const collection = db.collection('bp_processed');
    const patientId = `patient_${imei}`;
    
    let query = { patientId };
    if (day) {
      query.day = day;
    }
    
    const results = await collection.find(query).sort({ day: -1 }).toArray();
    
    console.log(`Found ${results.length} processed results for patient ${patientId}`);
    return results;
    
  } catch (error) {
    console.error('Error getting processed results:', error);
    throw error;
  }
};
