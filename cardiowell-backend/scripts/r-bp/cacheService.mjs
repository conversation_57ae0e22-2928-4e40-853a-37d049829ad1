import { BpMetricsCache } from "../../models/bpMetricsCache.mjs"

export async function saveMetricsToCache({ patientId, imei, rangeStart, rangeEnd, lastReadingTsIncluded, data, version = "1.0.0", source = "on-demand-dashboard" }) {
  const doc = new BpMetricsCache({
    patientId,
    imei,
    rangeStart: rangeStart || null,
    rangeEnd: rangeEnd || null,
    lastReadingTsIncluded: lastReadingTsIncluded || null,
    computedAt: new Date(),
    version,
    source,
    data,
  })
  await doc.save()
  return doc
}

export async function getLatestMetricsFromCache(imei, { rangeStart, rangeEnd } = {}) {
  const query = { imei }
  if (rangeStart) query.rangeStart = rangeStart
  if (rangeEnd) query.rangeEnd = rangeEnd
  return BpMetricsCache.findOne(query).sort({ computedAt: -1 }).lean().exec()
}


