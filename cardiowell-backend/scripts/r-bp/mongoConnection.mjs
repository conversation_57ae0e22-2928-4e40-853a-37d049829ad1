import pkg from 'mongodb'
const { MongoClient } = pkg
import dotenv from 'dotenv'

dotenv.config()

let clientMain = null
let clientRbp = null

export const connectToMain = async () => {
  if (!clientMain) {
    const uri = process.env.mongoUri || 'mongodb://localhost:27017/cardiowell_local'
    clientMain = new MongoClient(uri)
    await clientMain.connect()
    console.log('[Mongo] Connected to main DB')
  }
  return clientMain
}

export const connectToRBP = async () => {
  if (!clientRbp) {
    const uri = process.env.mongoUri || 'mongodb://localhost:27017/cardiowell_local'
    clientRbp = new MongoClient(uri)
    await clientRbp.connect()
    console.log('[Mongo] Connected to R_BP DB (same instance)')
  }
  return clientRbp
}

export const getCardiowellDB = async () => {
  const c = await connectToMain()
  return c.db()
}

export const getRBPDB = async () => {
  const c = await connectToRBP()
  return c.db()
}

export const createRBPIndexes = async () => {
  const db = await getRBPDB()
  await db.collection('bp_duration_metrics').createIndex({ patientId: 1 }, { unique: true })
  await db.collection('bp_duration_metrics').createIndex({ lastUpdated: -1 })
  // Helpful for recent views and dashboards
  await db.collection('bp_duration_metrics').createIndex({ 'metrics7Days.computedAt': -1 })
  await db.collection('bp_duration_metrics').createIndex({ 'metrics7Days.dateRange.end': -1 })
}

export const getAllDurationMetrics = async (patientId) => {
  const db = await getRBPDB()
  const coll = db.collection('bp_duration_metrics')
  return coll.findOne({ patientId })
}

// Default document factory for bp_duration_metrics
export const createDurationMetricsDocument = (patientId) => ({
  patientId,
  lastUpdated: new Date(),
  metrics7Days: { duration: '7Days', days: 7, totalReadings: 0, dateRange: { start: null, end: null }, basicStats: {}, variabilityStats: {}, rangeStats: {}, pressureStats: {}, circadianStats: {}, timeOfDay: {}, classificationCounts: {}, alerts: [], combinedStats: {}, lastReading: null, computedAt: null },
  metrics1Month: { duration: '1Month', days: 30, totalReadings: 0, dateRange: { start: null, end: null }, basicStats: {}, variabilityStats: {}, rangeStats: {}, pressureStats: {}, circadianStats: {}, timeOfDay: {}, classificationCounts: {}, alerts: [], combinedStats: {}, lastReading: null, computedAt: null },
  metrics3Months: { duration: '3Months', days: 90, totalReadings: 0, dateRange: { start: null, end: null }, basicStats: {}, variabilityStats: {}, rangeStats: {}, pressureStats: {}, circadianStats: {}, timeOfDay: {}, classificationCounts: {}, alerts: [], combinedStats: {}, lastReading: null, computedAt: null },
  metrics6Months: { duration: '6Months', days: 180, totalReadings: 0, dateRange: { start: null, end: null }, basicStats: {}, variabilityStats: {}, rangeStats: {}, pressureStats: {}, circadianStats: {}, timeOfDay: {}, classificationCounts: {}, alerts: [], combinedStats: {}, lastReading: null, computedAt: null },
  metrics1Year: { duration: '1Year', days: 365, totalReadings: 0, dateRange: { start: null, end: null }, basicStats: {}, variabilityStats: {}, rangeStats: {}, pressureStats: {}, circadianStats: {}, timeOfDay: {}, classificationCounts: {}, alerts: [], combinedStats: {}, lastReading: null, computedAt: null },
  metrics2Years: { duration: '2Years', days: 730, totalReadings: 0, dateRange: { start: null, end: null }, basicStats: {}, variabilityStats: {}, rangeStats: {}, pressureStats: {}, circadianStats: {}, timeOfDay: {}, classificationCounts: {}, alerts: [], combinedStats: {}, lastReading: null, computedAt: null },
  metricsAll: { duration: 'All', days: null, totalReadings: 0, dateRange: { start: null, end: null }, basicStats: {}, variabilityStats: {}, rangeStats: {}, pressureStats: {}, circadianStats: {}, timeOfDay: {}, classificationCounts: {}, alerts: [], combinedStats: {}, lastReading: null, computedAt: null },
  createdAt: new Date(),
  updatedAt: new Date(),
})

// Get or initialize duration metrics document
export const getDurationMetrics = async (patientId) => {
  const db = await getRBPDB()
  const collection = db.collection('bp_duration_metrics')
  const existing = await collection.findOne({ patientId })
  if (existing) return existing
  const doc = createDurationMetricsDocument(patientId)
  await collection.insertOne(doc)
  return doc
}

// Update one duration block
export const updateDurationMetrics = async (patientId, durationName, metrics) => {
  const db = await getRBPDB()
  const collection = db.collection('bp_duration_metrics')
  const updateDoc = {
    [`metrics${durationName}`]: { ...metrics, computedAt: new Date() },
    lastUpdated: new Date(),
    updatedAt: new Date(),
  }
  await collection.updateOne({ patientId }, { $set: updateDoc }, { upsert: true })
  console.log(`[Mongo] Updated metrics for ${patientId} ${durationName}`)
}


