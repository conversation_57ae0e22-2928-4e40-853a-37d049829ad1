import { EventEmitter } from 'events'
import dotenv from 'dotenv'
import { processAllDurations } from './durationCalculator.mjs'

dotenv.config()

const FEATURE_R_ANALYTICS = String(process.env.FEATURE_R_ANALYTICS || 'false') === 'true'
const ON_WRITE_ANALYTICS = String(process.env.FEATURE_BP_ON_WRITE || 'true') === 'true'
const ROLLOUT_MODE = (process.env.BP_ROLLOUT_MODE || 'all').toLowerCase() // 'all' | 'canary'
const CANARY_IMEIS = (process.env.BP_CANARY_IMEIS || '')
  .split(',')
  .map(v => v.trim())
  .filter(v => v.length > 0)
const DEBOUNCE_MS = parseInt(process.env.BP_ON_WRITE_DEBOUNCE_MS || '15000', 10)

const emitter = new EventEmitter()
const pendingTimers = new Map()

function scheduleProcessing(imei) {
  if (ROLLOUT_MODE === 'canary' && CANARY_IMEIS.length > 0 && !CANARY_IMEIS.includes(String(imei))) {
    return
  }
  if (pendingTimers.has(imei)) {
    clearTimeout(pendingTimers.get(imei))
  }
  const timer = setTimeout(async () => {
    pendingTimers.delete(imei)
    try {
      if (!ON_WRITE_ANALYTICS) return
      console.log(`[BP] On-write processing start for IMEI ${imei} (FEATURE_R_ANALYTICS=${FEATURE_R_ANALYTICS})`)
      await processAllDurations(imei)
      console.log(`[BP] On-write processing completed for IMEI ${imei}`)
    } catch (err) {
      console.error(`[BP] On-write processing failed for IMEI ${imei}:`, err.message)
    }
  }, DEBOUNCE_MS)
  pendingTimers.set(imei, timer)
}

export function emitNewBPReading(imei, manufacturer = 'unknown', sourceCollection = 'unknown', timestamp = new Date().toISOString()) {
  try {
    emitter.emit('bp:new', { imei, manufacturer, sourceCollection, timestamp })
  } catch (err) {
    console.error('[BP] Failed to emit new reading event:', err.message)
  }
}

// Listener registration
emitter.on('bp:new', (evt) => {
  if (!evt || !evt.imei) return
  scheduleProcessing(evt.imei)
})

export default { emitNewBPReading }


