/**
 * Common DataTable configuration for admin dashboard tables
 * Used across multiple table components for consistent behavior
 */

export const dataTableConfig = {
  retrieve: true,
  lengthMenu: [
    [10, 100, -1],
    [10, 100, 'All'],
  ],
  pageLength: 10,
  searching: true,
  ordering: true,
  info: true,
  paging: true,
  responsive: true,
}

/**
 * Helper function to initialize DataTables with consistent configuration
 * @param {Array} elements - Array of objects with { element, tableId, hasData }
 */
export const initializeDataTables = (elements) => {
  const $ = require('jquery')
  $.DataTable = require('datatables.net')

  elements.forEach(({ element, tableId, hasData = true }) => {
    if (element && hasData && !$.fn.DataTable.isDataTable(tableId)) {
      $(element).DataTable(dataTableConfig)
    }
  })
}

/**
 * Helper function to destroy DataTable instances
 * @param {Array} tableIds - Array of table selector strings
 */
export const destroyDataTables = (tableIds) => {
  const $ = require('jquery')
  $.DataTable = require('datatables.net')

  tableIds.forEach((tableId) => {
    if ($.fn.DataTable.isDataTable(tableId)) {
      $(tableId).DataTable().destroy()
    }
  })
}
