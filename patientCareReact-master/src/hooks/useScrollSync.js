/**
 * Custom hook for synchronizing scroll between two elements
 */
import { useEffect, useCallback, useRef } from 'react'

/**
 * Hook to synchronize scrolling between two elements
 * @param {Object} sourceRef - Source scroll element ref
 * @param {Object} targetRef - Target scroll element ref
 * @param {Object} options - Configuration options
 * @param {boolean} options.enabled - Whether sync is enabled
 * @param {number} options.debounceMs - Debounce delay in milliseconds
 */
export const useScrollSync = (sourceRef, targetRef, options = {}) => {
  const { enabled = true, debounceMs = 0 } = options
  const isSyncingRef = useRef(false)

  const handleScroll = useCallback(
    (event) => {
      if (!enabled || !sourceRef?.current || !targetRef?.current) {
        return
      }

      // Prevent recursive updates
      if (isSyncingRef.current) return

      isSyncingRef.current = true

      try {
        // Sync scroll position
        targetRef.current.scrollLeft = sourceRef.current.scrollLeft
      } catch (error) {
        console.warn('useScrollSync: Error syncing scroll', error)
      } finally {
        // Reset syncing flag after a microtask
        if (debounceMs > 0) {
          setTimeout(() => {
            isSyncingRef.current = false
          }, debounceMs)
        } else {
          // Use microtask for immediate reset
          Promise.resolve().then(() => {
            isSyncingRef.current = false
          })
        }
      }
    },
    [enabled, sourceRef, targetRef, debounceMs]
  )

  useEffect(() => {
    if (!enabled || !sourceRef?.current || !targetRef?.current) {
      return
    }

    const sourceElement = sourceRef.current

    if (!sourceElement) {
      return
    }

    // Add event listener with passive option for better performance
    sourceElement.addEventListener('scroll', handleScroll, { passive: true })

    // Cleanup function
    return () => {
      if (sourceElement) {
        sourceElement.removeEventListener('scroll', handleScroll)
      }
    }
  }, [handleScroll, enabled, sourceRef, targetRef])
}

/**
 * Hook for bidirectional scroll synchronization
 * @param {Object} ref1 - First element ref
 * @param {Object} ref2 - Second element ref
 * @param {Object} options - Configuration options
 */
export const useBidirectionalScrollSync = (ref1, ref2, options = {}) => {
  useScrollSync(ref1, ref2, options)
  useScrollSync(ref2, ref1, options)
}
