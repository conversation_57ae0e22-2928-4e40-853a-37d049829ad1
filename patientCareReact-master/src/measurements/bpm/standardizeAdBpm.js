export const standardizeAdBpm = (bpm) => {
  const payload = bpm?.payload || {}

  // Prefer new format: payload.measurements[] with measurementType
  const systolicFromArray = payload.measurements?.find(
    (m) => m?.measurementType === 'systolic'
  )?.value
  const diastolicFromArray = payload.measurements?.find(
    (m) => m?.measurementType === 'diastolic'
  )?.value
  const pulseFromArray = payload.measurements?.find(
    (m) => m?.measurementType === 'pulse'
  )?.value

  // Legacy format fallbacks: payload.sys/dia/pulse
  const systolic = systolicFromArray ?? payload.sys ?? payload.systolic ?? null
  const diastolic =
    diastolicFromArray ?? payload.dia ?? payload.diastolic ?? null
  const pulse = pulseFromArray ?? payload.pulse ?? null

  // Handle timestamp as ISO string or seconds/milliseconds
  const rawTs = payload.timestamp ?? payload.ts ?? payload.uploadTimestamp
  let ts = null
  if (typeof rawTs === 'number') {
    // If value looks like seconds since epoch, convert to ms
    ts = rawTs < 1e12 ? rawTs * 1000 : rawTs
  } else if (typeof rawTs === 'string') {
    const parsed = Date.parse(rawTs)
    ts = Number.isNaN(parsed) ? null : parsed
  } else if (rawTs instanceof Date) {
    ts = rawTs.valueOf()
  }

  return {
    systolic,
    diastolic,
    pulse,
    ts,
  }
}
