import { jsPDF } from 'jspdf'
import autoTable from 'jspdf-autotable'
import { generateChartImage } from './chart-generator'
import {
  calculateWeightMeasurementStats,
  calculatePulseMeasurementStats,
  calculateGlucoseMeasurementStats,
} from './measurement-stats-utils'
import { addMeasurementStats } from './addMeasurementStats'
import { formatMonitoringTime } from './time-utils'
import { addBloodPressureSection } from './addBloodPressureSection'
import { addMedicalStatistics } from './pdf-generator/addMedicalStatistics'
import { formatDate } from './formtDate'

const textLabelValue = (doc, yPosition) => (label, value) => {
  doc.text(label, 14, yPosition)
  doc.setFont('helvetica', 'bold')
  const labelWidth = doc.getTextWidth(label)
  doc.text(value || 'N/A', 14 + labelWidth, yPosition)
  doc.setFont('helvetica', 'normal')
  return yPosition + 5
}

export async function generatePDF(data, options) {
  // Set default options if undefined
  const defaultOptions = {
    isBloodPressureVisible: true,
    isWeightScaleVisible: true,
    isSpo2Visible: true,
    isGlucoseVisible: true,
  }

  const finalOptions = options || defaultOptions

  // Ensure data arrays are defined
  const bpmTableArray = data.bpmTableArray || []
  const wsTableArray = data.wsTableArray || []
  const pulseTableArray = data.pulseTableArray || []
  const glucoseTableArray = data.glucoseTableArray || []
  const weightUnit = data.weightUnit || 'Lbs'
  const clinicalNotes = data.clinicalNotes || []

  console.log('options.endDate >>>', options.endDate)

  const monitoringPeriod =
    options.startDate && options.endDate
      ? `${formatDate(options.startDate, data.patientInfo.timeZone)} - ${formatDate(options.endDate, data.patientInfo.timeZone)}`
      : 'N/A'

  // Create a new PDF document
  const doc = new jsPDF()
  let yPosition = 15

  // Add report title
  doc.setFontSize(20)
  doc.text('Patient RPM Report', 105, yPosition, { align: 'center' })
  yPosition += 20

  // Add patient information section
  doc.setFontSize(14)
  doc.text('Patient Information', 14, yPosition)
  yPosition += 10

  doc.setFontSize(10)

  yPosition = textLabelValue(doc, yPosition)(
    'Patient ID: ',
    data.patientInfo?.id
  )

  yPosition = textLabelValue(doc, yPosition)(
    'Patient Name: ',
    data.patientInfo?.name
  )

  yPosition = textLabelValue(doc, yPosition)(
    'Medical Record Number: ',
    data.patientInfo?.mrn
  )

  yPosition = textLabelValue(doc, yPosition)(
    'Date of Birth: ',
    data.patientInfo?.birthdate
      ? formatDate(data.patientInfo?.birthdate, data.patientInfo.timeZone)
      : null
  )

  yPosition = textLabelValue(doc, yPosition)(
    'Add Program Start Date: ',
    data.firstReading
      ? formatDate(data.firstReading, data.patientInfo.timeZone)
      : null
  )

  yPosition = textLabelValue(doc, yPosition)(
    'Monitoring Period: ',
    monitoringPeriod
  )

  yPosition += 10

  // Add contact information section
  doc.setFontSize(14)
  doc.text('Contact Information', 14, yPosition)
  yPosition += 10

  doc.setFontSize(10)
  const fullAddress = [
    data.patientInfo?.address,
    data.patientInfo?.city,
    data.patientInfo?.state,
    data.patientInfo?.zip,
  ]
    .filter(Boolean)
    .join(', ')

  doc.text(`Address: ${fullAddress || 'N/A'}`, 14, yPosition)
  yPosition += 5
  doc.text(
    `Home Phone Number: ${data.patientInfo?.homeNumber || 'N/A'}`,
    14,
    yPosition
  )
  yPosition += 5
  doc.text(
    `Cell Phone Number: ${data.patientInfo?.cellNumber || 'N/A'}`,
    14,
    yPosition
  )
  yPosition += 5
  doc.text(`Email: ${data.patientInfo?.email || 'N/A'}`, 14, yPosition)
  yPosition += 15

  // Add medical statistics summary
  yPosition = addMedicalStatistics(
    doc,
    { bpmTableArray, wsTableArray, pulseTableArray, glucoseTableArray },
    yPosition,
    finalOptions
  )

  // Add blood pressure chart and table if visible
  if (finalOptions.isBloodPressureVisible) {
    yPosition = await addBloodPressureSection(
      doc,
      bpmTableArray,
      yPosition,
      data.commonAvgDia,
      data.commonAvgSys,
      data.patientInfo.timeZone
    )
  }

  // Add weight chart and table if visible
  if (finalOptions.isWeightScaleVisible) {
    yPosition = await addWeightSection(
      doc,
      wsTableArray,
      weightUnit,
      yPosition,
      data.patientInfo.timeZone
    )
  }

  // Add pulse chart and table if visible
  if (finalOptions.isSpo2Visible) {
    yPosition = await addPulseSection(
      doc,
      pulseTableArray,
      yPosition,
      data.patientInfo.timeZone
    )
  }

  // Add glucose chart and table if visible
  if (finalOptions.isGlucoseVisible) {
    yPosition = await addGlucoseSection(
      doc,
      glucoseTableArray,
      yPosition,
      data.patientInfo.timeZone
    )
  }

  // Add clinical notes at the end if available
  if (clinicalNotes.length > 0) {
    yPosition = addClinicalNotes(doc, clinicalNotes, yPosition)
  }

  // Add monitoring time at the very end
  yPosition = addMonitoringTime(doc, data.patientInfo?.rt, yPosition)

  // Save the PDF with patient ID or a default name
  doc.save(`patient_report_${data.patientInfo?.id || 'report'}.pdf`)
}

// Function to decode HTML entities like &#x2F; back to /
const decodeHtmlEntities = (text) => {
  if (!text) return text

  // Use a more reliable approach that handles all common HTML entities
  return text
    .replace(/&#x2F;/g, '/')
    .replace(/&#x27;/g, "'")
    .replace(/&quot;/g, '"')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&amp;/g, '&')
}

function addClinicalNotes(doc, notes, yPosition) {
  // Check if we need a new page
  if (yPosition > 200) {
    doc.addPage()
    yPosition = 20
  }

  // Add section title
  doc.setFontSize(14)
  doc.text('Clinical Notes', 14, yPosition)
  yPosition += 15

  // Sort notes by date (newest first)
  const sortedNotes = [...notes].sort(
    (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  )

  for (const note of sortedNotes) {
    // Check if we need a new page for this note
    if (yPosition > 240) {
      doc.addPage()
      yPosition = 20
    }

    // Format date and provider (American format)
    const date = new Date(note.createdAt)
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const year = date.getFullYear()
    const hour = date.getHours()
    const minute = date.getMinutes().toString().padStart(2, '0')
    const ampm = hour >= 12 ? 'pm' : 'am'
    const hour12 = hour % 12 || 12

    const formattedDate = `${month}/${day}/${year}; ${hour12}:${minute}${ampm}`
    const providerName = `${note.providerName.firstName} ${note.providerName.lastName}`

    // Add date and provider
    doc.setFontSize(10)
    doc.setFont('helvetica', 'bold')
    doc.text(formattedDate, 14, yPosition)
    yPosition += 5

    doc.setFont('helvetica', 'normal')
    doc.text(providerName, 14, yPosition)
    yPosition += 5

    // Add note content with word wrapping
    doc.setFont('helvetica', 'bold')
    const notePrefix = 'Note: '
    doc.text(notePrefix, 14, yPosition)

    doc.setFont('helvetica', 'normal')
    const noteText = decodeHtmlEntities(note.note.trim())
    const splitNote = doc.splitTextToSize(noteText, 180)

    // Calculate the width of "Note: " to position the text correctly
    const prefixWidth = doc.getTextWidth(notePrefix)

    // Add the first line of the note on the same line as "Note:"
    if (splitNote.length > 0) {
      doc.text(splitNote[0], 14 + prefixWidth, yPosition)
      yPosition += 5

      // Add remaining lines
      for (let i = 1; i < splitNote.length; i++) {
        doc.text(splitNote[i], 14, yPosition)
        yPosition += 5
      }
    }

    yPosition += 10 // Add space between notes
  }

  return yPosition + 10
}

function addMonitoringTime(doc, monitoringTimeSeconds, yPosition) {
  // Check if we need a new page
  if (yPosition > 250) {
    doc.addPage()
    yPosition = 20
  }

  // Add section title
  doc.setFontSize(14)
  doc.text('Monitoring Time', 14, yPosition)
  yPosition += 15

  // Add monitoring time content
  doc.setFontSize(10)
  doc.text(
    'Total time spent monitoring and caring for the patient:',
    14,
    yPosition
  )
  yPosition += 10

  // Add formatted time
  doc.setFontSize(12)
  doc.setFont('helvetica', 'bold')
  const formattedTime = formatMonitoringTime(monitoringTimeSeconds)
  doc.text(formattedTime, 14, yPosition)
  doc.setFont('helvetica', 'normal')
  yPosition += 15

  return yPosition
}

async function addWeightSection(
  doc,
  readings,
  weightUnit,
  yPosition,
  timeZone
) {
  // Check if we need a new page
  if (yPosition > 220) {
    doc.addPage()
    yPosition = 20
  }

  // Add section title
  doc.setFontSize(14)
  doc.text('Weight Trend', 14, yPosition)
  yPosition += 10

  // Generate and add chart with smaller dimensions
  try {
    const chartImage = await generateChartImage({
      type: 'weight',
      data: readings,
      weightUnit,
      width: 500,
      height: 200,
    })

    // Add chart to PDF with smaller size
    doc.addImage(chartImage, 'PNG', 14, yPosition, 160, 64)
    yPosition += 70
  } catch (error) {
    console.error('Error generating weight chart:', error)
    yPosition += 10
  }

  // Check if we need a new page for the table
  if (yPosition > 200) {
    doc.addPage()
    yPosition = 20
  }

  // Add table title
  doc.setFontSize(14)
  doc.text('Weight Measurements', 14, yPosition)
  yPosition += 5

  // Add the table
  autoTable(doc, {
    startY: yPosition,
    head: [
      ['Date', 'Time', `Weight (${weightUnit === 'Lbs' ? 'lbs' : 'kgs'})`],
    ],
    body: readings.map((reading) => [
      formatDate(reading.date, timeZone),
      reading.time,
      reading.weight,
    ]),
    margin: { top: 10 },
    styles: { fontSize: 8 },
    headStyles: { fillColor: [66, 66, 66] },
  })

  // Get the final y position after the table is drawn
  let finalY = doc.lastAutoTable.finalY + 10

  // Add measurement statistics
  const weightStats = calculateWeightMeasurementStats(readings, timeZone)
  finalY = addMeasurementStats(doc, weightStats, 'Weight', finalY)

  return finalY
}

async function addPulseSection(doc, readings, yPosition, timeZone) {
  // Check if we need a new page
  if (yPosition > 220) {
    doc.addPage()
    yPosition = 20
  }

  // Add section title
  doc.setFontSize(14)
  doc.text('Pulse & SPO2 Trend', 14, yPosition)
  yPosition += 10

  // Generate and add chart with smaller dimensions
  try {
    const chartImage = await generateChartImage({
      type: 'pulse',
      data: readings,
      width: 500,
      height: 200,
    })

    // Add chart to PDF with smaller size
    doc.addImage(chartImage, 'PNG', 14, yPosition, 160, 64)
    yPosition += 70
  } catch (error) {
    console.error('Error generating pulse chart:', error)
    yPosition += 10
  }

  // Check if we need a new page for the table
  if (yPosition > 200) {
    doc.addPage()
    yPosition = 20
  }

  // Add table title
  doc.setFontSize(14)
  doc.text('Pulse & SPO2 Readings', 14, yPosition)
  yPosition += 5

  // Add the table
  autoTable(doc, {
    startY: yPosition,
    head: [['Date', 'Time', 'SPO2 (%)', 'BPM']],
    body: readings.map((reading) => [
      formatDate(reading.date, timeZone),
      reading.time,
      reading.spo2.toString(),
      reading.pr.toString(),
    ]),
    margin: { top: 10 },
    styles: { fontSize: 8 },
    headStyles: { fillColor: [66, 66, 66] },
  })

  // Get the final y position after the table is drawn
  let finalY = doc.lastAutoTable.finalY + 10

  // Add measurement statistics
  const pulseStats = calculatePulseMeasurementStats(readings, timeZone)
  finalY = addMeasurementStats(doc, pulseStats, 'Pulse & SPO2', finalY)

  return finalY
}

async function addGlucoseSection(doc, readings, yPosition, timeZone) {
  // Check if we need a new page
  if (yPosition > 220) {
    doc.addPage()
    yPosition = 20
  }

  // Add section title
  doc.setFontSize(14)
  doc.text('Blood Glucose Trend', 14, yPosition)
  yPosition += 10

  // Generate and add chart with smaller dimensions
  try {
    const chartImage = await generateChartImage({
      type: 'glucose',
      data: readings,
      width: 500,
      height: 200,
    })

    // Add chart to PDF with smaller size
    doc.addImage(chartImage, 'PNG', 14, yPosition, 160, 64)
    yPosition += 70
  } catch (error) {
    console.error('Error generating glucose chart:', error)
    yPosition += 10
  }

  // Check if we need a new page for the table
  if (yPosition > 200) {
    doc.addPage()
    yPosition = 20
  }

  // Add table title
  doc.setFontSize(14)
  doc.text('Blood Glucose Readings', 14, yPosition)
  yPosition += 5

  // Add the table
  console.log('readings', readings)

  autoTable(doc, {
    startY: yPosition,
    head: [['Date', 'Time', 'Glucose (mg/dL)']],
    body: readings.map((reading) => [
      formatDate(reading.date, timeZone),
      reading.time,
      reading.data.toString(),
    ]),
    margin: { top: 10 },
    styles: { fontSize: 8 },
    headStyles: { fillColor: [66, 66, 66] },
  })

  // Get the final y position after the table is drawn
  let finalY = doc.lastAutoTable.finalY + 10

  // Add measurement statistics
  const glucoseStats = calculateGlucoseMeasurementStats(readings, timeZone)
  finalY = addMeasurementStats(doc, glucoseStats, 'Blood Glucose', finalY)

  return finalY
}
