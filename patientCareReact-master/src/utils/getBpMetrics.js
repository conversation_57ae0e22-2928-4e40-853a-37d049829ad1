export async function getBpMetrics(imei, { start, end, tz } = {}) {
  if (!imei) return null
  const params = new URLSearchParams()
  if (start) params.append('start', start)
  if (end) params.append('end', end)
  if (tz) params.append('tz', tz)

  const url = `/api/bp-metrics/${encodeURIComponent(imei)}${params.toString() ? `?${params.toString()}` : ''}`
  const res = await fetch(url, { method: 'GET', credentials: 'include' })
  if (!res.ok) throw new Error(`Failed to fetch BP metrics: ${res.status}`)
  const data = await res.json()
  return data
}


