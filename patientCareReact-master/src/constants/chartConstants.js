/**
 * Chart constants and static data
 */

/**
 * Blood pressure categories for tooltips
 */
export const SYSTOLIC_CATEGORIES = [
  { range: 'LESS THAN 120', category: 'NORMAL', color: '#4CAF50' },
  { range: '120-129', category: 'ELEVATED', color: '#FFC107' },
  { range: '130-139', category: 'STAGE 1', color: '#FF9800' },
  { range: '140 OR HIGHER', category: 'STAGE 2', color: '#F44336' },
  { range: 'HIGHER THAN 180', category: 'CRISIS', color: '#B71C1C' },
]

export const DIASTOLIC_CATEGORIES = [
  { range: 'LESS THAN 80', category: 'NORMAL', color: '#4CAF50' },
  { range: 'LESS THAN 80', category: 'ELEVATED', color: '#FFC107' },
  { range: '80-89', category: 'STAGE 1', color: '#FF9800' },
  { range: '90 OR HIGHER', category: 'STAGE 2', color: '#F44336' },
  { range: 'HIGHER THAN 120', category: 'CRISIS', color: '#B71C1C' },
]

/**
 * Chart dimensions and spacing
 */
export const CHART_DIMENSIONS = {
  MOBILE_FONT_SIZE: {
    DATE: 8,
    TIME: 8,
    AXIS_LABEL: 10,
  },
  DESKTOP_FONT_SIZE: {
    DATE: 9,
    TIME: 10,
    AXIS_LABEL: 12,
  },
  Y_AXIS_WIDTH: 60,
  SCROLLBAR_HEIGHT: 8,
  DEFAULT_MARGINS: {
    TOP: '10%',
    RIGHT: '3%',
    BOTTOM: '5%',
    LEFT: '1%',
  },
  MOBILE_MARGINS: {
    RIGHT: '25%',
  },
}

/**
 * Chart colors
 */
export const CHART_COLORS = {
  SYSTOLIC: '#3F51B5',
  DIASTOLIC: '#4CAF50',
  PULSE: '#3F51B5',
  GLUCOSE: '#3F51B5',
  AXIS_LINE: '#E0E0E0',
  AXIS_LABEL: '#9E9E9E',
  TOOLTIP_BORDER: '#e0e0e0',
  SCROLLBAR_THUMB: '#E0E0E0',
  SCROLLBAR_THUMB_HOVER: '#999',
}

/**
 * Chart line styles
 */
export const LINE_STYLES = {
  DEFAULT: {
    width: 2,
    type: 'solid',
  },
  DASHED: {
    width: 2,
    type: 'dashed',
    dashArray: [4, 4],
  },
}

/**
 * Target points visible for scrolling calculations
 */
export const TARGET_POINTS_VISIBLE = 21

/**
 * Special duration days that require interval 0
 */
export const SPECIAL_DURATION_DAYS = [93, 186, 365, 730]

/**
 * Default chart options structure
 */
export const DEFAULT_CHART_OPTIONS = {
  animation: false,
  legend: {
    show: false,
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'line',
      lineStyle: {
        color: CHART_COLORS.TOOLTIP_BORDER,
        width: 2,
      },
    },
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    axisLine: {
      show: true,
      lineStyle: {
        color: CHART_COLORS.AXIS_LINE,
        width: 0.5,
      },
    },
    axisTick: {
      show: false,
    },
    axisLabel: {
      show: true,
      margin: 10,
    },
  },
  yAxis: {
    type: 'value',
    splitLine: {
      show: false,
    },
    axisLabel: {
      margin: 30,
    },
  },
}

/**
 * Label configurations for different chart types
 */
export const LABEL_CONFIGS = {
  DEFAULT: {
    show: true,
    position: 'top',
    color: '#000000',
    formatter: '{c}',
    distance: 1,
    align: 'center',
    fontWeight: 'bold',
    padding: [0, 0, 10, 0],
  },
  BOTTOM: {
    show: true,
    position: 'bottom',
    color: '#000000',
    formatter: '{c}',
    distance: 1,
    align: 'center',
    fontWeight: 'bold',
    padding: [-30, 0, 0, 0],
  },
}

/**
 * Symbol configurations
 */
export const SYMBOL_CONFIGS = {
  DEFAULT: {
    type: 'circle',
    showSymbol: true,
  },
}

/**
 * Grid configurations for different chart layouts
 */
export const GRID_CONFIGS = {
  Y_AXIS_ONLY: {
    left: 0,
    right: 0,
    top: 25,
    bottom: 45,
    containLabel: true,
  },
  MAIN_CHART: {
    left: 0,
    containLabel: true,
  },
  HEART_RATE: {
    top: 30,
    bottom: 40,
  },
}
