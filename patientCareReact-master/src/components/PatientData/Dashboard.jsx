import { useState, useMemo, useEffect, Fragment, useRef } from 'react'
import {
  Button,
  Container,
  Grid,
  MenuItem,
  Paper,
  Select,
} from '@material-ui/core'
import { Box, Typography, IconButton } from '@mui/material'
import { useTheme } from '@mui/material/styles'
import SmsIcon from '@mui/icons-material/Sms'
import EmailIcon from '@mui/icons-material/Email'
import moment from 'moment-timezone'
import timeme from 'timeme.js'
import { useStyles } from '../common/style'
import clsx from 'clsx'
import { useWeightUnit } from '../common/DisplaySetings'
import { useResponseState } from '../../common/useResponsiveState'
import { parseBpMeasures } from './BloodPressure/bloodPressure'
import {
  BloodPressureChart,
  HeartRateChart,
} from './BloodPressure/BloodPressureCharts'
import { BloodPressureGrid } from './BloodPressure/BloodPressureGrid'
import { parsePulseMeasures } from './Pulse/pulseOximeter'
import {
  BloodOxygenChart,
  HeartRateOximeterChart,
} from './Pulse/PulseOximeterCharts'
import { PulseOximeterGrid } from './Pulse/PulseOximeterGrid'
import { parseGlucoseMeasures } from './Glucose/glucose'
import { GlucoseChart } from './Glucose/GlucoseCharts'
import { GlucoseGrid } from './Glucose/GlucoseGrid'
import { parseWeightScaleMeasures } from './WeightScale/weightScale'
import { WeightScaleChart } from './WeightScale/WeightScaleCharts'
import { WeightScaleGrid } from './WeightScale/WeightScaleGrid'
import { DurationButtons } from './DurationButtons'
import { CARDIOWELL } from '../../common/manufacters'
import { EditPatientModal } from '../../patient/EditPatientModal'
import { providerDeletePatient } from '../../patient/providerDeletePatient'
import { providerSavePatientChanges } from '../../patient/providerSavePatientChanges'
import { sendTimerData } from './sendTimerData'
import { ClinicalNoteView } from './ClinicalNote/ClinicalNoteView'
import { useColumnVisibilityModel } from '../../provider/PatientListGrid/useColumnVisibilityModel'
import { secondsToHMS } from '../../common/conversions'
import { useNotificationModal } from '../../provider/useNotificationModal'
import { NotificationModal } from '../../provider/NotificationModal'
import { durationMap } from './ResponsiveDurationButtons'
import { getReport } from '../../utils/getReport'
import { getBpMetrics } from '../../utils/getBpMetrics'

const CHART = 'Chart'
const READINGS = 'Readings'
const NOTE = 'clinicalNotes'

export const PatientDataDashboard = ({
  patientData,
  clinic,
  removePatientFromList,
  updatePatientInList,
  updatePatientData,
  updateTime,
}) => {
  const theme = useTheme()
  const { weightUnit } = useWeightUnit()
  const { isMobile } = useResponseState()
  const classes = useStyles()
  const [months, setMonths] = useState(1)
  const [durationText, setDurationText] = useState('Last 1 month')
  const [tableView, setTableView] = useState('Chart')
  const [showEditPatientModal, setShowEditPatientModal] = useState(false)
  const [liveTime, setLiveTime] = useState(0)
  const [bpMetrics, setBpMetrics] = useState(null)
  const [bpMetricsError, setBpMetricsError] = useState(null)
  const {
    isBloodPressureVisible,
    isWeightScaleVisible,
    isSpo2Visible,
    isGlucoseVisible,
  } = useColumnVisibilityModel()
  const {
    showNotificationModal,
    notificationModalMessage,
    sendSMS,
    sendEmail,
    onClose,
  } = useNotificationModal()
  const providerId = sessionStorage.getItem('providerID')

  const fixedHeightPaper = clsx(classes.paper, classes.fixedHeight)

  const hasValue = (value) =>
    value !== undefined && value !== null && String(value).trim() !== ''

  const renderInfoItem = (label, value) =>
    hasValue(value) ? (
      <MenuItem style={{ backgroundColor: 'white' }}>
        {label}: {value}
      </MenuItem>
    ) : null

  const bpDevice = patientData.selectedBpDevice || CARDIOWELL
  const weightDevice = patientData.selectedWeightDevice || CARDIOWELL
  const threshold = patientData.threshold || {}

  // Convert months to days
  const days = useMemo(() => {
    switch (months) {
      case 1:
        return 30
      case 3:
        return 90 // 3 months (from DurationButtons)
      case 6:
        return 180 // 6 months
      case 12:
        return 365 // 1 year
      case 24:
        return 730 // 2 years
      case 20:
        return 10000 // All time
      default:
        return 30
    }
  }, [months])

  const isAllTimeFilter = days === 10000

  const timeZone =
    patientData?.timeZone && patientData.timeZone !== 'local'
      ? patientData.timeZone
      : moment.tz.guess()
  const d = useMemo(() => {
    const now = moment()
    return now.subtract(months, 'months').startOf('day').toDate()
  }, [months])

  const endDate = useMemo(() => {
    return moment().endOf('day').toDate()
  }, [])

  const deletePatient = async () => {
    try {
      const response = await providerDeletePatient(patientData.id, providerId)
      if (response.message === 'Success') {
        timeme.stopTimer(`${patientData.id}`)
        timeme.resetRecordedPageTime(`${patientData.id}`)
        removePatientFromList(patientData.id)
      }
      return response
    } catch (error) {
      console.error('Error deleting patient:', error)
      return { error: error.message }
    }
  }

  const savePatientChanges = async (patient) => {
    try {
      const response = await providerSavePatientChanges(patient)

      if (response.message === 'Success') {
        let updatedPatient =
          response.data || response.patient || response.updatedPatient

        if (
          !updatedPatient ||
          typeof updatedPatient !== 'object' ||
          !updatedPatient.id
        ) {
          updatedPatient = {
            ...patientData,
            ...patient,
            name: `${patient.firstName || patientData.firstName || ''} ${patient.lastName || patientData.lastName || ''}`.trim(),
          }
        }

        if (updatePatientData) {
          updatePatientData(updatedPatient)
        } else {
          updatePatientInList(updatedPatient)
        }
      }

      return response
    } catch (error) {
      console.error('Error saving patient changes:', error)
      return { error: error.message }
    }
  }

  const {
    arrayBP,
    bpmTableArray,
    highSys,
    highDia,
    lowSys,
    lowDia,
    avgSys,
    avgDia,
  } = useMemo(
    () =>
      parseBpMeasures({
        bpm: patientData?.bpm,
        btMessagesBpm: patientData?.btMessagesBpm,
        ttBpm: patientData?.ttBpm,
        adBpm: patientData?.adBpm,
        withingsBpm: patientData?.withingsBpm,
        bpDevice,
        timeframe: d,
        endDate,
        timeZone,
        isAllTimeFilter,
      }),
    [patientData, bpDevice, timeZone, d, endDate, isAllTimeFilter]
  )

  const { arrayWS, wsTableArray, lowWeight, highWeight } = useMemo(
    () =>
      parseWeightScaleMeasures({
        ws: patientData?.ws,
        btMessagesWs: patientData?.btMessagesWs,
        ttWs: patientData?.ttWs,
        weightDevice,
        weightUnit,
        timeframe: d,
        timeZone,
      }),
    [patientData, weightDevice, weightUnit, timeZone, d]
  )

  const { pulseChartArray, pulseTableArray, averageSpo2 } = useMemo(
    () =>
      parsePulseMeasures({
        pulse: patientData?.pulse,
        timeframe: d,
        timeZone,
      }),
    [patientData, timeZone, d]
  )

  const { glucoseChartArray, glucoseTableArray, averageGlucose } = useMemo(
    () => parseGlucoseMeasures({ glucose: patientData?.glucose, timeframe: d }),
    [patientData, d]
  )

  const InfoGrid = ({ info, title }) => (
    <Grid item xs={6} md={3} lg={3}>
      <Paper className={fixedHeightPaper}>
        <Container>
          <h4 style={{ textAlign: 'center', marginTop: '40px' }}>{info}</h4>
          <h5 style={{ textAlign: 'center' }}>{title}</h5>
          <p style={{ textAlign: 'center', marginTop: '60px' }}>
            {durationText}
          </p>
        </Container>
      </Paper>
    </Grid>
  )

  const bpChartRef = useRef(null)
  const hrChartRef = useRef(null)

  const DoubleGrid = ({ info, title }) => (
    <Grid item xs={6} md={3} lg={3}>
      <Paper className={fixedHeightPaper} style={{ overflowY: 'hidden' }}>
        <Container>
          <h4 style={{ textAlign: 'center', marginTop: '10px' }}>{info}</h4>
          <h5 style={{ textAlign: 'center' }}>{title}</h5>
          <p style={{ textAlign: 'center', marginTop: '30px' }}>
            {durationText}
          </p>
        </Container>
      </Paper>
    </Grid>
  )

  useEffect(() => {
    timeme.startTimer(`${patientData.id}`)
    const interval = setInterval(
      () => setLiveTime(timeme.getTimeOnPageInSeconds(patientData.id) || 0),
      1000
    )

    return async () => {
      timeme.stopTimer(`${patientData.id}`)
      clearInterval(interval)
      const timerData = await sendTimerData(patientData.id)
      updateTime?.(timerData)
    }
  }, [patientData.id])

  // Fetch Phase 1 BP metrics on demand for the panel
  useEffect(() => {
    const fetchMetrics = async () => {
      try {
        setBpMetricsError(null)
        setBpMetrics(null)
        const imei = patientData?.bpIMEI || patientData?.ttBpIMEI
        if (!imei || !isBloodPressureVisible()) return
        const start = moment().subtract(months, 'months').format('YYYY-MM-DD')
        const end = moment().format('YYYY-MM-DD')
        const data = await getBpMetrics(imei, { start, end, tz: timeZone })
        setBpMetrics(data)
      } catch (e) {
        setBpMetricsError(e?.message || 'Failed to load BP metrics')
      }
    }
    fetchMetrics()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [patientData?.bpIMEI, patientData?.ttBpIMEI, months, timeZone])

  return (
    <main className={classes.content}>
      <div className={classes.appBarSpacer} />
      <div>
        <Container maxWidth="lg" className={classes.container}>
          <Box
            display="flex"
            flexDirection="row"
            alignItems="center"
            margin={theme.spacing(1)}
          >
            <Typography variant="h3">{patientData?.name}</Typography>
            <Select
              style={{ marginBottom: '10px' }}
              labelId="demo-simple-select-label"
              id="demo-simple-select"
              disableUnderline
            >
              {renderInfoItem('Transtek BP IMEI', patientData?.ttBpIMEI)}
              {renderInfoItem('Weight IMEI', patientData?.weightIMEI)}
              {renderInfoItem('Pulse Oximeter IMEI', patientData?.pulseIMEI)}
              {renderInfoItem('Glucose IMEI', patientData?.glucoseIMEI)}
              {renderInfoItem('MRN', patientData?.mrn)}
              {renderInfoItem('Email', patientData?.email)}
              {renderInfoItem('Cell #', patientData?.cellNumber)}
              {renderInfoItem('BP IMEI', patientData?.bpIMEI)}
            </Select>
            <Button onClick={() => setShowEditPatientModal(true)}>Edit</Button>
            <Button onClick={() => getReport(patientData.id, providerId)}>
              Print Report
            </Button>
            <IconButton
              onClick={() => sendSMS(patientData.cellNumber, patientData.name)}
              sx={{ color: 'black' }}
            >
              <SmsIcon />
            </IconButton>
            <IconButton
              onClick={() => sendEmail(patientData.email, patientData.name)}
              sx={{ color: 'black' }}
            >
              <EmailIcon />
            </IconButton>
            {patientData.rt != null && patientData.rt != undefined && (
              <Typography
                variant="body2"
                style={{
                  color: 'rgba(0, 0, 0, 0.6)',
                  fontSize: '14px',
                  fontWeight: 400,
                  lineHeight: '20px',
                }}
              >{`RT (${durationMap[months]}): ${secondsToHMS(patientData.rt + liveTime)}`}</Typography>
            )}
          </Box>
          <Grid container spacing={3}>
            {isBloodPressureVisible() && (
              <Fragment>
                <InfoGrid info={`${avgSys}/${avgDia}`} title={'Avg BP'} />
                <InfoGrid info={`${highSys}/${highDia}`} title={'High BP'} />
                <InfoGrid info={`${lowSys}/${lowDia}`} title={'Low BP'} />
                <DoubleGrid
                  info={
                    <div
                      style={{
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                      }}
                    >
                      {bpMetrics == null && !bpMetricsError && (
                        <h5 style={{ textAlign: 'center', margin: 0 }}>Loading stats…</h5>
                      )}
                      {bpMetricsError && (
                        <h5 style={{ textAlign: 'center', margin: 0 }}>{bpMetricsError}</h5>
                      )}
                      {bpMetrics && bpMetrics.success && bpMetrics.dataAvailable && (
                        <>
                          <h4 style={{ textAlign: 'center', margin: 0 }}>
                            {bpMetrics.data?.totals?.totalReadings ?? arrayBP.length}
                          </h4>
                          <h5 style={{ textAlign: 'center', margin: 0 }}>BP Readings</h5>
                          <h4 style={{ textAlign: 'center', margin: '16px 0 0 0' }}>
                            {bpMetrics.data?.totals?.daysWithReadings}
                          </h4>
                          <h5 style={{ textAlign: 'center', margin: 0 }}>Days with BP Readings</h5>
                        </>
                      )}
                      {bpMetrics && bpMetrics.success && !bpMetrics.dataAvailable && (
                        <h5 style={{ textAlign: 'center', margin: 0 }}>No recent BP data</h5>
                      )}
                    </div>
                  }
                  title={''}
                />
              </Fragment>
            )}
            {isSpo2Visible() && (
              <InfoGrid info={averageSpo2} title={'Avg SP02'} />
            )}
            {isGlucoseVisible() && (
              <InfoGrid info={averageGlucose} title={'Avg Glucose'} />
            )}
            {/* Phase 3: Additional BP tiles based on computed metrics (always appended at the end) */}
            {bpMetrics && bpMetrics.success && bpMetrics.dataAvailable && (
              <>
                <InfoGrid
                  info={`${bpMetrics.data?.stagesPct?.normal ?? 0}% / ${bpMetrics.data?.stagesPct?.elevated ?? 0}% / ${bpMetrics.data?.stagesPct?.stage1 ?? 0}% / ${bpMetrics.data?.stagesPct?.stage2 ?? 0}%`}
                  title={'BP Stage Mix (N/E/S1/S2)'}
                />
                <InfoGrid
                  info={`${bpMetrics.data?.timeOfDay?.morning?.avgSys ?? '-'} / ${bpMetrics.data?.timeOfDay?.morning?.avgDia ?? '-'} • ${bpMetrics.data?.timeOfDay?.evening?.avgSys ?? '-'} / ${bpMetrics.data?.timeOfDay?.evening?.avgDia ?? '-'}`}
                  title={'AM Avg • PM Avg'}
                />
              </>
            )}
          </Grid>
          <Box
            display="flex"
            flexDirection="row-reverse"
            alignItems="flex-end"
            justifyContent="space-between"
            marginTop="5px"
          >
            <DurationButtons
              months={months}
              setMonths={setMonths}
              setDurationText={setDurationText}
            />
            <div>
              <Button
                onClick={() => setTableView(CHART)}
                style={{
                  ...(tableView === CHART && { backgroundColor: '#F0F0F0' }),
                }}
              >
                Chart
              </Button>
              <Button
                onClick={() => setTableView(READINGS)}
                style={{
                  ...(tableView === READINGS && { backgroundColor: '#F0F0F0' }),
                }}
              >
                Readings
              </Button>
              <Button
                onClick={() => setTableView(NOTE)}
                style={{
                  ...(tableView === NOTE && { backgroundColor: '#F0F0F0' }),
                }}
              >
                Clinical Notes
              </Button>
            </div>
          </Box>
        </Container>
        {tableView === CHART && (
          <div>
            {isBloodPressureVisible() && (
              <Container maxWidth="lg" className={classes.container}>
                <Grid item xs={12} lg={12}>
                  <Paper className={fixedHeightPaper}>
                    <BloodPressureChart
                      timeZone={timeZone}
                      chartData={arrayBP}
                      threshold={threshold?.bloodPressure}
                      startDate={moment()
                        .subtract(months, 'months')
                        .startOf('day')
                        .toDate()}
                      endDate={moment().endOf('day').toDate()}
                      durationDays={days}
                      scrollRef={bpChartRef}
                      syncScrollRef={hrChartRef}
                      isAllTimeFilter={isAllTimeFilter}
                      isMobile={isMobile}
                    />
                  </Paper>
                </Grid>
              </Container>
            )}
            {isBloodPressureVisible() && (
              <Container maxWidth="lg" className={classes.container}>
                <Grid item xs={12} lg={12}>
                  <Paper className={fixedHeightPaper}>
                    <HeartRateChart
                      timeZone={timeZone}
                      chartData={arrayBP}
                      threshold={threshold?.bloodPressure?.pulse}
                      startDate={moment()
                        .subtract(months, 'months')
                        .startOf('day')
                        .toDate()}
                      endDate={moment().endOf('day').toDate()}
                      durationDays={days}
                      scrollRef={hrChartRef}
                      syncScrollRef={bpChartRef}
                      isAllTimeFilter={isAllTimeFilter}
                      isMobile={isMobile}
                    />
                  </Paper>
                </Grid>
              </Container>
            )}
            {isWeightScaleVisible() && (
              <Container maxWidth="lg" className={classes.container}>
                <Grid item xs={12} lg={12}>
                  <Paper className={fixedHeightPaper}>
                    <WeightScaleChart
                      chartData={arrayWS}
                      weightUnit={weightUnit}
                      lowWeight={lowWeight}
                      highWeight={highWeight}
                      timeZone={timeZone}
                      threshold={threshold?.weight?.weight}
                      durationDays={days}
                      isAllTimeFilter={isAllTimeFilter}
                    />
                  </Paper>
                </Grid>
              </Container>
            )}
            {isSpo2Visible() && (
              <Container maxWidth="lg" className={classes.container}>
                <Grid item xs={12} lg={12}>
                  <Paper className={fixedHeightPaper}>
                    <BloodOxygenChart
                      chartData={pulseChartArray}
                      threshold={threshold?.pulseOximeter?.spo2}
                      durationDays={days}
                      isAllTimeFilter={isAllTimeFilter}
                    />
                  </Paper>
                </Grid>
              </Container>
            )}
            {isSpo2Visible() && (
              <Container maxWidth="lg" className={classes.container}>
                <Grid item xs={12} lg={12}>
                  <Paper className={fixedHeightPaper}>
                    <HeartRateOximeterChart
                      chartData={pulseChartArray}
                      threshold={threshold?.pulseOximeter?.pulse}
                      durationDays={days}
                      isAllTimeFilter={isAllTimeFilter}
                    />
                  </Paper>
                </Grid>
              </Container>
            )}
            {isGlucoseVisible() && (
              <Container maxWidth="lg" className={classes.container}>
                <Grid item xs={12} lg={12}>
                  <Paper className={fixedHeightPaper}>
                    <GlucoseChart
                      chartData={glucoseChartArray}
                      threshold={threshold?.bloodGlucose?.glucose}
                      timeZone={patientData.timeZone}
                      startDate={d}
                      endDate={new Date()}
                      durationDays={days}
                      fitView={true}
                      isAllTimeFilter={isAllTimeFilter}
                    />
                  </Paper>
                </Grid>
              </Container>
            )}
          </div>
        )}
        {tableView === READINGS && (
          <div style={{ marginTop: '25px' }}>
            {isBloodPressureVisible() && (
              <Container maxWidth="lg" className={classes.container}>
                <Grid item xs={12} lg={12}>
                  <h5>BP</h5>
                  <Paper>
                    <BloodPressureGrid gridData={bpmTableArray} />
                  </Paper>
                </Grid>
              </Container>
            )}
            {isWeightScaleVisible() && (
              <Container maxWidth="lg" className={classes.container}>
                <Grid item xs={12} lg={12}>
                  <h5>Weight</h5>
                  <Paper>
                    <WeightScaleGrid
                      gridData={wsTableArray}
                      weightUnit={weightUnit}
                    />
                  </Paper>
                </Grid>
              </Container>
            )}
            {isSpo2Visible() && (
              <Container maxWidth="lg" className={classes.container}>
                <Grid item xs={12} lg={12}>
                  <h5>Pulse</h5>
                  <Paper>
                    <PulseOximeterGrid gridData={pulseTableArray} />
                  </Paper>
                </Grid>
              </Container>
            )}
            {isGlucoseVisible() && (
              <Container maxWidth="lg" className={classes.container}>
                <Grid item xs={12} lg={12}>
                  <h5>Glucose</h5>
                  <Paper>
                    <GlucoseGrid gridData={glucoseTableArray} />
                  </Paper>
                </Grid>
              </Container>
            )}
          </div>
        )}
        {tableView === NOTE && (
          <div style={{ marginTop: '25px' }}>
            <Container maxWidth="lg" className={classes.container}>
              <ClinicalNoteView patient={patientData} timeframe={d} />
            </Container>
          </div>
        )}
      </div>
      <EditPatientModal
        canDelete={!!providerId}
        open={showEditPatientModal}
        patientId={patientData.id}
        patient={patientData}
        canEditClinic={false}
        timezoneRequired={true}
        handleClose={() => setShowEditPatientModal(false)}
        savePatientChanges={savePatientChanges}
        deletePatient={deletePatient}
      />
      <NotificationModal
        message={notificationModalMessage}
        open={showNotificationModal}
        onClose={() => onClose()}
      />
    </main>
  )
}
