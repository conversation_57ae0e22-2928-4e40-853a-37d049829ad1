import moment from 'moment-timezone'
import { pascalsToTorr } from '../../../common/conversions'
import { TRANSTEK, WITHINGS, AD } from '../../../common/manufacters'
import { standardizeAdBpm } from '../../../measurements/bpm/standardizeAdBpm'

const parseTranstekBpm = (bpm) => {
  if (!bpm || typeof bpm !== 'object') {
    return {
      systolic: null,
      diastolic: null,
      ts: null,
      pulse: null,
    }
  }

  const systolic = bpm.systolic ? parseInt(bpm.systolic) : null
  const diastolic = bpm.diastolic ? parseInt(bpm.diastolic) : null
  const ts = bpm.ts ? bpm.ts * 1000 : null
  const pulse = bpm.pulse || null

  return {
    systolic,
    diastolic,
    ts,
    pulse,
  }
}

const parseWithingsBpm = (bpm) => {
  if (!bpm || typeof bpm !== 'object') {
    return {
      systolic: null,
      diastolic: null,
      ts: null,
      pulse: null,
    }
  }

  const systolic =
    bpm.sys?.value && bpm.sys?.unit !== undefined
      ? bpm.sys.value * Math.pow(10, bpm.sys.unit)
      : null
  const diastolic =
    bpm.dia?.value && bpm.dia?.unit !== undefined
      ? bpm.dia.value * Math.pow(10, bpm.dia.unit)
      : null
  const pulse =
    bpm.pulse?.value && bpm.pulse?.unit !== undefined
      ? bpm.pulse.value * Math.pow(10, bpm.pulse.unit)
      : null
  const ts = bpm.created ? bpm.created * 1000 : null

  return {
    systolic,
    diastolic,
    ts,
    pulse,
  }
}

const parseBodyTraceBpm = (bpm) => {
  if (!bpm || typeof bpm !== 'object') {
    return null
  }

  const ts = bpm._created_at
  const hasSystolic = bpm.systolic != null
  const hasDiastolic = bpm.diastolic != null

  // Return null if no timestamp or no measurement values
  if (!ts || (!hasSystolic && !hasDiastolic)) {
    return null
  }

  const systolic =
    bpm.systolic !== undefined ? pascalsToTorr(bpm.systolic) : null
  const diastolic =
    bpm.diastolic !== undefined ? pascalsToTorr(bpm.diastolic) : null
  const pulse = bpm.pulse !== undefined ? bpm.pulse : null

  return {
    systolic,
    diastolic,
    ts,
    pulse,
  }
}

const parseBodyTraceMessageBpm = (bpm) => {
  if (!bpm || typeof bpm !== 'object') {
    return null
  }

  // Check if we have valid timestamp and at least one measurement value
  const ts = bpm.message?.ts
  const hasSystolic = bpm.message?.values?.systolic != null
  const hasDiastolic = bpm.message?.values?.diastolic != null

  if (!ts || (!hasSystolic && !hasDiastolic)) {
    return null
  }

  const systolic = bpm.message?.values?.systolic
    ? pascalsToTorr(bpm.message.values.systolic)
    : null
  const diastolic = bpm.message?.values?.diastolic
    ? pascalsToTorr(bpm.message.values.diastolic)
    : null
  const pulse = bpm.message?.values?.pulse || null

  return {
    systolic,
    diastolic,
    pulse,
    ts,
  }
}

const createTableEntry = (measurement, timeZone) => {
  if (!measurement || measurement.ts === null) {
    return {
      date: null,
      time: null,
      systolic: null,
      diastolic: null,
      pulse: null,
      ts: null,
    }
  }

  const { systolic, diastolic, pulse, ts } = measurement
  const measurementDate = moment(ts).tz(timeZone)
  return {
    date: measurementDate.toDate(),
    time: measurementDate.format('h:mm A'), // US time format with AM/PM
    systolic,
    diastolic,
    pulse,
    ts, // Keep original timestamp for compatibility
  }
}

export const parseBpMeasures = ({
  bpm = [],
  btMessagesBpm = [],
  ttBpm = [],
  adBpm = [],
  withingsBpm = [],
  bpDevice = '',
  timeframe = 0,
  timeZone,
  endDate,
  isAllTimeFilter = false,
}) => {
  const arrayBP = []
  const bpmTableArray = []

  let firstReading = {}
  let lastReading = {}
  let avgSys = 0
  let avgDia = 0
  let highSys = ''
  let highDia = ''
  let lowSys = ''
  let lowDia = ''

  const timeframeMoment = moment(timeframe)
  const now = moment(endDate) || moment()

  // Blood Pressure Chart Array
  if (bpDevice === TRANSTEK) {
    if (ttBpm && ttBpm.length > 0 && ttBpm[ttBpm.length - 1]) {
      firstReading = parseTranstekBpm(ttBpm[ttBpm.length - 1])
    }
    // Transtek BP Device
    for (let z = 0; z < ttBpm.length; z++) {
      if (ttBpm[z]) {
        const measurement = parseTranstekBpm(ttBpm[z])
        const readingDate = moment(measurement.ts)
        const isInTimeRange =
          isAllTimeFilter ||
          readingDate.isBetween(timeframeMoment, now, 'day', '[]')
        if (isInTimeRange) {
          arrayBP.push(measurement)
          bpmTableArray.push(createTableEntry(measurement, timeZone))
          if (measurement.systolic !== null) avgSys += measurement.systolic
          if (measurement.diastolic !== null) avgDia += measurement.diastolic
        }
      }
    }
    if (ttBpm.length > 0 && ttBpm[0]) {
      lastReading = parseTranstekBpm(ttBpm[0])
    }
  } else if (bpDevice === WITHINGS) {
    if (
      withingsBpm &&
      withingsBpm.length > 0 &&
      withingsBpm[withingsBpm.length - 1]
    ) {
      firstReading = parseWithingsBpm(withingsBpm[withingsBpm.length - 1])
    }
    // Withings BP Device
    for (let z = 0; z < withingsBpm.length; z++) {
      if (withingsBpm[z]) {
        const measurement = parseWithingsBpm(withingsBpm[z])
        const readingDate = moment(measurement.ts)
        const isInTimeRange =
          isAllTimeFilter ||
          readingDate.isBetween(timeframeMoment, now, 'day', '[]')
        if (isInTimeRange) {
          arrayBP.push(measurement)
          bpmTableArray.push(createTableEntry(measurement, timeZone))
          if (measurement.systolic !== null) avgSys += measurement.systolic
          if (measurement.diastolic !== null) avgDia += measurement.diastolic
        }
      }
    }
    if (withingsBpm.length > 0 && withingsBpm[withingsBpm.length - 1]) {
      lastReading = parseWithingsBpm(withingsBpm[withingsBpm.length - 1])
    }
  } else if (bpDevice === AD) {
    if (adBpm && adBpm.length > 0 && adBpm[0]) {
      firstReading = standardizeAdBpm(adBpm[0])
    }
    for (let i = 0; i < adBpm.length; i++) {
      if (adBpm[i]) {
        const measurement = standardizeAdBpm(adBpm[i])
        const readingDate = moment(measurement.ts)
        const isInTimeRange =
          isAllTimeFilter ||
          readingDate.isBetween(timeframeMoment, now, 'day', '[]')
        if (isInTimeRange) {
          arrayBP.push(measurement)
          bpmTableArray.push(createTableEntry(measurement, timeZone))
          if (measurement.systolic !== null) avgSys += measurement.systolic
          if (measurement.diastolic !== null) avgDia += measurement.diastolic
        }
      }
    }
    if (adBpm.length > 0 && adBpm[0]) {
      lastReading = standardizeAdBpm(adBpm[0])
    }
  } else {
    // Bodytrace BP Device
    if (bpm && bpm.length > 0 && bpm[bpm.length - 1]) {
      firstReading = parseBodyTraceBpm(bpm[bpm.length - 1])
    }
    for (let z = 0; z < bpm.length; z++) {
      if (bpm[z]) {
        const measurement = parseBodyTraceBpm(bpm[z])
        if (measurement) {
          // Only process valid measurements
          const readingDate = moment(measurement.ts)
          const isInTimeRange =
            isAllTimeFilter ||
            readingDate.isBetween(timeframeMoment, now, 'day', '[]')
          if (isInTimeRange) {
            arrayBP.push(measurement)
            bpmTableArray.push(createTableEntry(measurement, timeZone))
            if (measurement.systolic !== null) avgSys += measurement.systolic
            if (measurement.diastolic !== null) avgDia += measurement.diastolic
          }
        }
      }
    }
    // bodytracemessages BP readings
    for (let z = 0; z < btMessagesBpm.length; z++) {
      const reading = btMessagesBpm[z]
      if (reading) {
        const measurement = parseBodyTraceMessageBpm(reading)
        if (measurement) {
          // Only process valid measurements
          const readingDate = moment(measurement.ts)
          const isInTimeRange =
            isAllTimeFilter ||
            readingDate.isBetween(timeframeMoment, now, 'day', '[]')
          if (isInTimeRange) {
            arrayBP.push(measurement)
            bpmTableArray.push(createTableEntry(measurement, timeZone))
            if (measurement.systolic !== null) avgSys += measurement.systolic
            if (measurement.diastolic !== null) avgDia += measurement.diastolic
          }
        }
      }
    }
    // sort both arrays
    if (btMessagesBpm.length && bpm.length) {
      arrayBP.sort((a, b) => b.ts - a.ts)
      bpmTableArray.sort((a, b) => b.date - a.date)
    }
    const recentBtMeasure = bpm?.[0]
    const recentBtMessage = btMessagesBpm?.[0]
    if (recentBtMeasure && recentBtMessage) {
      lastReading =
        recentBtMeasure.ts > recentBtMessage.ts
          ? parseBodyTraceBpm(recentBtMeasure)
          : parseBodyTraceMessageBpm(recentBtMessage)
    } else if (recentBtMeasure) {
      lastReading = parseBodyTraceBpm(recentBtMeasure)
    } else if (recentBtMessage) {
      lastReading = parseBodyTraceMessageBpm(recentBtMessage)
    }
  }

  // Calculate averages
  if (arrayBP.length > 0) {
    const validSystolicValues = arrayBP
      .filter((item) => item.systolic !== null)
      .map((item) => item.systolic)
    const validDiastolicValues = arrayBP
      .filter((item) => item.diastolic !== null)
      .map((item) => item.diastolic)

    if (validSystolicValues.length > 0) {
      avgSys = Math.round(avgSys / validSystolicValues.length)
      highSys = Math.max(...validSystolicValues)
      lowSys = Math.min(...validSystolicValues)
    } else {
      avgSys = ''
      highSys = ''
      lowSys = ''
    }

    if (validDiastolicValues.length > 0) {
      avgDia = Math.round(avgDia / validDiastolicValues.length)
      highDia = Math.max(...validDiastolicValues)
      lowDia = Math.min(...validDiastolicValues)
    } else {
      avgDia = ''
      highDia = ''
      lowDia = ''
    }
  } else {
    avgSys = ''
    avgDia = ''
    highSys = ''
    highDia = ''
    lowSys = ''
    lowDia = ''
  }

  return {
    arrayBP,
    bpmTableArray,
    avgSys,
    avgDia,
    highSys,
    highDia,
    lowSys,
    lowDia,
    lastReading,
    firstReading,
  }
}
