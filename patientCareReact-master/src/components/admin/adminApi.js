export const getClinic = async (id) => {
  try {
    const response = await fetch(`/routes/users/clinics/${id}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error('Network response was not ok')
    }

    const data = await response.json()
    return data
  } catch (error) {
    console.error('Error fetching clinic:', error)
    throw error
  }
}

export const uploadClinicImage = async (clinicId, image) => {
  await fetch(`/routes/users/upload-clinic-image/${clinicId}`, {
    method: 'POST',
    body: image,
  })
}

export const updateClinic = async (clinic, adminId) => {
  try {
    const response = await fetch('/routes/users/saveClinicChanges', {
      method: 'POST',
      body: JSON.stringify({ ...clinic, adminId }),
      headers: { 'Content-Type': 'application/json' },
    })

    if (!response.ok) {
      throw new Error('Network response was not ok')
    }

    return response.json()
  } catch (error) {
    console.error('Error updating clinic:', error)
    throw error
  }
}
