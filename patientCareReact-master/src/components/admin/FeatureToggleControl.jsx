import {
  Box,
  Switch,
  FormControlLabel,
  RadioGroup,
  Radio,
  FormLabel,
  Collapse,
} from '@mui/material'

export function FeatureToggleControl({
  label = 'BP Buddy',
  value = 'inherit',
  onChange,
}) {
  const customized = value !== 'inherit'

  const handleCustomizeChange = (event, checked) => {
    if (!checked) {
      onChange('inherit')
    } else {
      onChange('enabled')
    }
  }

  return (
    <Box display="flex" flexDirection="column" gap={1}>
      <FormControlLabel
        control={
          <Switch
            checked={customized}
            onChange={handleCustomizeChange}
            name={`${label}-customize`}
          />
        }
        label={`Customize ${label}`}
      />

      <Collapse in={customized}>
        <Box pl={3}>
          <FormLabel component="legend">{label} override</FormLabel>
          <RadioGroup
            row
            value={value}
            onChange={(e) => onChange(e.target.value)}
          >
            <FormControlLabel
              value="enabled"
              control={<Radio />}
              label="Enabled"
            />
            <FormControlLabel
              value="disabled"
              control={<Radio />}
              label="Disabled"
            />
          </RadioGroup>
        </Box>
      </Collapse>
    </Box>
  )
}
