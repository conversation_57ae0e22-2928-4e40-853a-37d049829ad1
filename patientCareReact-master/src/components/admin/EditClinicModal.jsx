import React, { useEffect } from 'react'
import TextField from '@material-ui/core/TextField'
import { useStyles } from '../common/style'
import { Button, FormControlLabel, Switch } from '@material-ui/core'
import Modal from 'react-bootstrap/Modal'
import { getClinic, updateClinic, uploadClinicImage } from './adminApi'
import { FeatureToggleControl } from './FeatureToggleControl'
import { Container } from 'react-bootstrap'

export default function EditClinicModal({
  clinicId,
  adminId,
  show,
  onClose,
  deleteClinic,
  onSave,
}) {
  const classes = useStyles()

  const [clinic, setClinic] = React.useState(null)
  const [clinicLogo, setClinicLogo] = React.useState(null)
  const [bpBuddy, setBpBuddy] = React.useState('disabled')
  const [error, setError] = React.useState(null)
  const [bpMetrics, setBpMetrics] = React.useState('disabled')

  useEffect(() => {
    const fetchClinic = async () => {
      try {
        const clinic = await getClinic(clinicId)
        setClinic({
          id: clinicId,
          name: clinic.name,
          address: clinic.address,
          phoneNumber: clinic.phoneNumber,
          mainContact: clinic.mainContact,
        })
        setBpBuddy(clinic.features?.BPBuddy?.value || 'disabled')
        setBpMetrics(clinic.features?.BPMetrics?.value || 'disabled')
      } catch (error) {
        console.log(error)
        setError('There was an error loading the clinic data.')
      }
    }
    show === true && fetchClinic()
  }, [clinicId, show])

  const updateClinicField = (field, value) => {
    setClinic((prevClinic) => ({
      ...prevClinic,
      [field]: value,
    }))
  }

  const handleImageChange = (event) => {
    const imageFile = event.target.files[0]
    setClinicLogo(imageFile)
  }

  const save = () => {
    const saveClinicData = async () => {
      setError(null)
      try {
        await updateClinic(
          {
            ...clinic,
            features: {
              BPBuddy: { value: bpBuddy },
              BPMetrics: { value: bpMetrics },
            },
          },
          adminId
        )
        if (clinicLogo) {
          await uploadClinicImage(clinicId, clinicLogo)
        }
        onSave()
      } catch (error) {
        console.log(error)
        setError('There has been an error. Please try again.')
      }
    }
    saveClinicData()
  }

  if (!show) {
    return null
  }

  if (!clinic) {
    return <div>Loading...</div>
  }

  return (
    <Modal
      style={{ marginTop: '50px', marginLeft: '75px', width: '75%' }}
      show={show}
      onHide={onClose}
    >
      <Modal.Header closeButton>
        <Modal.Title>Edit Clinic</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <form className={classes.form} noValidate>
          <TextField
            variant="outlined"
            margin="normal"
            required
            style={{ marginLeft: '15px', width: '40%' }}
            id="name"
            label="Practice Name"
            name="name"
            autoFocus
            value={clinic.name}
            onChange={(event) => updateClinicField('name', event.target.value)}
          />
          <TextField
            variant="outlined"
            margin="normal"
            required
            style={{ marginLeft: '15px', width: '40%' }}
            name="address"
            label="Street Address"
            id="address"
            value={clinic.address}
            onChange={(event) =>
              updateClinicField('address', event.target.value)
            }
          />
          <TextField
            variant="outlined"
            margin="normal"
            required
            style={{ marginLeft: '15px', width: '40%' }}
            name="number"
            label="Phone Number"
            id="number"
            value={clinic.phoneNumber}
            onChange={(event) =>
              updateClinicField('phoneNumber', event.target.value)
            }
          />
          <TextField
            variant="outlined"
            margin="normal"
            required
            style={{ marginLeft: '15px', width: '40%' }}
            name="contact"
            label="Main Contact"
            id="contact"
            value={clinic.mainContact}
            onChange={(event) =>
              updateClinicField('mainContact', event.target.value)
            }
          />
          <div style={{ height: '20px' }}></div>
          <Container sx={{ ml: '15px' }}>
            <FeatureToggleControl onChange={setBpBuddy} value={bpBuddy} label="BP Buddy" />
          </Container>
          <Container sx={{ ml: '15px' }}>
            <FeatureToggleControl onChange={setBpMetrics} value={bpMetrics} label="BP Metrics" />
          </Container>
          <Container sx={{ ml: '15px' }}>
            <p>Add Logo:</p>
            <input type="file" accept="image/*" onChange={handleImageChange} />
          </Container>
          <Container sx={{ ml: '15px' }}>{error && <p>{error}</p>}</Container>
        </form>
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={onClose}>
          Close
        </Button>
        <Button onClick={deleteClinic}>Delete</Button>
        <Button variant="primary" onClick={save}>
          Save Changes
        </Button>
      </Modal.Footer>
    </Modal>
  )
}
