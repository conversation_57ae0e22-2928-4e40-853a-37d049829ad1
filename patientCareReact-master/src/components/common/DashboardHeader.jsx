import React, { useState, useEffect } from 'react'
import Typography from '@material-ui/core/Typography'
import { Box } from '@mui/material'
import { AppBar } from './MiniDrawer'
import cardiowell2 from '../../images/cardiowell-white-Logo.svg'
import cardiowellMobile from '../../images/cardiowell-mobile-logo.svg'

const shouldUseMobileLogo = (
  isMobile,
  extraSmallMobile,
  isTablet,
  isLargeMobile,
  isTinyMobile,
  isMicroMobile,
  open
) => {
  if (isMicroMobile) {
    return true
  }
  if (isTinyMobile) {
    return true
  }
  if (extraSmallMobile && open) {
    return true
  }
  if (isTablet) {
    return false
  }
  if (isLargeMobile) {
    return false
  }
  return false
}

const getLogoWidth = (
  isMobile,
  smallMobile,
  extraSmallMobile,
  isTablet,
  isLargeMobile,
  isTinyMobile,
  isMicroMobile,
  open
) => {
  if (
    shouldUseMobileLogo(
      isMobile,
      extraSmallMobile,
      isTablet,
      isLargeMobile,
      isTinyMobile,
      isMicroMobile,
      open
    )
  ) {
    return isTinyMobile || extraSmallMobile || isMicroMobile ? '24px' : '28px'
  }
  if (isTablet && open) {
    return '100px'
  }
  if (isLargeMobile && open) {
    return '100px'
  }
  if (isMobile) {
    return smallMobile ? '80px' : '100px'
  }
  return '125px'
}

const getLogoHeight = (
  isMobile,
  smallMobile,
  extraSmallMobile,
  isTablet,
  isLargeMobile,
  isTinyMobile,
  isMicroMobile,
  open,
  hasCustomImage
) => {
  if (
    shouldUseMobileLogo(
      isMobile,
      extraSmallMobile,
      isTablet,
      isLargeMobile,
      isTinyMobile,
      isMicroMobile,
      open
    )
  ) {
    return isTinyMobile || extraSmallMobile || isMicroMobile ? '20px' : '24px'
  }
  if (isTablet && open) {
    return hasCustomImage ? '24px' : '22px'
  }
  if (isLargeMobile && open) {
    return hasCustomImage ? '24px' : '22px'
  }
  if (isMobile) {
    if (smallMobile) {
      return hasCustomImage ? '20px' : '18px'
    }
    return hasCustomImage ? '24px' : '22px'
  }
  return hasCustomImage ? '30px' : '25px'
}

const shouldShowHeaderText = (
  isMobile,
  extraSmallMobile,
  isTablet,
  isLargeMobile,
  isTinyMobile,
  isMicroMobile,
  open
) => {
  if (!isMobile) {
    return true
  }
  if (isMicroMobile) {
    return true
  }
  if (isTinyMobile) {
    return !open
  }
  if (extraSmallMobile) {
    return !open
  }
  if (isTablet) {
    return !open
  }
  if (isLargeMobile) {
    return !open
  }
  return !open
}

const getLogoSource = (
  isMobile,
  extraSmallMobile,
  isTablet,
  isLargeMobile,
  isTinyMobile,
  isMicroMobile,
  open,
  imageData
) => {
  if (
    shouldUseMobileLogo(
      isMobile,
      extraSmallMobile,
      isTablet,
      isLargeMobile,
      isTinyMobile,
      isMicroMobile,
      open
    )
  ) {
    return cardiowellMobile
  }
  return imageData || cardiowell2
}

export const DashboardHeader = ({
  open,
  handleDrawerOpen,
  clinic,
  classes,
}) => {
  const [imageData, setImageData] = useState('')

  const [windowWidth, setWindowWidth] = useState(
    typeof window !== 'undefined' ? window.innerWidth : 1200
  )
  const isActuallyMobile = windowWidth < 900
  const isActuallySmallMobile = windowWidth < 600
  const isActuallyLargeMobile = windowWidth >= 768 && windowWidth < 900
  const isActuallyTablet = windowWidth >= 425 && windowWidth < 768
  const isActuallyExtraSmallMobile = windowWidth < 425
  const isActuallyTinyMobile = windowWidth >= 320 && windowWidth <= 375
  const isActuallyMicroMobile = windowWidth < 320

  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth)
    }
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  useEffect(() => {
    fetch(`/routes/users/clinic-images/${clinic}`)
      .then((response) => response.json())
      .then((data) => {
        if (data.image) {
          const imageBase64 = data.image
          const imageUrl = `data:image/jpeg;base64,${imageBase64}`
          setImageData(imageUrl)
        }
      })
      .catch((error) => {
        console.error('Error fetching clinic image:', error)
      })
  }, [clinic])

  return (
    <AppBar open={open} handleDrawerOpen={handleDrawerOpen}>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: shouldShowHeaderText(
            isActuallyMobile,
            isActuallyExtraSmallMobile,
            isActuallyTablet,
            isActuallyLargeMobile,
            isActuallyTinyMobile,
            isActuallyMicroMobile,
            open
          )
            ? 'space-between'
            : 'center',
          width: '100%',
          padding: { sm: '0 16px', md: '0 24px' },
          height: '100%',
        }}
      >
        {shouldShowHeaderText(
          isActuallyMobile,
          isActuallyExtraSmallMobile,
          isActuallyTablet,
          isActuallyLargeMobile,
          isActuallyTinyMobile,
          isActuallyMicroMobile,
          open
        ) && (
          <Typography
            component="h1"
            variant="h6"
            color="inherit"
            noWrap
            className={classes.title}
            sx={{
              fontSize: { xs: '14px', sm: '16px', md: '1.25rem' },
              fontWeight: { xs: 500, md: 400 },
              maxWidth: { xs: '75%', sm: '80%', md: 'none' },
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            }}
          >
            {isActuallyMicroMobile
              ? ''
              : isActuallyTinyMobile || isActuallyExtraSmallMobile
                ? 'Patient Dashboard'
                : 'Patient Care Dashboard'}
          </Typography>
        )}
        <img
          style={{
            width: getLogoWidth(
              isActuallyMobile,
              isActuallySmallMobile,
              isActuallyExtraSmallMobile,
              isActuallyTablet,
              isActuallyLargeMobile,
              isActuallyTinyMobile,
              isActuallyMicroMobile,
              open
            ),
            height: getLogoHeight(
              isActuallyMobile,
              isActuallySmallMobile,
              isActuallyExtraSmallMobile,
              isActuallyTablet,
              isActuallyLargeMobile,
              isActuallyTinyMobile,
              isActuallyMicroMobile,
              open,
              !!imageData
            ),
            objectFit: 'contain',
          }}
          src={getLogoSource(
            isActuallyMobile,
            isActuallyExtraSmallMobile,
            isActuallyTablet,
            isActuallyLargeMobile,
            isActuallyTinyMobile,
            isActuallyMicroMobile,
            open,
            imageData
          )}
          alt={
            shouldUseMobileLogo(
              isActuallyMobile,
              isActuallyExtraSmallMobile,
              isActuallyTablet,
              isActuallyLargeMobile,
              isActuallyTinyMobile,
              isActuallyMicroMobile,
              open
            )
              ? 'Cardiowell'
              : imageData
                ? 'Clinic'
                : 'Cardiowell'
          }
        />
      </Box>
    </AppBar>
  )
}
