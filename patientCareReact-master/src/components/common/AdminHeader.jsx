import React, { useState, useEffect } from 'react'
import Typography from '@material-ui/core/Typography'
import { Box } from '@mui/material'
import { AppBar } from './MiniDrawer'
import cardiowell2 from '../../images/cardiowell2.png'
import cardiowellMobile from '../../images/cardiowell-mobile-logo.svg'

const shouldUseMobileLogo = (
  isMobile,
  isTablet,
  isLargeMobile,
  isTinyMobile,
  open
) => {
  if (isTinyMobile && open) {
    return true
  }
  return false
}

const getLogoWidth = (
  isMobile,
  isTablet,
  isLargeMobile,
  isTinyMobile,
  open
) => {
  if (
    shouldUseMobileLogo(isMobile, isTablet, isLargeMobile, isTinyMobile, open)
  ) {
    return '24px'
  }
  if (isMobile) {
    return open ? '100px' : '125px'
  }
  return '125px'
}

const getLogoHeight = (
  isMobile,
  isTablet,
  isLargeMobile,
  isTinyMobile,
  open
) => {
  if (
    shouldUseMobileLogo(isMobile, isTablet, isLargeMobile, isTinyMobile, open)
  ) {
    return '20px'
  }
  return 'auto'
}

const shouldShowHeaderText = (isMobile, isTinyMobile, open) => {
  if (!isMobile) {
    return true
  }
  if (isTinyMobile) {
    return true
  }
  return !open
}

const getHeaderText = (isTinyMobile) => {
  if (isTinyMobile) {
    return ''
  }
  return 'Admin Dashboard'
}

const getLogoSource = (
  isMobile,
  isTablet,
  isLargeMobile,
  isTinyMobile,
  open
) => {
  if (
    shouldUseMobileLogo(isMobile, isTablet, isLargeMobile, isTinyMobile, open)
  ) {
    return cardiowellMobile
  }
  return cardiowell2
}

export const AdminHeader = ({ open, handleDrawerOpen, classes }) => {
  const [windowWidth, setWindowWidth] = useState(
    typeof window !== 'undefined' ? window.innerWidth : 1200
  )
  const isActuallyMobile = windowWidth < 900
  const isActuallyLargeMobile = windowWidth >= 768 && windowWidth < 900
  const isActuallyTablet = windowWidth >= 425 && windowWidth < 768
  const isActuallyTinyMobile = windowWidth < 425

  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth)
    }
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  return (
    <AppBar open={open} handleDrawerOpen={handleDrawerOpen}>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: shouldShowHeaderText(
            isActuallyMobile,
            isActuallyTinyMobile,
            open
          )
            ? 'space-between'
            : 'center',
          width: '100%',
          padding: { sm: '0 16px', md: '0 24px' },
          height: '100%',
        }}
      >
        {shouldShowHeaderText(isActuallyMobile, isActuallyTinyMobile, open) && (
          <Typography
            component="h1"
            variant="h6"
            color="inherit"
            noWrap
            className={classes.title}
            sx={{
              fontSize: { xs: '14px', sm: '16px', md: '1.25rem' },
              fontWeight: { xs: 500, md: 400 },
              maxWidth: { xs: '75%', sm: '80%', md: 'none' },
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            }}
          >
            {getHeaderText(isActuallyTinyMobile)}
          </Typography>
        )}
        <img
          style={{
            width: getLogoWidth(
              isActuallyMobile,
              isActuallyTablet,
              isActuallyLargeMobile,
              isActuallyTinyMobile,
              open
            ),
            height: getLogoHeight(
              isActuallyMobile,
              isActuallyTablet,
              isActuallyLargeMobile,
              isActuallyTinyMobile,
              open
            ),
            objectFit: 'contain',
          }}
          src={getLogoSource(
            isActuallyMobile,
            isActuallyTablet,
            isActuallyLargeMobile,
            isActuallyTinyMobile,
            open
          )}
          alt={
            shouldUseMobileLogo(
              isActuallyMobile,
              isActuallyTablet,
              isActuallyLargeMobile,
              isActuallyTinyMobile,
              open
            )
              ? 'Cardiowell'
              : 'Cardiowell Admin'
          }
        />
      </Box>
    </AppBar>
  )
}
