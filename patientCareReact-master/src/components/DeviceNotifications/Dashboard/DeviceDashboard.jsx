import { useEffect, useState } from 'react'
import {
  Box,
  CssBaseline,
  AppBar,
  Typography,
  Toolbar,
  IconButton,
  Avatar,
} from '@material-ui/core'
import { Alert } from '@mui/material'
import { useTheme } from '@mui/material/styles'
import { Copyright } from '../../common/Copyright'
import { getDeviceMeasures } from '../getDeviceMeasures'
import { useStyles } from '../../common/style'
import cardiowell2 from '../../../images/main-logo-white.svg'
import { DeviceReadingsDisplay } from '../DeviceReadingsDisplay'
import { EditPatientForm } from '../../../patient/EditPatientForm'
import { history } from '../../../App'
import { Loader } from '../../common/Loader/Loader'
import { WithChatLayout } from '../../chat/WithChatLayout'

const EditPencilIcon = () => (
  <div
    style={{
      width: '20px',
      height: '20px',
      backgroundColor: 'white',
      borderRadius: '10px',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      padding: '2px',
    }}
  >
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
    >
      <path
        d="M1.99902 11.5009V14.0009H4.49902L11.8724 6.6276L9.37236 4.1276L1.99902 11.5009ZM13.8057 4.69427C14.0657 4.43427 14.0657 4.01427 13.8057 3.75427L12.2457 2.19427C11.9857 1.93427 11.5657 1.93427 11.3057 2.19427L10.0857 3.41427L12.5857 5.91427L13.8057 4.69427Z"
        fill="#E65100"
      />
    </svg>
  </div>
)

const ErrorDisplay = ({ message }) => {
  const classes = useStyles()
  return (
    <Box className={classes.centered}>
      <Typography>{message}</Typography>
    </Box>
  )
}

const LoadingDisplay = () => {
  const classes = useStyles()
  return (
    <Box className={classes.centered}>
      <Loader size={80} />
    </Box>
  )
}

export const DeviceDashboard = (props) => {
  const [display, setDisplay] = useState('loading')
  const [deviceData, setDeviceData] = useState([])
  const [serverResponse, setServerResponse] = useState('')
  const [showProfileAlert, setShowProfileAlert] = useState(false)
  const [hasSaveError, setHasSaveError] = useState(false)
  const [isFormCompleted, setIsFormCompleted] = useState(false)
  const [isFormSkipped, setIsFormSkipped] = useState(false)
  const [formData, setFormData] = useState(null)
  const [uploadedImageUrl, setUploadedImageUrl] = useState(null)

  const imei = props.match.params.imei

  const classes = useStyles()
  const theme = useTheme()

  const profileRoute = () => {
    history.push(`/device-updates/dashboard/${imei}/profile`)
  }

  const isProfileComplete = (data) => {
    if (isFormSkipped) {
      return false
    }

    const mergedData = formData ? { ...data, ...formData } : data

    const fieldsToCheck = [
      { field: 'birthdate' },
      { field: 'gender' },
      { field: 'ethnicity' },
      { field: 'zip', alternate: 'patientZip' },
      { field: 'email', alternate: 'patientEmail' },
      { field: 'weight' },
      { field: 'height' },
      { field: 'chronicConditions' },
      { field: 'hypertensionMedications' },
      { field: 'allergies' },
    ]

    return fieldsToCheck.every(({ field, alternate }) => {
      const value =
        mergedData[field] !== undefined
          ? mergedData[field]
          : alternate
            ? mergedData[alternate]
            : undefined

      if (!value) {
        return false
      }

      if (Array.isArray(value)) {
        return value.length > 0
      }

      return value.toString().trim() !== ''
    })
  }

  useEffect(() => {
    // Check localStorage for avatar
    const savedAvatar = localStorage.getItem(`avatar_${imei}`)
    if (savedAvatar) {
      setUploadedImageUrl(savedAvatar)
    }
  }, [imei])

  useEffect(() => {
    getDeviceMeasures(imei).then((response) => {
      if (response.error) {
        setDisplay('error')
        setServerResponse(
          response.message || 'There is an error with this IMEI number'
        )
      } else {
        setDeviceData(response)
        const mergedData = formData ? { ...response, ...formData } : response
        const profileComplete = isProfileComplete(mergedData)

        if (profileComplete) {
          setIsFormCompleted(true)
          setShowProfileAlert(false)
          setDisplay('data')
        } else if (!isFormCompleted) {
          setDisplay('patient')
        } else {
          setDisplay('data')
          setShowProfileAlert(false)
        }
      }
    })
  }, [imei, isFormCompleted, formData])

  const handleFormSubmitOrSkip = (isSkipped = false, submittedData = null) => {
    if (!isSkipped && submittedData) {
      setFormData(submittedData)
    }

    if (isSkipped) {
      setIsFormCompleted(true)
      setIsFormSkipped(true)
      setShowProfileAlert(false)
      setHasSaveError(false)
      setDisplay('data')
    } else {
      setDisplay('loading')
      setIsFormCompleted(true)
      setIsFormSkipped(false)

      getDeviceMeasures(imei).then((response) => {
        if (!response.error) {
          setDeviceData(response)
          const mergedData = submittedData
            ? { ...response, ...submittedData }
            : response
          const profileComplete = isProfileComplete(mergedData)
          setShowProfileAlert(false)
          setHasSaveError(false)
          setDisplay('data')
        } else {
          setShowProfileAlert(true)
          setHasSaveError(true)
          setDisplay('data')
        }
      })
    }
  }

  const handleAlertClose = () => {
    setShowProfileAlert(false)
    setHasSaveError(false)
  }

  return (
    <WithChatLayout>
      <main className={classes.content}>
        <CssBaseline />
        <AppBar position="absolute" className={classes.appBar}>
          <Toolbar
            className={classes.toolbar}
            style={{ display: 'flex', justifyContent: 'space-between' }}
          >
            <img
              style={{ maxHeight: '17px' }}
              src={cardiowell2}
              alt="main-logo"
            />
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                position: 'relative',
              }}
            >
              <div style={{ position: 'relative' }}>
                <Avatar
                  src={uploadedImageUrl}
                  alt="Profile"
                  style={{
                    width: '40px',
                    height: '40px',
                    cursor: 'pointer',
                  }}
                  onClick={() => profileRoute()}
                >
                  {!uploadedImageUrl && (
                    <svg
                      className="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium MuiAvatar-fallback css-10mi8st-MuiSvgIcon-root-MuiAvatar-fallback"
                      focusable="false"
                      aria-hidden="true"
                      viewBox="0 0 24 24"
                      data-testid="PersonIcon"
                    >
                      <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"></path>
                    </svg>
                  )}
                </Avatar>
                {showProfileAlert && (
                  <div
                    style={{
                      position: 'absolute',
                      top: -2,
                      right: -6,
                      cursor: 'pointer',
                    }}
                    onClick={() => profileRoute()}
                  >
                    <EditPencilIcon />
                  </div>
                )}
              </div>
              <Typography style={{ padding: '0 0 0 12px' }}>
                {deviceData?.firstName || deviceData?.lastName
                  ? `${deviceData?.firstName || ''} ${deviceData?.lastName || ''}`.trim()
                  : ''}
              </Typography>
            </div>
          </Toolbar>
        </AppBar>
        <div className={classes.appBarSpacer} />

        {showProfileAlert && hasSaveError && (
          <>
            <Box
              sx={{
                display: { xs: 'flex', md: 'none' },
                justifyContent: 'center',
                width: '100%',
              }}
            >
              <Alert
                severity="info"
                onClose={handleAlertClose}
                sx={{
                  position: 'relative',
                  width: 'calc(100% - 32px)',
                  maxWidth: '500px',
                  height: 'auto',
                  minHeight: '80px',
                  margin: '32px 16px 0',
                  padding: '16px',
                  backgroundColor: '#E8EAF6',
                  borderRadius: '8px',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'flex-start',
                  '& .MuiAlert-message': {
                    width: '100%',
                    padding: 0,
                  },
                  '& .MuiAlert-icon': {
                    display: 'none',
                  },
                  '& .MuiAlert-action': {
                    padding: 0,
                    marginRight: 0,
                  },
                  '& .MuiIconButton-root': {
                    padding: '0px !important',
                    position: 'absolute',
                    top: '16px',
                    right: '16px',
                    width: '24px',
                    height: '24px',
                    '& .MuiSvgIcon-root': {
                      width: '24px',
                      height: '24px',
                      padding: '0px !important',
                    },
                  },
                }}
              >
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '4px',
                    width: '100%',
                    maxWidth: '264px',
                  }}
                >
                  <Typography
                    variant="h6"
                    style={{
                      color: '#1A237E',
                      fontSize: '16px',
                      fontStyle: 'normal',
                      fontWeight: 500,
                      lineHeight: '24px',
                      letterSpacing: '0.15px',
                    }}
                  >
                    Missing profile details
                  </Typography>
                  <Typography
                    style={{
                      color: 'rgba(0, 0, 0, 0.6)',
                      fontSize: '14px',
                      lineHeight: '20px',
                    }}
                  >
                    Tap your name to complete profile
                  </Typography>
                </div>
              </Alert>
            </Box>

            <Box
              sx={{
                display: { xs: 'none', md: 'flex' },
                position: 'fixed',
                left: '50%',
                bottom: '40px',
                transform: 'translateX(-50%)',
                zIndex: 10,
              }}
            >
              <Box
                sx={{
                  width: '334px',
                  height: '84px',
                  backgroundColor: '#FDE7E7',
                  borderRadius: '4px',
                  display: 'flex',
                  alignItems: 'flex-start',
                  padding: '6px 16px',
                  boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.15)',
                }}
              >
                <Box sx={{ display: 'flex', width: '100%' }}>
                  <Box sx={{ marginRight: '12px', marginTop: '2px' }}>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="22"
                      height="22"
                      viewBox="0 0 22 22"
                      fill="none"
                    >
                      <path
                        d="M10.0835 13.7502H11.9168V15.5835H10.0835V13.7502ZM10.0835 6.41683H11.9168V11.9168H10.0835V6.41683ZM10.991 1.8335C5.931 1.8335 1.8335 5.94016 1.8335 11.0002C1.8335 16.0602 5.931 20.1668 10.991 20.1668C16.0602 20.1668 20.1668 16.0602 20.1668 11.0002C20.1668 5.94016 16.0602 1.8335 10.991 1.8335ZM11.0002 18.3335C6.9485 18.3335 3.66683 15.0518 3.66683 11.0002C3.66683 6.9485 6.9485 3.66683 11.0002 3.66683C15.0518 3.66683 18.3335 6.9485 18.3335 11.0002C18.3335 15.0518 15.0518 18.3335 11.0002 18.3335Z"
                        fill="#D32F2F"
                      />
                    </svg>
                  </Box>
                  <Box sx={{ flex: 1 }}>
                    <Typography
                      style={{
                        fontSize: '16px',
                        fontWeight: 500,
                        lineHeight: '24px',
                        color: '#5F2120',
                        marginBottom: '4px',
                      }}
                    >
                      Your data has not been saved
                    </Typography>
                    <Typography
                      style={{
                        fontSize: '14px',
                        fontWeight: 400,
                        lineHeight: '20px',
                        color: '#5F2120',
                      }}
                    >
                      Try filling in the fields later in your personal account.
                    </Typography>
                  </Box>
                  <Box sx={{ marginLeft: '8px', marginTop: '2px' }}>
                    <IconButton
                      onClick={handleAlertClose}
                      style={{ padding: '0' }}
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="20"
                        height="20"
                        viewBox="0 0 20 20"
                        fill="none"
                      >
                        <path
                          d="M15.8332 5.3415L14.6582 4.1665L9.99984 8.82484L5.3415 4.1665L4.1665 5.3415L8.82484 9.99984L4.1665 14.6582L5.3415 15.8332L9.99984 11.1748L14.6582 15.8332L15.8332 14.6582L11.1748 9.99984L15.8332 5.3415Z"
                          fill="#5F2120"
                        />
                      </svg>
                    </IconButton>
                  </Box>
                </Box>
              </Box>
            </Box>
          </>
        )}

        {display === 'loading' && <LoadingDisplay />}
        {display === 'data' && (
          <DeviceReadingsDisplay
            imei={imei}
            deviceData={deviceData}
            showAlert={showProfileAlert}
          />
        )}
        {display === 'patient' && (
          <EditPatientForm
            patient={deviceData}
            editPatients={() => {}}
            updatePatients={(updatedPatient) => {
              setDeviceData({ ...deviceData, ...updatedPatient })
            }}
            editDevices={false}
            deviceImei={imei}
            onComplete={(isSkipped, submittedData) =>
              handleFormSubmitOrSkip(isSkipped, submittedData)
            }
          />
        )}
        {display === 'error' && <ErrorDisplay message={serverResponse} />}
      </main>
    </WithChatLayout>
  )
}
