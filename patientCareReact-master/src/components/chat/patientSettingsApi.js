export const getPatientSettings = async () => {
  try {
    const response = await fetch(`/routes/patient/settings`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error('Network response was not ok')
    }

    const data = await response.json()
    return data
  } catch (error) {
    console.error('Error fetching patient settings:', error)
    throw error
  }
}
